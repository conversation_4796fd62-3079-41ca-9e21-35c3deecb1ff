files:
  "/opt/elasticbeanstalk/support/conf/pumaconf.rb":
     owner: root
     group: root
     mode: "000644"
     content: |
      threads_count = ENV.fetch("RAILS_MAX_THREADS") { 5 }
      directory '/var/app/current'
      threads threads_count, threads_count * 2
      workers %x(grep -c processor /proc/cpuinfo)
      bind 'unix:///var/run/puma/my_app.sock'
      pidfile '/var/run/puma/puma.pid'
      stdout_redirect '/var/log/puma/puma.log', '/var/log/puma/puma.log', true
      daemonize false
