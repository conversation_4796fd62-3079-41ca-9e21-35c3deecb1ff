Rails.application.routes.draw do
  root to: "home#index"
  get "/admin", to: "home#index"

  health_check_routes

  namespace :system do
    root to: "roles#index"
    resources :roles
    resources :user_roles, only: %i[index edit update]
    namespace :push_notification do
      resources :configurations do
        resources :logs, only: :index
        put "/toggle", action: "toggle_configuration"
      end
      resources :logs, only: :none do
        resources :recipients, only: :index
      end
      scope "devtools", controller: "devtools" do
        post "send_notification"
      end
    end
  end

  devise_for :users, controllers: {
    # Override the following Devise controllers with our custom versions
    registrations: "users/registrations",
    omniauth_callbacks: "users/callbacks"
  }

  resources :news, only: %i[index show] do
    member do
      post :comments, to: "news#store_comment"
    end
  end

  # Feedback
  scope :feedbacks do
    post "/submit", to: "feedbacks#submit", as: :feedback_submit
    post "/ignore", to: "feedbacks#ignore", as: :feedback_ignore
  end

  namespace :snd do
    # SnD merit and strike summary
    get "/merit_strikes/", to: "merit_strikes#index", as: :merit_strikes
    get "/merit_strikes/merit_logs", to: "merit_strikes#merit_logs", as: :merit_logs
    get "/merit_strikes/strike_logs", to: "merit_strikes#strike_logs", as: :strike_logs

    get :faqs, to: "faqs#index"

    # My Profile
    get "/my_profiles/edit", to: "my_profiles#edit", as: :edit_my_profile
    put "/my_profiles", to: "my_profiles#update", as: :my_profile_update

    # Merit redeem
    resources :merit_redemptions, only: %i[index new create show]

    # User Payslip
    get "payslips", to: "user_payslips#index", as: :payslips
    get "payslips/my_payslips/:id", to: "user_payslips#show", as: :my_payslips
    get "payslips/my_payslips/:id/export", to: "user_payslips#export_payslip", as: :export_payslip

    # Settlements
    get "show_my_order/:id", to: "settlement_logs#show_my_order", as: :show_my_order
    get "my_orders", to: "settlement_logs#my_orders", as: :my_orders
    resources :settlement_logs, only: %i[index show new destroy] do
      collection do
        get "my_settlements", to: "settlement_logs#my_settlements", as: :my_settlements
        post "/create", to: "settlement_logs#create", as: :create_settlement_log
      end
      member do
        post :receive, to: "settlement_logs#receive"
        post :not_receive, to: "settlement_logs#not_receive"
      end
    end

    # SnD Strike Appeal
    get "/user_strikes/:user_strike_id/appeals", to: "strike_appeals#index_by_user_strike", as: :strike_appeals
    post "/user_strikes/:user_strike_id/appeals", to: "strike_appeals#create"
    get "/user_strikes/:user_strike_id/appeals/new", to: "strike_appeals#new", as: :new_strike_appeal
    get "/user_strikes/:user_strike_id/appeals/:appeal_id", to: "strike_appeals#show", as: :strike_appeal
    get "/strike_appeals", to: "strike_appeals#index_by_user", as: :strike_appeals_by_user

    # Inventory
    get "/inventory", to: "inventories#show_items", as: :inventory_items
    get "/cards", to: "inventories#show_cards", as: :inventory_cards
    get "/card/:id", to: "inventories#show_card_details", as: :inventory_card

    # Fleet's Performance
    get "/performances", to: "performances#index", as: :performances

    # Payroll
    get "/payrolls", to: "payrolls#index", as: :payrolls

    # set locales
    get "/locale/:lang_code", to: "locales#set_locale_cookie", as: :set_locale_cookie

    get "/onboarding", to: "onboardings#index", as: :onboarding_index
    post "/onboarding", to: "onboardings#create", as: :onboarding

    get "/particular_form", to: "particular_form#index", as: :particular_forms
    post "/particular_form", to: "particular_form#create", as: :new_particular_form

    # SnD Online Training
    scope :training do
      get "", to: "trainings#index", as: :training_index
      get "dashboard", to: "training_dashboard#index", as: :training_dashboard
      get "progress", to: "training_progress#index", as: :training_progress
      get ":type_slug", to: "trainings#show_modules", as: :training_modules
    end
    resources :trainings, only: :none do
      get "search", on: :collection
    end
    scope :training_modules do
      get ":module_slug/sequence/:seq", to: "trainings#show_submodule_content", as: :training_submodules
      get ":module_slug/quiz", to: "trainings#show_quiz", as: :training_module_quiz
      get ":module_slug/summary", to: "trainings#show_summary", as: :training_module_summary
      post ":module_slug/start_quiz", to: "trainings#start_quiz", as: :training_module_start_quiz
      post ":module_slug/request_appeal", to: "trainings#request_appeal", as: :training_module_request_appeal
      get ":module_slug/submodules_navigation", to: "trainings#show_submodules_navigation", as: :training_submodules_navigation
    end
    scope :quiz_sessions do
      get ":quiz_session_id/question_sequence/:question_seq", to: "trainings#show_question", as: :training_quiz_question
      post ":quiz_session_id/question_sequence/:question_seq", to: "trainings#create_answer", as: :training_quiz_answer
      get ":quiz_session_id/result", to: "trainings#show_quiz_result", as: :training_quiz_result
    end

    # Order History
    scope :order_histories do
      get "", to: "order_histories#index", as: :order_history_index
      get ":order_number", to: "order_histories#show", as: :order_history
    end

    # Credit Cards
    scope :credit_cards do
      get "", to: "credit_cards#index", as: :credit_cards
      get ":id", to: "credit_cards#show", as: :credit_card
    end
  end

  namespace :admin do
    resources :fms_roles
    resources :users, except: %i[destroy] do
      member do
        get "/user_profile", to: "users#user_my_profile", as: :user_profile
      end
      collection do
        get "/export", to: "users#export_users", as: :export_users
      end
    end
    resources :news
    resources :letters
    resources :policies
    resources :merit_rewards
    # inventories export as csv
    get "/inventories/export", to: "inventories#export"
    resources :inventories, except: %i[show]
    resources :faq_categories do
      resources :faqs
    end
    resources :merits
    get "redash_schedules/search", to: "redash_schedules#search", as: :redash_search
    get "redash_schedules/get_redash_column_names/:redash_schedule_id" => "redash_schedules#get_redash_column_names", as: :get_redash_column_names
    resources :redash_schedules
    resources :redash_batches, only: %i[index]
    get "redash_jobs", to: "redash_batches#jobs", as: :redash_jobs

    # Settlements
    resources :settlement_logs, only: %i[index show] do
      collection do
        get :new_uncollected, to: "settlement_logs#new_uncollected", as: :new_uncollected
        get "upload", to: "shopping_cash#upload", as: :upload_shopping_cash
        get "upload/download_format_file", to: "shopping_cash#download_format_file", as: :download_format_file
        get "shoppings_cash", to: "shopping_cash#index", as: :index_shopping_cash
        post "shopping_cash", to: "shopping_cash#create"

        get "settlement_adjustments", to: "settlement_adjustments#index", as: :settlement_adjustments
        get "settlement_adjustments/upload", to: "settlement_adjustments#upload", as: :upload_settlement_adjustment
        get "settlement_adjustments/download_format_file", to: "settlement_adjustments#download_template_file", as: :settlement_adjustment_template_file
        post "settlement_adjustments", to: "settlement_adjustments#create", as: :create_settlement_adjustment
        get "settlement_adjustment_logs", to: "settlement_adjustments#log", as: :settlement_adjustment_log

        post :create_uncollected, to: "settlement_logs#create_uncollected", as: :create_uncollected
      end

      member do
        delete "shopping_cash", to: "shopping_cash#destroy", as: :cancel_shopping_cash
        post :approve, to: "settlement_logs#approve", as: :approve_settlement
        post :reject, to: "settlement_logs#reject", as: :reject_settlement
        delete :cancel_uncollected, to: "settlement_logs#cancel_uncollected", as: :cancel_uncollected
        post :retry, to: "settlement_adjustments#retry", as: :retry_settlement_adjustment
      end
    end

    get "/user_settlements/", to: "user_settlements#index", as: :user_settlements
    get "export_settlements_summary", to: "user_settlements#export"

    resources :strikes
    resources :strike_appeals, only: %i[index show] do
      member do
        post :approve, to: "strike_appeals#approve"
        post :reject, to: "strike_appeals#reject"
      end
    end
    resources :cards, except: [:show, :destroy] do
      collection do
        get "export", to: "cards#export"
      end
    end

    # Credit Cards
    resources :credit_cards, only: [:index, :show]

    resources :user_inventories, only: [:index, :show] do
      collection do
        get "export", to: "user_inventories#export"
      end
    end

    resources :kpis
    resources :performance_schemes
    get "/user_kpis", to: "user_kpis#index", as: :user_kpis
    get "/user_kpis/user/:user_id", to: "user_kpis#show", as: :user_kpi

    # Inventory assignment logs
    get "/user_inventory_logs", to: "inventory_life_cycles#index", as: :user_inventory_logs
    # User inventory assignment
    get "/user_inventories/:user_id/assign", to: "inventory_assignments#new", as: :new_user_inventory_assignment
    post "/user_inventories/:user_id/assign", to: "inventory_assignments#create", as: :user_inventory_assignment
    # Userinventory un-assignment
    post "/user_inventories/:user_id/unassign_inventory/:inventory_id",
      to: "inventory_unassignments#create",
      as: :user_inventory_unassignment
    # User card assignment
    get "/user_inventories/:user_id/assign_card", to: "card_assignments#new", as: :new_user_card_assignment
    post "/user_inventories/:user_id/assign_card", to: "card_assignments#create", as: :user_card_assignment
    # User card un-assignment
    post "/user_inventories/:user_id/unassign_card/:card_id",
      to: "card_unassignments#create",
      as: :user_card_unassignment

    # Merit/Strike Summary Index
    get "/user_merit_strikes/", to: "user_merit_strikes#index", as: :user_merit_strikes
    get "/user_merit_strikes/export", to: "user_merit_strikes#export", as: :user_merit_strikes_export
    # Strike Point Addition
    get "/user_strikes/:user_id/new", to: "user_strikes#new", as: :new_user_strike
    post "/user_strikes/:user_id", to: "user_strikes#create", as: :user_strikes
    # Strike Point Log
    get "/user_strikes/:user_id/log", to: "user_strikes#show_log", as: :user_strike_logs
    post "/user_strikes/:user_id/bulk_delete", to: "user_strikes#bulk_delete", as: :user_strikes_bulk_delete
    # Delete Strike
    delete "/user_strikes/:id", to: "user_strikes#destroy", as: :delete_user_strike
    # Merit Point Addition
    get "/user_merits/:user_id/new", to: "user_merits#new", as: :new_user_merit
    post "/user_merits/:user_id", to: "user_merits#create", as: :user_merits
    # Merit Point Log
    get "/user_merits/:user_id/log", to: "user_merits#show_log", as: :user_merit_logs

    # Manage merit redemptions
    get "user_merit_redemptions", to: "user_merit_redemptions#index", as: :user_merit_redemptions
    get "user_merit_redemptions/:id", to: "user_merit_redemptions#show", as: :user_merit_redemption
    post "user_merit_redemptions/:id/approve", to: "user_merit_redemptions#approve", as: :approve_user_merit_redemption
    post "user_merit_redemptions/:id/confirm", to: "user_merit_redemptions#confirm", as: :confirm_user_merit_redemption
    post "user_merit_redemptions/:id/reject", to: "user_merit_redemptions#reject", as: :reject_user_merit_redemption

    # (Admin onboarding is disabled)
    # get "/onboarding", to: "onboardings#index", as: :onboarding_index
    # post "/onboarding", to: "onboardings#create", as: :onboarding

    # form data
    resources :particular_forms, only: %i[index show destroy]
    scope :particular_form do
      get "form_data", to: "form_data#index", as: :form_data
      resources :referral_channels, except: %i[index show]
      resources :banks, except: %i[index show]
    end

    # Payroll
    resources :payroll_schemes
    resources :payroll_adjustments
    resources :payroll_after_grosses
    get "payslips/new", to: "payslips#generate", as: :new_payslips
    post "payslips/create", to: "payslips#create_payslips", as: :create_payslips
    get "payslips/jobs", to: "payslips#jobs", as: :payslip_jobs
    get "payslips/:id/export", to: "payslips#export_payslip", as: :export_payslip
    get "payslips/batch/:batch_id", to: "payslips#payslip_batch", as: :payslip_batch
    delete "payslips/batch/:batch_id", to: "payslips#delete_payslip_batch", as: :delete_payslip_batch
    post "payslips/batch/:batch_id/publish", to: "payslips#publish_payslip_batch", as: :publish_payslip_batch
    get "payslips/get_payroll_scheme_period/:payroll_scheme_id" => "payslips#get_payroll_scheme_period", as: :get_payroll_scheme_period
    resources :payslips, only: %i[index show]
    resources :ops_facts, only: %i[index create] do
      collection do
        get "upload", to: "ops_facts#upload"
      end
    end
    scope "payroll_facts" do
      get "", to: "payroll_facts#index", as: :payroll_facts
      get "columns", to: "payroll_facts#columns_index", as: :payroll_facts_columns
      get "upload", to: "payroll_facts#upload_index", as: :payroll_facts_upload_index
      post "", to: "payroll_facts#create", as: :create_payroll_fact
      put ":id", to: "payroll_facts#update", as: :update_payroll_fact
      delete ":id", to: "payroll_facts#destroy", as: :delete_payroll_fact
      post "generate_template", to: "payroll_facts#generate_template", as: :payroll_facts_generate_template
      post "upload", to: "payroll_facts#upload", as: :payroll_facts_upload
      get "download_csv/:id", to: "payroll_facts#download_csv", as: :payroll_facts_download_csv
    end

    # Admin Online Training
    scope :trainings do
      get "", to: "trainings#index", as: :trainings

      scope :tracks do
        put ":training_track_id/reorder", to: "trainings#reorder_module", as: :reorder_module
        post "", to: "trainings#create_type", as: :create_training_track
        patch ":type_id", to: "trainings#update_type", as: :update_training_track
        post ":type_id/modules", to: "trainings#create_module", as: :create_training_module
      end

      scope "modules/:module_id" do
        patch "", to: "trainings#update_module", as: :update_training_module
        delete "", to: "trainings#destroy_module", as: :delete_training_module
        post "duplicate", to: "trainings#duplicate_module", as: :duplicate_training_module

        scope :submodules do
          get "", to: "training_submodules#index", as: :training_submodules_index
          get "new", to: "training_submodules#new", as: :training_submodules_new
          post "", to: "training_submodules#create", as: :training_submodules_create
          get ":submodule_id/edit", to: "training_submodules#edit", as: :training_submodules_edit
          patch ":submodule_id", to: "training_submodules#update", as: :training_submodules_update
          delete ":submodule_id", to: "training_submodules#destroy", as: :training_submodules_delete
        end

        scope :quizzes do
          get "new", to: "quizzes#new", as: :quizzes_new
          post "", to: "quizzes#create", as: :quizzes_create
        end
      end

      resources :quizzes, except: %i[index destroy new create] do
        post "duplicate", to: "quizzes#duplicate", as: :duplicate

        get "quiz_questions/new", to: "quiz_questions#new", as: :new_questions
        post "quiz_questions", to: "quiz_questions#create", as: :create_questions
        delete "quiz_questions/:question_id", to: "quiz_questions#destroy", as: :delete_question
        get "quiz_questions/:question_id/edit", to: "quiz_questions#edit", as: :edit_questions
        patch "quiz_questions/:question_id", to: "quiz_questions#update", as: :update_questions
      end
    end

    # Quiz attempt appeals
    scope :quiz_appeals do
      post ":appeal_id/approve", to: "quiz_appeals#approve", as: :approve_quiz_appeal
      post ":appeal_id/reject", to: "quiz_appeals#reject", as: :reject_quiz_appeal
      get "", to: "quiz_appeals#index", as: :quiz_appeals
    end
  end

  # FulfillmentV2 Configuration
  namespace :fulfillment_config do
    get "countries", to: "countries#index", as: :countries
    get "countries/:iso_name/edit", to: "countries#edit", as: :country_edit
    put "countries/:iso_name", to: "countries#update", as: :country_update
    get "packaging_types", to: "packaging_types#index", as: :packaging_types
    get "packaging_types/:id/edit", to: "packaging_types#edit", as: :packaging_type_edit
    post "packaging_types/:id", to: "packaging_types#update", as: :packaging_type_update
    get "packaging_types/new", to: "packaging_types#new", as: :packaging_type_new
    post "packaging_types", to: "packaging_types#create", as: :packaging_type_create
    get "slots", to: "slots#index", as: :slots
    get "slots/upload", to: "slots#upload", as: :slots_upload
    post "slots", to: "slots#create", as: :slots_create
    get "slots/:id/download", to: "slots#download", as: :download_slot_file
    get "slots/download_format_file", to: "slots#download_format_file", as: :download_format_file
    get "slots/export", to: "slots#export", as: :slots_export
    get "fleet_plan_dashboard", to: "fleet_plan_dashboard#index", as: :fleet_plan_dashboard
    get "slot_availability_dashboard", to: "slot_availability_dashboard#index", as: :slot_availability_dashboard

    scope :long_slots do
      get "", to: "long_slots#index", as: :long_slots
      get "upload", to: "long_slots#upload", as: :long_slots_upload
      post "", to: "long_slots#create", as: :long_slots_create
      get "download_format_file", to: "long_slots#download_format_file", as: :long_slots_download_template
      delete ":slot_id", to: "long_slots#destroy", as: :long_slots_delete
      get "edit", to: "long_slots#edit", as: :long_slots_edit
      put "update", to: "long_slots#update", as: :long_slots_update
    end

    scope :shifts do
      get "", to: "shifts#index", as: :shifts
      get "upload", to: "shifts#upload", as: :shifts_upload
      post "", to: "shifts#create", as: :shifts_create
      get "download_format_file", to: "shifts#download_format_file", as: :shifts_download_template
      delete ":shift_id", to: "shifts#destroy", as: :shifts_delete
      get "edit", to: "shifts#edit", as: :shifts_edit
      put "update", to: "shifts#update", as: :shifts_update
    end

    scope :stock_location do
      scope :shop_n_deliveries do
        get "", to: "shop_n_deliveries#index", as: :shop_n_deliveries
        get ":stock_location_id/edit", to: "shop_n_deliveries#edit", as: :shop_n_delivery_edit
        put ":stock_location_id", to: "shop_n_deliveries#update", as: :shop_n_delivery_update
      end

      scope :ddf do
        get "", to: "ddfs#index", as: :ddfs
        get ":stock_location_id/show", to: "ddfs#show", as: :ddf
        get ":stock_location_id/edit", to: "ddfs#edit", as: :ddf_edit
        post ":stock_location_id/update", to: "ddfs#update", as: :ddf_update
        get "download_format_file", to: "ddfs#download_format_file", as: :ddf_download_format_file
      end

      scope :ge_and_tpl do
        get "", to: "ge_and_tpls#index", as: :ge_and_tpls
        get ":stock_location_id/edit", to: "ge_and_tpls#edit", as: :ge_and_tpl_edit
        put ":stock_location_id", to: "ge_and_tpls#update", as: :ge_and_tpl_update
      end

      scope :generals do
        get "", to: "generals#index", as: :generals
        get ":stock_location_id/edit", to: "generals#edit", as: :general_edit
        put ":stock_location_id", to: "generals#update", as: :general_update
      end

      scope :store_coverage do
        get "", to: "store_coverages#index", as: :store_coverages
        get "edit", to: "store_coverages#edit", as: :store_coverages_edit
        get ":external_id", to: "store_coverages#show", as: :store_coverage, constraints: { format: :json }
        post "download_kml", to: "store_coverages#download_kml", as: :download_kml
        post "upload_kml", to: "store_coverages#upload_kml", as: :upload_kml
        post "update", to: "store_coverages#update", as: :store_coverages_update
      end

      scope :mass_update do
        get "", to: "mass_update#index", as: :mass_update_index
        post "download", to: "mass_update#download_template", as: :mass_update_download_template
        post "upload", to: "mass_update#upload", as: :mass_update_upload
      end
    end

    get "packaging_stock_locations", to: "packagings#store_index", as: :packaging_stock_locations
    scope "stock_locations" do
      scope "/:stock_location_id/packagings" do
        get "", to: "packagings#index", as: :packagings
        get "new", to: "packagings#new", as: :new_packaging
        post "", to: "packagings#create", as: :create_packaging
        get ":id/edit", to: "packagings#edit", as: :edit_packaging
        put "reorder", to: "packagings#reorder_sequence", as: :reorder_packaging
        put ":id", to: "packagings#update", as: :update_packaging
        delete ":id/remove", to: "packagings#remove", as: :remove_packaging
      end
    end

    scope :on_demand_orders do
      get "", to: "on_demand_orders#index", as: :on_demand_orders
      put "update_user", to: "on_demand_orders#update_user", as: :update_on_demand_order_user
    end

    scope :express_configurations do
      get "", to: "express_configurations#index", as: :express_configurations
      get ":stock_location_id/edit", to: "express_configurations#edit", as: :express_configuration_edit
      put ":stock_location_id", to: "express_configurations#update", as: :express_configuration_update
    end

    scope :on_demand_clusters do
      get "", to: "on_demand_clusters#index", as: :on_demand_clusters
      post "", to: "on_demand_clusters#create", as: :create_on_demand_cluster
      put ":id", to: "on_demand_clusters#update", as: :update_on_demand_cluster
    end

    scope :manual_assignments do
      get "", to: "manual_assignments#index", as: :manual_assignment
      get "batches/:batch_id", to: "manual_assignments#view_available_fleets", as: :view_available_fleets
      put "batches/:batch_id/assign_user", to: "manual_assignments#update_batch_user", as: :update_batch_user
      put "batches/:batch_id/shipments/:shipment_number/switch_to_hf", to: "manual_assignments#switch_to_hf", as: :switch_to_hf
      put "batches/:batch_id/shipments/:shipment_number/unpool", to: "manual_assignments#unpool", as: :unpool
      put "order_number/:order_number/tpl_vendor/:tpl_vendor/tpl_vehicle_type/:tpl_vehicle_type/switch_to_tpl", to: "manual_assignments#switch_to_tpl", as: :switch_to_tpl
      get "logs", to: "manual_assignment_logs#index", as: :manual_assignment_logs
    end

    scope :manual_arrivals do
      get "", to: "manual_arrivals#index", as: :manual_arrival
      put "/:order_number/update_status", to: "manual_arrivals#update_shipment_status", as: :update_manual_arrival_status
    end

    scope :manual_order_mutations do
      get "", to: "manual_order_mutations#index", as: :manual_order_mutation
      get "batches/:batch_id", to: "manual_order_mutations#view_available_stock_locations", as: :view_available_stock_location
      put "/:order_number/update_stock_location", to: "manual_order_mutations#update_stock_location", as: :update_stock_location
    end
  end

  # Shared API endpoints
  namespace :api do
    scope module: "search" do
      resources :tags, only: :index
    end
  end

  # TinyMCE image upload route
  post "tinymce_assets", to: "tinymce_assets#image_upload"

  authenticate :user, lambda { |u| u.system? } do
    require "sidekiq/web"
    require "sidekiq-status/web"
    require "sidekiq/cron/web"

    mount Sidekiq::Web => "/sidekiq"
  end

  get "/404", to: "errors#not_found"
  get "/422", to: "errors#unacceptable"
  get "/500", to: "errors#internal_error"
end
