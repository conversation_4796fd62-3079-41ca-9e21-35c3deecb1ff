# Inspired by an article below
# https://codetales.io/blog/speeding-up-bundler-in-dockerized-environments

ARG BASE_IMAGE=ruby:2.6.3-slim-buster
ARG CACHE_IMAGE=${BASE_IMAGE}
ARG RAILS_ENV
ARG SECRET_KEY_BASE

# Create a build stage for the gem cache
# Ensure that the /usr/local/bundle exists in case we use an empty image as cache
FROM ${CACHE_IMAGE} AS gem-cache
RUN mkdir -p /usr/local/bundle

# Create an intermediate image that has bundler installed
FROM $BASE_IMAGE AS base
# Known issue workaround
# https://github.com/nextcloud/docker/issues/380
RUN mkdir -p /usr/share/man/man1

RUN apt-get update -qq && \
    apt-get install -y build-essential imagemagick npm curl file \
    libpq-dev curl libcurl4 libcurl4-openssl-dev
    # openjdk-11-jre

RUN curl -fsSL https://deb.nodesource.com/setup_lts.x | bash -

RUN apt-get install -y nodejs

RUN npm install npm@latest -g

ENV INSTALL_PATH "/usr/src/fms-2.0"

RUN mkdir -p $INSTALL_PATH
WORKDIR $INSTALL_PATH

# Copy the gems from a the gem-cache build stage, install missing gems and clean up
FROM base AS gems
COPY --from=gem-cache /usr/local/bundle /usr/local/bundle
ARG SSH_PRIVATE_KEY

# Known issue workaround
# https://github.com/nextcloud/docker/issues/380
RUN mkdir -p /usr/share/man/man1

RUN apt-get update -qq && \
    apt-get install -y git wget openssh-client
    # default-jdk

# Set working directory
WORKDIR /

ENV GEM_HOME "/usr/local/bundle"
ARG BUNDLED_WITH "2.3.12"
ARG GIT_SSL_NO_VERIFY "true"
ARG CI_JOB_TOKEN

RUN mkdir /root/.ssh/ \
    && echo "${SSH_PRIVATE_KEY}" > /root/.ssh/id_rsa \
    && chmod 600 /root/.ssh/id_rsa \
    && touch /root/.ssh/known_hosts \
    && ssh-keyscan bitbucket.org >> /root/.ssh/known_hosts

# Adding gems
COPY Gemfile $WORKDIR
COPY Gemfile.lock $WORKDIR

# From https://gist.github.com/sephraim/b11ff4d704c4f7d13280c2b3603cbea4
# Prep for gem installation
# NOTE: The RubyGems version that comes with Ruby 2.6 contains a bug and must be updated to v3.2.3.
#       Also, Bundler must be pinned to v2.4.22 because it is the last version that works with Ruby 2.6.
#       When Ruby gets upgraded, the pinned versions can get removed below.
RUN gem update --system 3.2.3 && gem install bundler -v "${BUNDLED_WITH}"

RUN bundle config https://gitlab.happyfresh.net/logistics/carbon/store-coverage-integrate.git gitlab-ci-token:${CI_JOB_TOKEN}

RUN bundle install --jobs 20 --retry 5
    # && bundle clean --force \
    # && rm -rf $GEM_HOME/cache/*.gem \
    # && find $GEM_HOME/gems/ -name "*.c" -delete \
    # && find $GEM_HOME/gems/ -name "*.o" -delete

# Add node modules
COPY package.json $WORKDIR
COPY package-lock.json $WORKDIR
COPY modernizr-config.json $WORKDIR

# Set timeout to avoid network timeout error when install packages
RUN npm config set fetch-retry-mintimeout 20000
RUN npm config set fetch-retry-maxtimeout 120000
RUN npm install

# Fix UID Bitbucket
# https://jira.atlassian.com/browse/BCLOUD-17319
# RUN \
#     chown root:root -R /node_modules/ && \
#     chown root:root -R /usr/local/bundle

FROM base AS prod

# Datadog set version and source
# ARG VERSION
# ENV DD_VERSION=$VERSION
# LABEL com.datadoghq.tags.version=$VERSION
# ENV DD_SOURCE="ruby"
# LABEL com.datadoghq.tags.source="ruby"

WORKDIR $INSTALL_PATH

COPY --from=gems /usr/local/bundle /usr/local/bundle
COPY --from=gems /node_modules/ ./node_modules/
COPY . $INSTALL_PATH

RUN ls
RUN ls /usr/local/bundle
RUN ls /usr/local/bundle/gems

RUN apt-get update && \
    apt-get upgrade -y && \
    apt-get install -y git

RUN bundle install

RUN apt-get remove git -y

RUN RAILS_ENV=$RAILS_ENV SECRET_KEY_BASE=$SECRET_KEY_BASE bundle exec rake assets:precompile

ENTRYPOINT [ "/bin/bash", "docker-entrypoint.sh" ]

CMD ["server"]
