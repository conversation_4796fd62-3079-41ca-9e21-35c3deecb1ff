module Service
  module Fulfillment
    class CreditCard < BaseFulfillmentService
      def self.find_all
        # For development/testing, return sample data if API is not available
        if Rails.env.development? && ENV["USE_SAMPLE_CREDIT_CARDS"] == "true"
          return sample_response
        end

        client.dup.get("/api/admin/credit_cards")
      end

      def self.find_by_id(id)
        client.dup.get("/api/admin/credit_cards/#{id}")
      end

      private

      def self.client
        @@client ||= default_client
      end

      def self.default_client
        Faraday.new(ENV["FULFILLMENT_BASE_URL"] || "https://happyfresh.fsrv-gw-stage.happyfresh.net") do |c|
          c.headers["X-Fulfillment-Tenant-Token"] = ENV["FULFILLMENT_TENANT_TOKEN"] || "12345678"
          c.headers["X-Fulfillment-User-Token"] = ENV["FULFILLMENT_CONFIG_ADMIN_USER_TOKEN"] || "11111111"
          c.response :json, content_type: /\bjson/
          c.adapter Faraday.default_adapter
        end
      end

      def self.sample_response
        OpenStruct.new(
          status: 200,
          body: {
            "took" => "121",
            "response" => [
              {
                "name" => "yan",
                "last_digits" => "0001",
                "month" => "01",
                "year" => "2028",
                "state" => "DISABLED",
                "shopper_email" => "<EMAIL>",
                "shopper_phone" => "0"
              },
              {
                "name" => "yan",
                "last_digits" => "9100",
                "month" => "07",
                "year" => "2028",
                "state" => "ENABLED",
                "shopper_email" => "<EMAIL>",
                "shopper_phone" => "0"
              },
              {
                "name" => "yan",
                "last_digits" => "9100",
                "month" => "07",
                "year" => "2028",
                "state" => "AUTOMATIC",
                "shopper_email" => "<EMAIL>",
                "shopper_phone" => "0"
              }
            ]
          }
        )
      end
    end
  end
end
