module Service
  module Fulfillment
    class CreditCard < BaseFulfillmentService
      def self.find_all
        client.dup.get("/api/admin/credit_cards")
      end

      def self.find_by_id(id)
        client.dup.get("/api/admin/credit_cards/#{id}")
      end

      def self.update(id, params)
        payload = {
          user_id: params[:user_id].to_i,
          state: params[:state]
        }

        client.dup.put("/api/admin/credit_cards/#{id}", payload.to_json, "Content-Type" => "application/json")
      end

      private

      def self.client
        @@client ||= default_client
      end

      def self.default_client
        Faraday.new(ENV["FULFILLMENT_BASE_URL"] || "https://happyfresh.fsrv-gw-stage.happyfresh.net") do |c|
          c.headers["X-Fulfillment-Tenant-Token"] = ENV["FULFILLMENT_TENANT_TOKEN"] || "12345678"
          c.headers["X-Fulfillment-User-Token"] = ENV["FULFILLMENT_CONFIG_ADMIN_USER_TOKEN"] || "11111111"
          c.response :json, content_type: /\bjson/
          c.adapter Faraday.default_adapter
        end
      end
    end
  end
end
