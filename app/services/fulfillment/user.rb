module Service
  module Fulfillment
    class User < Service::Fulfillment::BaseFulfillmentService
      include ActiveModel::Validations

      attr_accessor :email,
        :first_name,
        :last_name,
        :is_active,
        :phone,
        :profile_picture_url,
        :roles

      validates :email, presence: true
      validates :first_name, presence: true
      validates :phone, presence: true
      validates_format_of :phone, with: /[0-9]+/, allow_nil: true # check for nil already covered by validates presence: true
      validate :valid_roles
      validate :valid_email

      def initialize(user_params)
        @email = user_params["email"]
        @first_name = user_params["first_name"]
        @last_name = user_params["last_name"]
        @is_active = user_params["is_active"]
        @phone = user_params["phone"]
        @roles = user_params["roles"]
        @user_params = user_params.to_h
      end

      def self.create(payload)
        obj = new(payload)
        obj.create
      end

      def self.update(payload)
        obj = new(payload)
        obj.update
      end

      def self.find_all
        client.dup.get("/api/users")
      end


      def create
        validate!

        payload = @user_params
        payload["is_active"] = is_active.present? ? is_active : true
        payload["roles"] = []
        @roles.each do |role|
          payload["roles"] << { "name" => role }
        end

        self.class.client.dup.post("/api/users", payload.to_json, "Content-Type" => "application/json")
      end

      def update
        validate!

        payload = @user_params
        payload["is_active"] = is_active.present? ? is_active : true
        payload["roles"] = []
        @roles.each do |role|
          payload["roles"] << { "name" => role }
        end

        self.class.client.dup.put("/api/users", payload.to_json, "Content-Type" => "application/json")
      end

      private
        def valid_email
          if email.blank?
            errors.add :email, "Email cannot be blank"
          elsif !email.match(Devise.email_regexp)
            errors.add :email, "Invalid snd email"
          end
        end

        def valid_roles
          unless roles.is_a?(Array) && roles.present? && roles.all? { |role| %w[SHOPPER DRIVER ON_DEMAND_RANGER BACK_OFFICE].include?(role) }
            errors.add :roles, "Invalid role"
          end
        end

        def self.sample_response
          OpenStruct.new(
            status: 200,
            body: {
              "took" => "50",
              "response" => [
                {
                  "id" => "1",
                  "email" => "<EMAIL>",
                  "first_name" => "John",
                  "last_name" => "Doe",
                  "is_active" => true,
                  "phone" => "081234567890",
                  "roles" => ["SHOPPER"]
                },
                {
                  "id" => "2",
                  "email" => "<EMAIL>",
                  "first_name" => "Jane",
                  "last_name" => "Smith",
                  "is_active" => true,
                  "phone" => "081234567891",
                  "roles" => ["SHOPPER"]
                },
                {
                  "id" => "3",
                  "email" => "<EMAIL>",
                  "first_name" => "Admin",
                  "last_name" => "User",
                  "is_active" => true,
                  "phone" => "081234567892",
                  "roles" => ["BACK_OFFICE"]
                },
                {
                  "id" => "4",
                  "email" => "<EMAIL>",
                  "first_name" => "Driver",
                  "last_name" => "User",
                  "is_active" => true,
                  "phone" => "081234567893",
                  "roles" => ["DRIVER"]
                }
              ]
            }
          )
        end
    end
  end
end
