module System
  class RolesController < BaseController
    before_action :set_role, only: %i[show edit update destroy]
    before_action :authorize_action, except: %i[index]

    # GET /system/roles
    def index
      @q = policy_scope(Role, policy_scope_class: SystemPolicy::Scope).ransack(params[:q])
      @q.sorts = "name desc" if @q.sorts.empty?
      @roles = @q.result.page(params[:page])
    end

    # GET /system/roles/1
    def show
      # defined on before_action
    end

    # GET /system/roles/new
    def new
      @role = Role.new
    end

    # GET /system/roles/1/edit
    def edit
      # defined on before_action
    end

    # POST /system/roles
    def create
      interactor = CreateRole.call(role_params) # Interactor
      @role = interactor.model

      if interactor.success?
        redirect_to system_role_path(@role), notice: "Role was successfully created."
      else
        flash[:error] = interactor.message
        render :new
      end
    end

    # PATCH/PUT /system/roles/1
    def update
      interactor = UpdateRole.call(model: @role, params: role_params) # Interactor
      @role = interactor.model

      if interactor.success?
        redirect_to system_role_path(@role), notice: "Role was successfully updated."
      else
        flash[:error] = interactor.message
        render :edit
      end
    end

    # DELETE /system/roles/1
    def destroy
      interactor = DeleteRole.call(model: @role) # Interactor
      if interactor.success?
        redirect_to system_roles_url, notice: "FMS Role was successfully destroyed."
      else
        flash[:error] = interactor.message
        redirect_to system_roles_url, alert: "Failed to delete FMS Role."
      end
    end

    private

      def set_role
        @role = Role.find(params[:id])
      end

      def authorize_action
        authorize :system, :actions
      end

      def role_params
        params.require(:role).permit(:name, :all_access, controller_actions: [])
      end
  end
end
