module System
  module PushNotification
    class RecipientsController < BaseController
      before_action :authorize_action
      before_action :set_log
      append_before_action :redirect_to_configurations_index, only: :index, if: -> { !@log }

      def index
        q = policy_scope(@log.recipients, policy_scope_class: SystemPolicy::Scope).ransack(params[:q])
        q.sorts = "created_at DESC" if q.sorts.empty?
        @recipients = q.result.page(params[:page])
      end

      private

        def authorize_action
          authorize :system, :actions
        end

        def set_log
          @log = ::PushNotification::Log.where(id: params[:log_id]).first
        end

        def redirect_to_configurations_index
          flash[:error] = t(".not_found")
          redirect_to system_push_notification_configurations_path
        end
      # end of private block
    end
  end
end
