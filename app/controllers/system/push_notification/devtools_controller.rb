module System
  module PushNotification
    class DevtoolsController < BaseController
      before_action :authorize_action

      def send_notification
        resp = {}
        properties = {
          "title" => notification_params[:title],
          "message" => notification_params[:message],
          "url" => notification_params[:data][:url],
          "type" => "fms_open_webview"
        }

        if notification_params[:use_case] == "news"
          properties = {
            "news_title" => notification_params[:title],
            "news_url" => notification_params[:data][:url],
            "news_snippet" => notification_params[:message],
            "type" => "fms_news_broadcast"
          }
        end

        if notification_params[:service_type] == "braze"
          resp = Service::Braze::TrainingModuleNotification.send(
            notification_params[:campaign_id],
            notification_params[:recipients],
            properties
          )
        elsif notification_params[:service_type] == "moengage"
          resp = Service::Moengage::Campaigns.trigger_send(
            notification_params[:campaign_id],
            notification_params[:recipients],
            notification_params[:title],
            notification_params[:message],
            properties
          )
        end

        respond_to do |format|
          format.json { render json: { message: resp.body["message"] || resp.body["status"] }, status: :ok }
        end
      rescue StandardError => error
        respond_to do |format|
          format.json { render json: { message: error.message }, status: :bad_request }
        end
      end

      private

        def authorize_action
          authorize :system, :actions
        end

        def notification_params
          params.require(:notification).permit(:campaign_id, :service_type, :use_case, :title, :message, recipients: [], data: [:url])
        end
      # end of private block
    end
  end
end
