module System
  module PushNotification
    class ConfigurationsController < BaseController
      before_action :authorize_action, except: %i[index]
      before_action :set_configuration, only: %i[show edit update destroy toggle_configuration]

      def index
        @q = policy_scope(::PushNotification::Configuration, policy_scope_class: SystemPolicy::Scope).ransack(params[:q])
        @q.sorts = "created_at DESC" if @q.sorts.empty?
        @configurations = @q.result.page(params[:page])
      end

      def new
        @configuration = current_user.push_notification_configurations.new
      end

      def create
        interactor = ::PushNotification::CreateConfiguration.call(*configuration_params.merge(creator: current_user))
        @configuration = interactor.model
        if interactor.success?
          redirect_to_configuration_detail_with_success_message
        else
          flash[:error] = interactor.message
          render :new, status: :unprocessable_entity
        end
      end

      def show
        # @configuration loaded by set_configuration and render default show template
      end

      def edit
        # @configuration loaded by set_configuration and render default edit template
      end

      def update
        interactor = ::PushNotification::UpdateConfiguration.call(model: @configuration, updater: current_user, params: configuration_params)
        @configuration = interactor.model
        if interactor.success?
          redirect_to_configuration_detail_with_success_message
        else
          flash[:error] = interactor.message
          render :edit, status: :unprocessable_entity
        end
      end

      def destroy
        interactor = ::PushNotification::DeleteConfiguration.call(model: @configuration, remover: current_user)
        @configuration = interactor.model

        respond_to do |format|
          format.html do
            if interactor.success?
              flash[:notice] = success_message
            else
              flash[:error] = interactor.message
            end

            redirect_to system_push_notification_configurations_path
          end

          format.js do
            @message = interactor.success? ? t(".#{action_name}.success") : interactor.message
            render :destroy, status: interactor.success? ? :ok : :unprocessable_entity
          end
        end
      end

      def toggle_configuration
        interactor = ::PushNotification::UpdateConfiguration.call(model: @configuration,
                                                                  updater: current_user,
                                                                  params: {  is_active: !@configuration.is_active? })
        @configuration = interactor.model

        respond_to do |format|
          format.html do
            if interactor.success?
              flash[:notice] = t(".success", status: @configuration.is_active? ? t(".activated") : t(".deactivated"))
            else
              flash[:error] = interactor.message
            end

            redirect_to system_push_notification_configurations_path
          end

          format.js do
            @message = interactor.success? ? t(".#{action_name}.success") : interactor.message
            render :toggle_configuration, status: interactor.success? ? :ok : :unprocessable_entity
          end
        end
      end

      private

        def authorize_action
          authorize :system, :actions
        end

        def set_configuration
          @configuration = ::PushNotification::Configuration.find(params[:configuration_id].presence || params[:id])
        end

        def configuration_params
          params.require(:configuration).permit(:use_case,
                                                :service_type,
                                                :campaign_id,
                                                :deliver_days_after,
                                                :delivery_start_time,
                                                :delivery_end_time,
                                                :is_active)
        end

        def redirect_to_configuration_detail_with_success_message
          flash[:notice] = success_message
          redirect_to system_push_notification_configuration_path(@configuration)
        end

        def success_message
          t(".success")
        end
      # end of private block
    end
  end
end
