module System
  module PushNotification
    class LogsController < BaseController
      before_action :authorize_action
      before_action :set_configuration
      append_before_action :set_log_time_range, only: :index
      append_before_action :redirect_to_configurations_index, only: :index, if: -> { !@configuration }

      def index
        q = policy_scope(@configuration.logs.in_period(@start_date, @end_date), policy_scope_class: SystemPolicy::Scope).ransack(params[:q])
        q.sorts = "created_at DESC" if q.sorts.empty?
        @logs = q.result.page(params[:page])
      end

      private

        def authorize_action
          authorize :system, :actions
        end

        def set_configuration
          @configuration = ::PushNotification::Configuration.where(id: params[:configuration_id]).first
        end

        def redirect_to_configurations_index
          flash[:error] = t(".not_found")
          redirect_to system_push_notification_configurations_path
        end

        def set_log_time_range
          @start_date = params[:start_date].presence || (Date.today - 6.month)
          @end_date = params[:end_date].presence || Date.today
        end

      # end of private block
    end
  end
end
