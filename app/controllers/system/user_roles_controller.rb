module System
  class UserRolesController < BaseController
    before_action :set_user, only: %i[show edit update]
    before_action :authorize_action, except: %i[index]

    # GET /system/user_roles
    def index
      @q = policy_scope(User.admin, policy_scope_class: SystemPolicy::Scope).ransack(params[:q])
      @q.sorts = "email asc" if @q.sorts.empty?
      @users = @q.result.page(params[:page])
    end

    # GET /system/user_roles/1/edit
    def edit
      # defined on before_action
    end

    # PATCH/PUT /system/user_roles/1
    def update
      interactor = UpdateUserRole.call(model: @user, roles: role_params[:roles]) # Interactor
      @role = interactor.model

      if interactor.success?
        redirect_to system_user_roles_path, notice: "User roles was successfully updated."
      else
        flash[:error] = interactor.message
        render :edit
      end
    end

    private

      def set_user
        @user = User.friendly.find(params[:id])
      end

      def authorize_action
        authorize :system, :actions
      end

      def role_params
        params.require(:user).permit(roles: [])
      end
  end
end
