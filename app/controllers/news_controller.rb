class NewsController < BaseController
  before_action :set_news, only: %i[show store_comment]
  before_action :set_sorting_params, only: [:index]

  # GET /news
  def index
    authorize :dash, :actions

    # This ivar is not used in the view, only as input to <PERSON><PERSON><PERSON>. There is
    # no need to eager load associations here, <PERSON><PERSON><PERSON> avoids N+1 queries.
    @q = policy_scope(News.includes(:author), policy_scope_class: SndAndAdminPolicy::Scope).ransack(params[:q])

    # query for sorting purpose (with dropdown)
    case @selected_sorting
    when "latest_updated"
      @q.sorts = "updated_at desc"
    when "most_popular"
      @q.sorts = "viewers_count desc"
    else
      @q.sorts = "updated_at desc" # if load the page for the first time (params[:sorting] is null)
    end

    # Ransack search/filter results, paginated by <PERSON><PERSON><PERSON>.
    @news = @q.result.published.page(params[:page]).per(6)
  end

  # GET /news/slug
  def show
    authorize :snd_and_admin, :actions

    # only count direct access to the link, not from redirect post comment
    if notice.nil?
      interactor = CountNewsViews.call(news: @news, notice: nil)
      if interactor.failure?
        flash[:error] = interactor.message
        redirect_to news_index_path
      end
    end
  end

  # POST /news/:id/comments
  def store_comment
    authorize :snd_and_admin, :actions

    interactor = StoreNewsComment.call(news: @news, body: news_comment_params[:body], author: current_user)
    @comment = interactor.model

    respond_to do |format|
      if interactor.success?
        format.html { redirect_to news_path(@news), notice: t(".comment_created") }
        format.json { render :show, status: :created, location: @news }
      else
        @news.reload
        flash[:error] = interactor.message
        format.html { render :show }
        format.json { render json: @news.errors, status: :unprocessable_entity }
      end
    end
  end

  private

    # set @news instance variable
    def set_news
      @news = News.includes(comments: :author).friendly.find(params[:id])
    rescue ActiveRecord::RecordNotFound
      flash[:error] = I18n.t("news.e.news_nil")
      redirect_to news_index_path
    end

    # Never trust parameters from the scary internet, only allow the white list through.
    def news_comment_params
      params.require(:comment).permit(:body)
    end

    def set_sorting_params
      # set value for the sorting dropdown
      case params[:sorting]
      when "latest_updated"
        @selected_sorting = "latest_updated"
      when "most_popular"
        @selected_sorting = "most_popular"
      end
    end
end
