class FeedbacksController < ApplicationController
  before_action :set_feedback_log, only: %i[submit ignore]

  # POST /feedbacks/submit
  def submit
    authorize :snd_and_admin, :actions

    interactor = ReceiveFeedback.call(model: @feedback_log, params: feedback_log_params, status: :submitted)
    if interactor.success?
      render json: @feedback_log, status: :ok
    else
      render json: { error: interactor.message }, status: interactor.status_code
      Rails.logger.error("submit_feedback: #{interactor.message}")
    end
  end

  # POST /feedbacks/ignore
  def ignore
    authorize :snd_and_admin, :actions

    interactor = ReceiveFeedback.call(model: @feedback_log, params: feedback_log_params, status: :ignored)
    if interactor.success?
      render json: @feedback_log, status: :ok
    else
      render json: { error: interactor.message }, status: interactor.status_code
      Rails.logger.error("ignore_feedback: #{interactor.message}")
    end
  end

  private

    def set_feedback_log
      @feedback_log = FeedbackLog.find(params[:id])
    end

    def authorize_action
      authorize :admin, :actions
    end

    def feedback_log_params
      params.permit(:id, :feedback, :details)
    end
end
