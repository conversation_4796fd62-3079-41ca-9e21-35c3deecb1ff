class ErrorsController < ApplicationController
  skip_before_action :authenticate_user!
  skip_after_action :verify_authorized

  def not_found
    render "errors/not_found", layout: "guest", status: :not_found
  end

  def unprocessable
    render file: "#{Rails.root}/public/422.html", layout: false, status: :unprocessable_entity
  end

  def internal_error
    render file: "#{Rails.root}/public/500.html", layout: false, status: :internal_server_error
  end
end
