class ApplicationController < ActionController::Base
  # # Prevent CSRF attacks by raising an exception.
  # # For APIs, you may want to use :null_session instead.
  # protect_from_forgery with: :exception

  # Authorization gem
  include Pundit

  protect_from_forgery with: :exception, unless: :with_token?

  # Ensure that Pundit's #verify_policy_scoped or #verify_authorized are
  # called in all actions of all controllers. In other words, ensure
  # authorization policies are enforced everywhere.
  after_action :verify_authorized,
               except: :index,
               unless: :devise_controller?
  after_action :verify_policy_scoped,
               only: :index,
               unless: :devise_controller?

  # Require authentication for all requests. Add
  # skip_before_action :authenticate_user! to controllers that should not
  # require authentication.
  before_action :authenticate_user!, unless: :devise_controller?

  # set locale based on country tenant
  before_action :set_locale

  # For onboarding process (currently disabled)
  before_action :has_onboarded

  # Display user-friendly errors for the following exceptions
  rescue_from Pundit::NotAuthorizedError,
              with: :show_user_not_authorized_error
  rescue_from ActiveRecord::DeleteRestrictionError,
              with: :show_delete_restriction_error
  rescue_from ActiveRecord::RecordNotFound,
              with: :handle_record_not_found

  layout :set_layout

  def default_url_options
    { host: request.host_with_port }
  end

  private

    def set_layout
      return "guest" unless user_signed_in?
    end

    def with_token?
      session[:session_type] == "webview"
    end

    # Rescue Pundit::NotAuthorizedError, which happens when a user tries to
    # access a resource for which he does not have permission.
    def show_user_not_authorized_error
      redirect_to request.referer || root_path,
                  flash: { error: t(:not_authorized, scope: "authorization") }
    end

    # Rescue raise ActiveRecord::DeleteRestrictionError, which happens when trying
    # do delete records restricted by "dependent: :restrict_with_exception"
    def show_delete_restriction_error(exception)
      redirect_to request.referer || root_path,
                  flash: { error: exception.message }
    end

    def handle_record_not_found
      render "errors/not_found", layout: "guest", status: :not_found
    end

    # Inject params to Policy class
    # https://github.com/varvet/pundit#additional-context
    def pundit_user
      PolicyContext.new(current_user, params)
    end

    # Redirect if user has not been onboarded yet
    def has_onboarded
      if user_signed_in? &&
        current_user.try(:user?) &&
        ! current_user.try(:onboarded) &&
        (params[:controller] != "devise/sessions") &&
        (params[:action] != "destroy") &&
        !with_token?  # prevent redirect to onboarding in snd webview
        redirect_to snd_onboarding_index_url
      end
    end

    def set_locale
      if request.cookies["locale"].nil?
        locale = Apartment::Tenant.current
      else
        locale = request.cookies["locale"]
      end

      if locale == "my"
        locale = "ms"
      end

      unless I18n.config.available_locales.map(&:to_s).include?(locale)
        locale = nil
      end

      # check whether user is snd or not
      if user_signed_in?
        unless current_user.user?
          locale = nil
        end
      end

      I18n.locale = locale || I18n.default_locale
    end
end
