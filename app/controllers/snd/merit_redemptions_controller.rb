class Snd::MeritRedemptionsController < Snd::BaseController
  include <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>per

  before_action :authorize_action, except: [:index]
  before_action :validate_date_range, only: [:index]

  # GET /merit_redemptions
  def index
    @q = policy_scope(MeritRedemption
      .includes(:merit_reward)
      .where(requested_by: current_user.id), policy_scope_class: SndPolicy::Scope)
      .between_date(@start_date, @end_date)
      .ransack(params[:q])
    @q.sorts = "requested_at desc" if @q.sorts.empty?
    @redeem_logs = @q.result.page(params[:page])
  end

  # GET '/merit_redemptions/new'
  def new
    @merit_redemption = MeritRedemption.new
    @merit_rewards = MeritReward.all
    # provide back link to referer if different with current, otherwise set to redemptions history
    @back_button_path = request.referer != new_snd_merit_redemption_url ? :back : snd_merit_redemptions_path
  end

  # POST '/merit_redemptions'
  def create
    interactor = AddMeritRedemption.call(requester: current_user, params: merit_redemption_params)
    @merit_redemption = interactor.model

    respond_to do |format|
      if interactor.success?
        format.html { redirect_to new_snd_merit_redemption_path, notice: t(".success_message") }
        format.json { render :show, status: :ok, location: @merit_redemption }
      else
        @merit_rewards = MeritReward.all

        flash[:error] = interactor.message
        format.html { render :new, status: interactor.status_code }
        format.json { render json: @merit_redemption.errors, status: interactor.status_code }
      end
    end
  end

  # GET '/merit_redemptions/:id'
  def show
    @cw_add_classes = ["merit-redemption-detail"]
    @merit_redemption = MeritRedemption.find(params[:id])
    @merit_reward = @merit_redemption.merit_reward
  end

  private

    def merit_redemption_params
      params.require(:merit_redemption).permit(:merit_reward_id, :quantity)
    end
end
