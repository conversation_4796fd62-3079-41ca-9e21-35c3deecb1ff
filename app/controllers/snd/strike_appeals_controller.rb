class Snd::StrikeAppealsController < Snd::BaseController
  include <PERSON><PERSON><PERSON><PERSON>Helper

  before_action :set_user_strike, only: [:index_by_user_strike, :new, :create]
  before_action :authorize_action
  before_action :validate_date_range, only: %i[index_by_user]

  # GET '/user_strikes/:user_strike_id/appeals'
  # Returns all appeals for single strike
  def index_by_user_strike
    @strike_appeals = @user_strike.appeals.includes(:requester_strike)
  end

  # GET '/strike_appeals'
  # Returns all user's submitted appeals (all strike)
  def index_by_user
    strike_appeals = current_user.strike_appeals.includes(requester_strike: :creator)

    @q = policy_scope(strike_appeals
      .between_date(@start_date, @end_date), policy_scope_class: SndPolicy::Scope)
      .ransack(params[:q])
    @q.sorts = "created_at desc" if @q.sorts.empty?
    @appeals = @q.result.page(params[:page])
  end

  # GET '/user_strikes/:user_strike_id/appeal/:appeal_id'
  def show
    @cw_add_classes = ["strike-appeals-detail"]
    @strike_appeal = StrikeAppeal.includes(:requester_strike).find(params[:appeal_id])

    @user_strike = @strike_appeal.requester_strike
    @user = current_user
  end

  # GET '/user_strikes/:user_strike_id/appeal'
  def new
    # Prevent user to create appeal on non appealable user_strike
    unless @user_strike.appealable?
      redirect_to snd_strike_logs_path, alert: t(".cannot_appeal", user_strike: @user_strike.log_id)
    end

    @cw_add_classes = ["strike-appeals-form"]
    @strike_appeal = StrikeAppeal.new
  end

  # POST '/user_strikes/:user_strike_id/appeal'
  def create
    interactor = CreateStrikeAppeal.call(requester: current_user, user_strike: @user_strike, params: strike_appeal_params)
    @strike_appeal = interactor.model

    respond_to do |format|
      if interactor.success?
        format.html { redirect_to snd_strike_logs_path, notice: t(".success_create") }
        format.json { render :show, status: :ok, location: @strike_appeal }
      else
        format.html { render :new, status: interactor.status_code }
        format.json { render json: @strike_appeal.errors, status: interactor.status_code }
      end
    end
  end

  private

    def set_user_strike
      @user_strike = UserStrike.where(id: params[:user_strike_id], user_id: current_user.id).first

      redirect_to snd_strike_logs_path, flash: { error: t(".new.strike_not_found") } if @user_strike.blank?
    end

    def strike_appeal_params
      params.require(:strike_appeal).permit(:description, :attachment)
    end
end
