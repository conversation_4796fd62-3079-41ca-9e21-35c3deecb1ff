class Snd::OrderHistoriesController < Snd::BaseController
  include Date<PERSON>ilterHelper
  include OrderHistoryHelper

  before_action :authorize_action, except: :index
  before_action :set_start_end_dates, only: :index
  before_action :set_order, only: :show

  # GET /order_history
  def index
    order_histories = OrderHistory::Order.includes(:jobs).references(:jobs)
      .where(oh_jobs: { fleet_email: current_user.email })
      .order(Arel.sql("oh_orders.delivery_time desc, oh_orders.slot_start::time desc"))
    if params[:search].present?
      order_histories = order_histories.where("(oh_orders.order_number ilike :search OR oh_orders.customer_name ilike :search)", search: "%#{params[:search]}%")
    end

    @q = policy_scope(order_histories.between_date(@start_date, @end_date), policy_scope_class: SndPolicy::Scope)
      .ransack(params[:q])
    @orders = @q.result.page(params[:page])
  end

  # GET /order_history/:order_number
  def show
    # order fetched in before_action
  end

  private

    def set_start_end_dates
      validate_params_date_range(max_days: 7)

      @start_date = (params[:start_date] || 3.days.ago).to_date
      @end_date = (params[:end_date] || Date.current).to_date
    end

    def set_order
      order_number = params[:order_number]

      @order = OrderHistory::Order.joins(:jobs)
        .where("oh_jobs.fleet_email = ? AND oh_orders.order_number = ?", current_user.email, order_number)
        .includes(:customer_payments, :shopper_payments, original_items: :replacement_item, original_packagings: :replacement_packaging).first

      if @order.nil?
        flash[:error] = t("snd.order_histories.e.invalid_order_number", order_number: order_number)
        redirect_to snd_order_history_index_path
      end
    end
end
