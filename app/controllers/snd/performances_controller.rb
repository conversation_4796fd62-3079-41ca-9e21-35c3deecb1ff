class Snd::PerformancesController < Snd::BaseController
  before_action :authorize_action
  before_action :set_period_stepback

  # GET /performances
  def index
    skip_policy_scope

    kpi_interactor = CalculateUserKpis.call(user: current_user, period_stepback: @period_stepback)
    if kpi_interactor.failure?
      flash[:error] = kpi_interactor.message
      redirect_back(fallback_location: root_path)
      return
    end
    @kpi_summary = kpi_interactor.kpi_summary
    @performance_scheme = kpi_interactor.scheme
    @period_start_date = kpi_interactor.period_start_date
    @period_end_date = kpi_interactor.period_end_date

    incentive_interactor = CalculateUserDailyIncentive.call(
      kpi_summary: @kpi_summary,
      user: current_user,
      start_date: @period_start_date,
      end_date: @period_end_date
    )
    if incentive_interactor.payroll_schemes.empty?
      return # don't show earnings
    elsif incentive_interactor.failure?
      flash[:error] = incentive_interactor.message
      redirect_back(fallback_location: root_path)
      return
    end
    @kpi_last_updated = UserKpi.where(email: current_user.email).order(updated_at: :desc).limit(1)
      .select(:updated_at).pluck(:updated_at).first
    @earning_summary = incentive_interactor.earning_summary
    @kpi_earning_summaries = incentive_interactor.kpi_earning_summaries.group_by { |summary| summary[:kpi_type] }
  end

  private
    def set_period_stepback
      @period_stepback =
        if params[:previous_period].blank?
          0
        else
          params[:previous_period].to_i
        end
    end
end
