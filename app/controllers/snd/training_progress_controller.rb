class Snd::TrainingProgressController < Snd::BaseController
  before_action :authorize_action

  # GET /snd/training/progress
  def index
    skip_policy_scope
    @user = current_user

    interactor = GetUserTrainingProgress.call(user: @user)
    @training_progress = []
    if interactor.success?
      @training_progress = interactor.training_progress
    else
      flash[:error] = interactor.message
      redirect_to snd_training_index_path
    end
  end
end
