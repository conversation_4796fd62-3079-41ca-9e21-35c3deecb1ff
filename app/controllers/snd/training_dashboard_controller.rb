class Snd::TrainingDashboardController < Snd::BaseController
  before_action :authorize_action

  # GET /snd/training/dashboard
  def index
    skip_policy_scope

    interactor = GetRecentUserTrainings.call(user: current_user)
    if interactor.success?
      @recent_user_trainings = interactor.result
    else
      flash[:error] = interactor.message
      redirect_to snd_training_index_path
    end
  end
end
