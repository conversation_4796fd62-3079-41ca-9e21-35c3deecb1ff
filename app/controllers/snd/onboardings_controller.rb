module Snd
  class OnboardingsController < BaseController
    skip_before_action :has_onboarded
    before_action :authorize_action, only: %i[create]

    # GET /snd/onboarding
    def index
      skip_policy_scope
      redirect_to root_path if current_user.onboarded
    end

    # POST /snd/onboarding
    def create
      interactor = CreateSndOnboarding.call(user: current_user, params: onboarding_params) # Interactor
      @user = interactor.user

      if interactor.success?
        redirect_to root_path, notice: t(".success_message")
      else
        flash[:error] = interactor.message
        render :index
      end
    end

    private
      def onboarding_params
        params.require(:onboarding).permit(:ic_number, :first_name, :last_name, :personal_email)
      end
  end
end
