class Snd::LocalesController < ApplicationController
  before_action :authorize_action

  # GET /locale/:lang_code
  def set_locale_cookie
    if I18n.config.available_locales.map(&:to_s).include?(params[:lang_code])
      cookies[:locale] = params[:lang_code]
      redirect_to request.referer || root_path
    else
      flash[:error] = "invalid language code"
      redirect_to request.referer || root_path
    end
  end

  private
    def authorize_action
      authorize :snd, :actions
    end
end
