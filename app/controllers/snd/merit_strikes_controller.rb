class Snd::MeritStrikesController < Snd::BaseController
  include <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>per

  before_action :set_user
  before_action :authorize_action
  before_action :set_strike_status, only: :strike_logs
  before_action :set_date_range_merit_logs, only: %i[merit_logs]
  before_action :set_date_range_strike_logs, only: %i[strike_logs]

  # GET /merit_strikes
  def index
    skip_policy_scope
  end

  # GET /merit_strikes/merit_logs
  def merit_logs
    user_merit = UserMerit.includes(:creator).where("user_id = ?", current_user.id)
    @points_earned = user_merit.sum("point")
    # where status is not rejected
    @points_redeemed = MeritRedemption.where("requested_by = ? and status != 2", current_user.id).sum("total_reward_point")

    @q = policy_scope(user_merit
      .between_date(@start_date, @end_date), policy_scope_class: SndPolicy::Scope)
      .ransack(params[:q])
    @q.sorts = "created_at desc" if @q.sorts.empty?
    @merit_logs = @q.result.page(params[:page])
  end

  # GET /merit_strikes/strike_logs
  def strike_logs
    @q = policy_scope(
        @user.user_strikes.between_date(@start_date, @end_date).without_appealed,
        policy_scope_class: SndCurrentUserPolicy::Scope
      )
      .ransack(params[:q])
    @q.sorts = "created_at desc"
    @strike_logs = @q.result.page(params[:page]).includes(:creator, :appeals)

    unless @status == "all"
      @strike_logs =
        if @status == "approved"
          @strike_logs.select { |log| log.appeal_status == "approved" }
        else
          @strike_logs.reject { |log| log.appeal_status == "approved" }
        end
    end

    @strike_points = @user.strike_point
    @strike_limit = UserStrike.dangerous_status_minimum
  end

  private

    def set_user
      @user = current_user
    end

    def set_strike_status
      @status = params[:status] || "all"
      unless %w[all approved non-approved].include? @status
        flash[:error] = t(".status_incorrect")
        render :strike_logs, status: :unprocessable_entity
      end
    end

    def set_date_range_merit_logs
      validate_date_range
    end

    def set_date_range_strike_logs
      current_country = Apartment::Tenant.current

      if current_country == "my"
        # validate six months
        validate_date_range(180)
      else
        validate_date_range
      end
    end
end
