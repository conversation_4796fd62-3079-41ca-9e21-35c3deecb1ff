class Snd::CreditCardsController < ApplicationController
  before_action :authenticate_user!
  before_action :set_credit_card, only: [:show]

  # GET /snd/credit_cards
  def index
    @loading = false
    @error_message = nil
    @credit_cards = []

    begin
      @loading = true
      response = Service::Fulfillment::CreditCard.find_all

      if response.status == 200 && response.body["response"]
        @credit_cards = response.body["response"]
      else
        @error_message = "Failed to fetch credit cards. Please try again later."
      end
    rescue => e
      Rails.logger.error "Credit Cards API Error: #{e.message}"
      @error_message = "Unable to connect to credit card service. Please try again later."
    ensure
      @loading = false
    end
  end

  # GET /snd/credit_cards/:id
  def show
    # Credit card details fetched in before_action
  end

  private

  def set_credit_card
    card_id = params[:id]

    begin
      # For now, we'll fetch all cards and find the one by index or last_digits
      # since the API doesn't seem to have individual card endpoints
      response = Service::Fulfillment::CreditCard.find_all

      if response.status == 200 && response.body["response"]
        cards = response.body["response"]

        # Try to find by array index first, then by last_digits
        if card_id.match?(/^\d+$/) && card_id.to_i < cards.length
          @credit_card = cards[card_id.to_i]
        else
          @credit_card = cards.find { |card| card["last_digits"] == card_id }
        end

        unless @credit_card
          flash[:error] = "Credit card not found."
          redirect_to snd_credit_cards_path
        end
      else
        flash[:error] = "Failed to fetch credit card details."
        redirect_to snd_credit_cards_path
      end
    rescue => e
      Rails.logger.error "Credit Card Detail API Error: #{e.message}"
      flash[:error] = "Unable to connect to credit card service."
      redirect_to snd_credit_cards_path
    end
  end

  private
end
