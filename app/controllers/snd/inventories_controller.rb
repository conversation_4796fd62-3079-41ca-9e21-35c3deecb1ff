class Snd::InventoriesController < Snd::BaseController
  before_action :set_card, only: [:show_card_details]
  before_action :authorize_action

  # GET /inventory
  def show_items
    @q = current_user.inventories.ransack(params[:q])
    @q.sorts = "names asc" if @q.sorts.empty?
    @items = @q.result.where(inventory_type: :item).page(params[:page])
  end

  # GET /cards
  def show_cards
    @q = current_user.inventories.cards.ransack(params[:q])
    @q.sorts = "updated_at desc" if @q.sorts.empty?
    @cards = @q.result.page(params[:page])
  end

  # GET /inventories/1
  def show_card_details
    # defined on before_action
    # validate card assigned to current_user or not
    unless current_user.inventories.cards.include?(@card)
      flash[:error] = t(".invalid_card")
      redirect_to snd_inventory_cards_path
    end
  end

  private

    def set_card
      @card = Inventory.find(params[:id])
    end
end
