class Snd::PayrollsController < Snd::BaseController
  before_action :authorize_action

  # GET /payrolls
  def index
    skip_policy_scope
    
    metabase_site_url = ENV["METABASE_BASE_URL"]
    metabase_secret_key = ENV["METABASE_SECRET_KEY"]

    # Validate required environment variables
    if metabase_site_url.blank? || metabase_secret_key.blank?
      flash[:error] = "Metabase configuration is missing. Please contact administrator."
      redirect_back(fallback_location: root_path)
      return
    end

    # Get yesterday's date and calculate month boundaries
    yesterday = Date.yesterday
    month_start = yesterday.beginning_of_month
    month_end = yesterday.end_of_month

    payload = {
      resource: { dashboard: dashboard_id },
      params: {
        "email_snd" => [current_user.email],
        "date_start" => month_start.strftime("%Y-%m-%d"),
        "date_end" => month_end.strftime("%Y-%m-%d")
      },
      exp: Time.now.to_i + (60 * 10) # 10 minute expiration
    }
    
    token = JWT.encode(payload, metabase_secret_key)
    @iframe_url = "#{metabase_site_url}/embed/dashboard/#{token}#bordered=true&titled=true"
  end

  private

  def dashboard_id
    country = Apartment::Tenant.current.downcase
    dashboard_id = Redis.current.hget(SND_PAYROLL_DASHBOARD_CONFIG_REDIS_KEY, country)
    if dashboard_id.present?
      dashboard_id.to_i
    else
      DEFAULT_SND_PAYROLL_DASHBOARD_ID
    end
  rescue StandardError => e
    Rails.logger.error("Failed to retrieve dashboard ID from Redis: #{e}")
    DEFAULT_SND_PAYROLL_DASHBOARD_ID
  end
end
