module Snd
  class ParticularFormController < BaseController
    skip_before_action :authenticate_user!
    skip_before_action :has_onboarded

    # GET /snd/particular_form
    def index
      skip_policy_scope
      @particular_form = ParticularForm.new
      @letters = Letter.published.onboardings
    end

    def create
      skip_authorization

      interactor = CreateParticularForm.call(particular_form_params) # Interactor
      @particular_form = interactor.model

      respond_to do |format|
        if interactor.success?
          format.html { redirect_to new_user_session_path, notice: t(".success") }
          format.json { render :show, status: :created, location: @particular_form }
        else
          flash[:error] = interactor.message
          @letters = Letter.published.onboardings
          format.html { render :index }
          format.json { render json: @particular_form.errors, status: :unprocessable_entity }
        end
      end
    end

    def set_layout
      "guest"
    end

    private

      def particular_form_params
        params.require(:particular_form).permit(
          :name, :gender, :birth_date, :nationality, :address, :mobile_phone_number,
          :personal_email, :ic_number, :recommender, :referral_channel_id, :license_number,
          :license_expiry_date, :mobile_phone_model, :bank_id, :bank_account_number,
          :is_own_bank_account, :emergency_contact_detail, :emergency_contact_number,
          :motorcycle_model, :number_of_children, :prev_job_details,
          letters: {}
        )
      end
  end
end
