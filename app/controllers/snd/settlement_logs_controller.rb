class Snd::SettlementLogsController < Snd::BaseController
  include InputHelper
  include TimezoneHelper

  before_action :set_settlement_log, only: [:show, :destroy, :receive, :not_receive]
  before_action :authorize_action, except: [:index]
  before_action :apply_date_range, only: [:my_orders, :my_settlements]
  before_action :set_date_range, only: [:my_orders, :my_settlements]
  before_action :set_sorting_params, only: [:my_settlements]

  def index
    policy_scope(User.where(user: current_user), policy_scope_class: SndPolicy::Scope)

    current_time = DateTime.current
    today_tz = utc_to_local(current_time)
    yesterday_tz = today_tz.yesterday # convert to local time

    # hardcoded by email of user who is used as creator of data from ETL in prod and staging
    @latest_settlement_from_etl = SettlementLog.latest_from_etl

    @previous_balance = UserBalanceCalculator.new(current_user.email, yesterday_tz).net_balance
    @current_balance = UserBalanceCalculator.new(current_user.email, today_tz).net_balance
    @balance_today = UserBalances.new(current_user.email, today_tz, today_tz)
  end

  def show
    requested_settlement = SettlementLog.find(params[:id])
    @settlement_log = SettlementLog.latest_log_state(requested_settlement.log_id)
  end

  def new
    @settlement_log = SettlementLog.new
  end

  def create
    interactor = CreateSettlementRequest.call(
      requester: current_user,
      params: settlement_log_params
    )
    @settlement_log = interactor.model
    respond_to do |format|
      if interactor.success?
        flash[:notice] = t(".success_create")
        flash.keep(:notice) # to keep flash notice after being redirected
        format.html { redirect_to snd_settlement_logs_path }
        format.json { render json: @settlement_log, status: :ok }
      else
        session[:snd_settlement_old_values] = settlement_log_params
        format.html { render :new, status: interactor.status_code }
        format.json { render json: { error: interactor.message }, status: interactor.status_code }
      end
    end
  end

  def destroy
    interactor = CancelSettlementRequest.call(model: @settlement_log)
    respond_to do |format|
      if interactor.success?
        format.html { redirect_to snd_settlement_logs_path, notice: t(".success_delete") }
        format.json { render :show, status: :ok, location: @settlement_log }
      else
        flash[:error] = interactor.message
        format.html { render :new, status: interactor.status_code }
        format.json { render json: @settlement_log.errors, status: interactor.status_code }
      end
    end
  end

  def receive
    interactor = ApproveSettlementRequest.call(model: @settlement_log, approver: current_user) # Interactor
    respond_to do |format|
      if interactor.success?
        format.html { redirect_to snd_settlement_logs_path, notice: t(".request_received") }
        format.json { render :show, status: :ok, location: @settlement_log }
      else
        # Set error message to flash
        flash[:error] = interactor.message
        format.html { render :new, status: interactor.status_code }
        format.json { render json: @settlement_log.errors, status: interactor.status_code }
      end
    end
  end

  def not_receive
    interactor = RejectSettlementRequest.call(
      model: @settlement_log,
      rejecter: current_user,
      reject_reason: params[:reject_reason]
    )

    respond_to do |format|
      if interactor.success?
        format.html { redirect_to snd_settlement_logs_path, notice: t(".request_not_received") }
        format.json { render :show, status: :ok, location: @settlement_log }
      else
        # Set error message to flash
        flash[:error] = interactor.message
        format.html { render :new, status: interactor.status_code }
        format.json { render json: @settlement_log.errors, status: interactor.status_code }
      end
    end
  end

  def my_orders
    my_settlement_balances = SettlementBalance.where(fleet_email: current_user.email).between_date(@start_date, @end_date)
    if params[:search].present?
      my_settlement_balances = SettlementBalance
        .where("(settlement_balances.fleet_email = :fleet_email AND settlement_balances.order_number ilike :search)",
          fleet_email: current_user.email, search: "%#{params[:search]}%")
    end

    @q = policy_scope(my_settlement_balances, policy_scope_class: SndPolicy::Scope)
      .ransack(params[:q])
    @q.sorts = "delivery_time asc" if @q.sorts.empty?
    @settlement_balances = @q.result.page(params[:page])
  end

  def show_my_order
    @order = SettlementBalance.find(params[:id])
  end

  def my_settlements
    my_settlements = SettlementLog.not_pending
      .where("settlement_logs.fleet_email = ? OR settlement_logs.received_by = ?", current_user.email, current_user.id)
      .between_date(@start_date, @end_date)
    if params[:search].present?
      my_settlements = my_settlements.where("(settlement_logs.order_id ilike :search)", search: "%#{params[:search]}%")
    end
    @q = policy_scope(my_settlements, policy_scope_class: SndPolicy::Scope)
      .ransack(params[:q])

    # query for sorting purpose (with dropdown)
    case params[:sorting]
    when "date_updated_desc"
      @q.sorts = "updated_at desc"
    when "date_updated_asc"
      @q.sorts = "updated_at asc"
    else
      @q.sorts = "created_at desc" # if load the page for the first time (params[:sorting] is null)
    end

    @settlement_logs = @q.result.page(params[:page]).reject do |sl|
      # remove settlement log requested which already approved/rejected/cancelled
      if sl.requested?
        !SettlementLog.latest_log_state(sl.log_id).requested?
      end
    end
  end

  private
    def set_date_range
      cookies.signed.permanent["snd_settlement_#{action_name}_start_date".to_sym] = @start_date
      cookies.signed.permanent["snd_settlement_#{action_name}_end_date".to_sym] = @end_date
    end

    def set_settlement_log
      @settlement_log = SettlementLog.find(params[:id])
    end

    def apply_date_range
      # default to 7 days ago if param not present
      @start_date = params[:start_date] || cookies.signed.permanent["snd_settlement_#{action_name}_start_date".to_sym] || 7.days.ago.to_date
      @end_date = params[:end_date] || cookies.signed.permanent["snd_settlement_#{action_name}_end_date".to_sym] || Date.today
    end

    def settlement_log_params
      params[:amount] = strip_currency_mask_and_convert_to_float(params[:amount])
      params.permit(:amount, :balance_type, :description, :attachment, :order_id, :receipt_number, :receiver_email, :tpl_name)
    end

    def authorize_action
      authorize :snd, :actions
    end

    def set_sorting_params
      # set value for the sorting dropdown
      case params[:sorting]
      when "date_updated_desc"
        @selected_sorting = "date_updated_desc"
      when "date_updated_asc"
        @selected_sorting = "date_updated_asc"
      end
    end
end
