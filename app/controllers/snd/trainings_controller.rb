class Snd::TrainingsController < Snd::BaseController
  include TrainingsHelper

  before_action :authorize_action
  before_action :set_user

  before_action :set_type, only: %i[show_modules]
  before_action :set_module, only: %i[show_submodule_content show_summary show_quiz start_quiz request_appeal show_submodules_navigation]
  before_action :set_quiz_session, only: %i[show_question create_answer show_quiz_result]

  helper_method :total_submodules_and_quiz_in_module
  helper_method :percentage_submodule_done
  helper_method :submodule_completed?

  # GET /snd/training
  def index
    source = session[:user_role].present? ? TrainingType.visible_for_role(training_user_role) : TrainingType

    @q = policy_scope(source, policy_scope_class: SndAndAdminPolicy::Scope).ransack(params[:q])
    @q.sorts = "seq asc" if @q.sorts.empty?
    @training_types = @q.result
  end

  # GET /snd/training/:type_slug
  def show_modules
    @user_training ||= fetch_user_trainings
    @modules = @type.training_modules.includes(:training_submodules).order(seq: :asc)
    @completed_quiz_module_ids ||= fetch_completed_quiz_module_ids
    @cw_add_classes = ["training-view"]
  end

  # GET /snd/training_modules/:module_slug/sequence/:seq
  def show_submodule_content
    @all_submodules = @module.training_submodules.content

    validator = ValidateViewSubmodule.call(
      user: @user,
      all_submodules: @all_submodules,
      chosen_seq: params[:seq].to_i
    )
    if validator.failure?
      flash[:error] = validator.message
      if !validator.quiz_session_id.nil?
        redirect_to snd_training_quiz_question_path(
          quiz_session_id: validator.quiz_session_id,
          question_seq: validator.question_seq
        )
      elsif validator.target_seq >= 1
        redirect_to snd_training_submodules_path(module_slug: @module.slug, seq: validator.target_seq)
      else
        redirect_to snd_training_index_path
      end
    else
      @current_submodule = validator.current_submodule
      @next_submodule = validator.next_submodule
      @type = @module.training_type
      @quiz = @module.quiz unless @next_module

      interactor = AddOrUpdateUserTraining.call(
        user: @user,
        submodule: @current_submodule
      )
      unless interactor.success?
        flash[:error] = interactor.message
        redirect_to snd_training_index_path
      end
    end
  end

  # GET /snd/training_modules/:module_slug/quiz
  def show_quiz
    interactor = GetModuleQuiz.call(user: @user, module: @module)

    if interactor.success?
      @type = interactor.training_type
      @module = interactor.module
      @quiz = interactor.quiz
      @last_quiz_attempt = interactor.last_quiz_attempt
    else # interactor.failure
      flash[:error] = interactor.message unless interactor.message.blank?
      if interactor.incomplete_quiz_session_id.present?
        redirect_to snd_training_quiz_question_path(quiz_session_id: interactor.incomplete_quiz_session_id, question_seq: interactor.question_seq)
      else
        redirect_to snd_training_modules_path(interactor.training_type.slug)
      end
    end
  end

  # GET /snd/training_modules/:module_slug/summary
  def show_summary
    interactor = GetModuleSummary.call(user: current_user, module: @module)
    if interactor.failure?
      flash[:error] = interactor.message
      redirect_to action: :show_modules, type_slug: @module.training_type.slug
    else
      @summary = interactor.summary
      @type = interactor.type
      @next_module = interactor.next_module
    end
  end

  # POST /snd/training_modules/:module_slug/start_quiz
  def start_quiz
    interactor = StartQuiz.call(user: current_user, module: @module)

    if interactor.failure?
      flash[:error] = interactor.message unless interactor.message.blank?
      if interactor.incomplete_quiz_session_id.present?
        redirect_to snd_training_quiz_question_path(quiz_session_id: interactor.incomplete_quiz_session_id, question_seq: interactor.question_seq)
      else
        redirect_to snd_training_module_quiz_path(interactor.module.slug)
      end
    else
      redirect_to snd_training_quiz_question_path(quiz_session_id: interactor.quiz_session.id, question_seq: 1)
    end
  end

  # GET /snd/quiz_sessions/:quiz_session_id/question_sequence/:question_seq
  def show_question
    validator = ValidateAccessQuizQuestion.call(
      user: current_user,
      quiz_session: @quiz_session,
      chosen_seq: params[:question_seq].to_i,
      answered: params[:answered] == "true"
    )
    if validator.failure?
      flash[:error] = validator.message
      if validator.target_seq >= 1
        redirect_to action: :show_question, quiz_session_id: params[:quiz_session_id], question_seq: validator.target_seq
      else
        redirect_to action: :index
      end
    else
      @question_seq = params[:question_seq].to_i
      @question = validator.question.body
      @answer_choices = validator.answer_choices
      @answered = validator.answered
      @user_answers = validator.user_answers if validator.answered
      @module = @quiz_session.quiz.training_module
    end
  end

  # POST /snd/quiz_sessions/:quiz_session_id/question_sequence/:question_seq
  def create_answer
    interactor = SubmitQuizAnswer.call(
      user: current_user,
      quiz_session: @quiz_session,
      chosen_seq: params[:question_seq].to_i,
      answer_id: params[:answer_id].to_i
    )
    if interactor.failure?
      flash[:error] = interactor.message

      if interactor.redirect_to_result?
        redirect_to snd_training_quiz_result_path(@quiz_session.id)
      else
        redirect_to snd_training_quiz_question_path(quiz_session_id: params[:quiz_session_id], question_seq: params[:question_seq])
      end
    else
      flash[:notice] = t(".answer_saved")
      redirect_to snd_training_quiz_question_path(quiz_session_id: params[:quiz_session_id], question_seq: params[:question_seq], answered: true)
    end
  end

  # GET /snd/quiz_sessions/:quiz_session_id/result
  def show_quiz_result
    validator = ValidateAccessQuizScore.call(user: current_user, quiz_session: @quiz_session)
    if validator.failure?
      flash[:error] = validator.message
      redirect_to action: :show_question, quiz_session_id: @quiz_session.id, question_seq: @quiz_session.question_count
    else
      @total = validator.total
      @score = validator.score
      @pass_quiz = validator.pass_quiz
      @module = validator.module
      @type = validator.type
    end
  end

  # POST /snd/training_modules/:module_slug/request_appeal
  def request_appeal
    interactor_quiz = GetModuleQuiz.call(user: @user, module: @module)

    if interactor_quiz.success?
      interactor_appeal = CreateQuizAttemptAppeal.call(user: @user, quiz: interactor_quiz.quiz)

      if interactor_appeal.success?
        flash[:notice] = t(".appeal_submitted")
      else
        flash[:error] = interactor_appeal.message unless interactor_appeal.message.blank?
      end
      redirect_to snd_training_module_quiz_path(module_slug: @module.slug)
    else
      flash[:error] = interactor_quiz.message unless interactor_quiz.message.blank?
      redirect_to snd_training_modules_path(interactor_quiz.training_type.slug)
    end
  end

  # GET /snd/training_modules/:module_slug/submodules_navigation
  def show_submodules_navigation
    @user_training ||= fetch_user_trainings
    @completed_quiz_module_ids ||= fetch_completed_quiz_module_ids
  end

  # GET /snd/training/search
  def search
    user_role = session[:user_role]
    @keyword = params[:keyword]
    @result = nil

    if @keyword
      interactor = SearchTrainingByKeyword.call(keyword: @keyword, user_role: user_role)
      @result = interactor.result
      flash[:notice] = interactor.message
    end

    render :search, status: :ok
  end

  private

    def set_user
      @user = current_user
    end

    def set_type
      @type = TrainingType.friendly.find(params[:type_slug])
    rescue ActiveRecord::RecordNotFound
      flash[:error] = I18n.t("snd.trainings.set_type.type_not_found")
      redirect_to snd_training_index_path
    end

    def set_module
      @module = TrainingModule.friendly.find(params[:module_slug])
    rescue ActiveRecord::RecordNotFound
      flash[:error] = I18n.t("snd.trainings.set_content.content_not_found")
      redirect_to snd_training_index_path
    end

    def set_quiz_session
      @quiz_session = QuizSession.find(params[:quiz_session_id])
    rescue ActiveRecord::RecordNotFound
      flash[:error] = I18n.t("snd.trainings.e.quiz_session_not_found")
      redirect_to snd_training_index_path
    end

    # Helpers for show_modules view
    def count_submodules_and_quiz_taken_in_module(modul)
      submodule_ids = modul.training_submodules.pluck(:id)
      @user_training ||= fetch_user_trainings
      completed_submodules_count = @user_training.select { |ut| submodule_ids.include? ut.training_submodule_id }.length

      @completed_quiz_module_ids ||= fetch_completed_quiz_module_ids
      completed_quiz_count = @completed_quiz_module_ids.include?(modul.id) ? 1 : 0

      completed_submodules_count + completed_quiz_count
    end

    # Helpers for show_modules view
    def total_submodules_and_quiz_in_module(modul)
      modul.submodules_count + 1 # 1 quiz included
    end

    # Helpers for show_modules view
    # TODO: there is a chance calculation exceed 100
    def percentage_submodule_done(modul)
      total = total_submodules_and_quiz_in_module(modul)
      count = count_submodules_and_quiz_taken_in_module(modul)
      if total > 0
        (count * 100.0 / total).to_i
      else
        # module doesn't have submodules
        0
      end
    end

    # Helpers for show_modules view
    def submodule_completed?(submodule)
      @user_training.select { |ut| ut.training_submodule_id == submodule.id }.any?
    end

    def fetch_completed_quiz_module_ids
      @user.quiz_sessions.includes(:quiz)
        .select("DISTINCT ON (quiz_sessions.quiz_id) quiz_sessions.*")
        .order("quiz_sessions.quiz_id, quiz_sessions.created_at DESC")
        .select { |qs| qs.passed? }
        .map { |qs| qs.quiz&.training_module_id }.compact
    end

    def fetch_user_trainings
      @user.user_trainings.joins(:training_submodule) # user trainings whose submodule not deleted
    end

    def training_user_role
      session[:user_role]
    end
end
