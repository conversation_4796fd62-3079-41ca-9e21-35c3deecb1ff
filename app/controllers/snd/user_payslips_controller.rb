class Snd::UserPayslipsController < Snd::BaseController
  include Date<PERSON>ilterHelper

  before_action :set_payslip, only: %i[show export_payslip]
  before_action :set_start_end_dates, only: :index
  before_action :authorize_action

  # GET /payslips
  def index
    @q = policy_scope(Payslip
      .left_joins(:payslip_batch)
      .where("payslip_batches.published_at IS NOT NULL
        AND payslip_batches.start_date >= ? AND payslip_batches.end_date <= ?",
        @start_date, @end_date),
      policy_scope_class: SndCurrentUserPolicy::Scope
    ).ransack(params[:q])
    @q.sorts = "payslip_batches.start_date desc" if @q.sorts.empty?
    @payslips = @q.result.page(params[:page])
  end

  # GET /user_payslip
  def show
    # defined in before_action
  end

  # GET /user_payslips/:id/export
  def export_payslip
    interactor = ExportPayslip.call(user_adjustments: @user_adjustments, payslip: @payslip)

    if interactor.success?
      send_data interactor.pdf, filename: interactor.filename
    else
      respond_to do |format|
        flash[:error] = interactor.message
        format.html { render :show, status: interactor.status_code }
      end
    end
  end

  private

    # Use callbacks to share common setup or constraints between actions.
    def set_payslip
      @payslip = authorize Payslip.find(params[:id]), policy_class: SndCurrentUserPolicy
      @user_adjustments = UserAdjustment.where(payslip_id: @payslip.id)
                                   .includes(:adjustment_condition)
                                   .order("adjustment_conditions.number ASC")
    rescue ActiveRecord::RecordNotFound
      flash[:error] = I18n.t("snd.user_payslips.e.payslip_not_found")
      redirect_to snd_payslips_path
    end

    def set_start_end_dates
      validate_params_date_range(max_days: 92) # until three months

      # to display at least lasth month's payslip (if any)
      @start_date = (params[:start_date] || Date.current.beginning_of_month - 1.month).to_date
      @end_date = (params[:end_date] || Date.current.end_of_month).to_date
    end
end
