class Snd::MyProfilesController < Snd::BaseController
  before_action :authorize_action
  before_action :set_my_profile, only: %i[edit update]

  def edit
    if current_user.my_profile.present?
      @my_profile = current_user.my_profile
      return
    end

    particular_form = ParticularForm.find_by(personal_email: current_user.personal_email)
    if particular_form.present?
      @my_profile = particular_form
      return
    end

    @my_profile = MyProfile.new
  end

  def update
    interactor = UpdateMyProfile.call(
      model: @my_profile,
      params: my_profile_params,
      creator: current_user

    )
    @my_profile = interactor.model

    respond_to do |format|
      if interactor.success?
        format.html { redirect_to snd_edit_my_profile_path, notice: t(".success") }
        format.json { render :edit, status: :ok, location: @my_profile }
      else
        flash[:error] = interactor.message
        format.html { render :edit }
        format.json { render json: @my_profile.errors, status: :unprocessable_entity }
      end
    end
  end

  private

    def set_my_profile
      @my_profile = current_user.my_profile
    end

    def my_profile_params
      params.require(:my_profile).permit(
        :gender,
        :birth_date,
        :nationality,
        :address,
        :mobile_phone_number,
        :recommender,
        :referral_channel_id,
        :license_number,
        :license_expiry_date,
        :mobile_phone_model,
        :bank_id,
        :bank_account_number,
        :is_own_bank_account,
        :emergency_contact_detail,
        :emergency_contact_number,
        :motorcycle_model,
        :number_of_children,
        :prev_job_details
      )
    end
end
