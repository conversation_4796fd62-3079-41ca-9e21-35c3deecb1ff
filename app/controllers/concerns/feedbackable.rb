module Feedbackable
  extend ActiveSupport::Concern

  included do
    before_action :queue_feedback
    before_action :get_feedback
  end

  def queue_feedback
    if ["export"].include?(action_name) || params[:commit].present?
      interactor = QueueFeedback.call(
        user: current_user,
        controller: controller_path,
        action: action_name
      )

      if interactor.failure?
        controller_full_name = controller_path.split("/").map(&:camelize).join("::")
        Rails.logger.error("#{controller_full_name}.queue_feedback: #{interactor.message}")
      end
    end
  end

  def get_feedback
    interactor = GetFeedback.call(
      user: current_user,
      controller: controller_path
    )

    if interactor.failure?
      controller_full_name = controller_path.split("/").map(&:camelize).join("::")
      Rails.logger.error("#{controller_full_name}.get_feedback: #{interactor.message}")
    else
      @feedback = interactor.model
    end
  end
end
