module DateFilterHelper
  extend ActiveSupport::Concern

  private

    def validate_params_date_range(max_days: 31)
      return if params[:start_date].blank? && params[:end_date].blank?

      if params[:start_date].present? && params[:end_date].present?
        start_date = params[:start_date].to_date
        end_date = params[:end_date].to_date

        difference = (end_date - start_date).to_i

        if difference < 0
          set_redirect_to_page(I18n.t("e.end_date_invalid"))
        elsif difference > max_days
          set_redirect_to_page(I18n.t("e.date_range_wider_than_expected", max_days: max_days))
        end
      else
        set_redirect_to_page(I18n.t("e.start_end_date_nil"))
      end
    rescue ArgumentError => e
      context.fail!(message: e.message)
    end

    def validate_date_range(max_days = 31)
      validate_params_date_range(max_days: max_days)

      # default to 1 month ago if param not present
      @start_date = params[:start_date] || 1.months.ago.to_date
      @end_date = params[:end_date] || Date.today
      @today = Date.today
    end

    def set_redirect_to_page(message)
      flash[:error] = message
      redirect_to "#{request.base_url}#{request.path}"
    end
end
