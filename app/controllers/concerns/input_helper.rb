module InputHelper
  extend ActiveSupport::Concern

  private

    def strip_currency_mask(amount)
      pattern = Regexp.new("\\#{helpers.current_country.currency[:delimiter]}")
      amount.gsub(pattern, "").strip
    end

    def strip_currency_mask_and_convert_to_float(amount)
      return amount if amount.is_a? Float

      amount_without_mask = strip_currency_mask(amount)
      # need to convert "," string to ".".
      # some currency string with "," separator will
      # lose decimal precision when being converted to float
      amount_without_mask.gsub(/\,/, ".").strip.to_f
    end
end
