require Rails.root.join("app", "queries", "search", "available_keywords_query.rb").to_s

module Api
  module Search
    class TagsController < ActionController::Base
      def index
        @tags = ::Search::AvailableKeywordsQuery.call(context: params[:context], keyword: params[:q])
                                                .containing_keyword

        respond_to do |format|
          format.json { render json: @tags, status: :ok }
        end
      end
    end
  end
end
