module FulfillmentConfig
  class GeAndTplsController < BaseController
    before_action :authorize_action

    # GET/fulfillment_config/stock_location/ge_and_tpl
    def index
      skip_policy_scope
      @stock_locations ||= get_stock_locations
    end

    # GET /fulfillment_config/stock_locations/ge_and_3pl/:stock_location_id/edit
    def edit
      interactor = ::FulfillmentConfig::GetStockLocation.call(id: params[:stock_location_id])
      if interactor.success?
        @stock_location = interactor.result
      else
        redirect_to fulfillment_config_ge_and_tpls_path, alert: interactor.message
      end
    end

    def update
      interactor = ::FulfillmentConfig::UpdateStockLocation.call(
        id: params[:stock_location_id],
        stock_location: stock_location_params.to_h,
        updater: current_user
      )
      if interactor.success?
        redirect_to fulfillment_config_ge_and_tpls_path, notice: t(".success")
      else
        session[:fulfillment_ge_and_tpls_validation] = interactor.errors
        session[:fulfillment_ge_and_tpls_old_values] = stock_location_params
        redirect_to fulfillment_config_ge_and_tpl_edit_path(params[:stock_location_id]), alert: interactor.message
      end
    end

    private
      def authorize_action
        authorize :admin, :actions
      end

      def stock_location_params
        params.require(:stock_location).permit(
          :enable_grab_express,
          :enable_grab_express_cod,
          :tpl_enabled,
          :ship_distance_threshold,
          :enable_lalamove,
          :enable_delyva,
          preferences: {}
        )
      end
  end
end
