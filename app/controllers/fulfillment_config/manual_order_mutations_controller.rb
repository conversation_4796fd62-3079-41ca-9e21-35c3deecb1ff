module FulfillmentConfig
  class ManualOrderMutationsController < BaseController
    before_action :authorize_action

    def index
      skip_policy_scope

      @order_number = params[:search_string]
      @batches = []
      @shipment = nil
      @delivery_job = nil

      respond_to do |format|
        format.html do
          if params[:search_string]
            interactor = ::FulfillmentConfig::GetActiveJobFromBatches.call(order_number: @order_number, user_email: nil)
            if interactor.success?
              @batches = interactor.batches
              @shipment = interactor.shipment
              @stock_location = interactor.stock_location
              @delivery_job = interactor.delivery_job
              @current_delivery_state = interactor.current_delivery_state

              fetch_log_interactor = ::FulfillmentConfig::GetOrderMutationLog.call(order_number: @order_number)
              @logs = fetch_log_interactor.success? ? fetch_log_interactor.logs : []

              if interactor.stock_location
                fetch_stores_interactor = ::FulfillmentConfig::GetStockLocationsBySupplier.call(supplier_id: interactor.stock_location["supplier_id"])
                @stores = fetch_stores_interactor.success? ? fetch_stores_interactor.stock_locations : []
              end
            else
              flash[:error] = interactor.message
              redirect_to fulfillment_config_manual_order_mutation_path
            end
          end
        end
      end
    end

    def update_stock_location
      interactor = ChangeStore.call(
        actor_id: current_user.id,
        order_number: params[:order_number],
        shipment_number: update_stock_location_params[:shipment_number],
        origin_stock_location: update_stock_location_params[:origin_stock_location],
        target_stock_location: update_stock_location_params[:target_stock_location],
        stock_location_id: update_stock_location_params[:stock_location_id],
        reason: update_stock_location_params[:reason],
        reason_description: update_stock_location_params[:reason_description],
        remove_distance_restriction: update_stock_location_params[:remove_distance_restriction] == "true"
      )

      respond_to do |format|
        format.html do
          if interactor.success?
            flash[:notice] = t("fulfillment_config.manual_order_mutations.index.update_successfully",
                               order_number: interactor.order_number)
          else
            flash[:error] = interactor.message
          end
          redirect_to fulfillment_config_manual_order_mutation_path(search_string: interactor.order_number)
        end
      end
    end

    private

      def authorize_action
        authorize :admin, :actions
      end

      def update_stock_location_params
        params.require(:update_stock_location).permit(:shipment_number,
                                              :origin_stock_location,
                                              :target_stock_location,
                                              :stock_location_id,
                                              :reason,
                                              :reason_description,
                                              :remove_distance_restriction)
      end
    # end of private block
  end
end
