module FulfillmentConfig
  class OnDemandClustersController < BaseController
    before_action :authorize_action

    # GET /fulfillment_config/on_demand_clusters
    def index
      skip_policy_scope

      interactor = ::FulfillmentConfig::GetOnDemandClustersByCountry.call
      if interactor.success?
        @on_demand_clusters = interactor.result
      else
        @on_demand_clusters = []
        flash[:error] = interactor.message
        render "fulfillment_config/on_demand_clusters/index", status: interactor.status_code
      end
    end

    # POST /fulfillment_config/on_demand_clusters
    def create
      interactor = ::FulfillmentConfig::CreateOnDemandCluster.call(params: on_demand_cluster_params)
      if interactor.success?
        flash[:notice] = t(".create_success")
        redirect_to fulfillment_config_on_demand_clusters_path
      else
        flash[:error] = interactor.message
        render "fulfillment_config/on_demand_clusters/index", status: interactor.status_code
      end
    end

    # PUT /fulfillment_config/on_demand_clusters/:id
    def update
      interactor = ::FulfillmentConfig::UpdateOnDemandCluster.call(on_demand_cluster_id: params[:id], params: on_demand_cluster_params)
      if interactor.success?
        flash[:notice] = t(".update_success")
        redirect_to fulfillment_config_on_demand_clusters_path
      else
        flash[:error] = interactor.message
        render "fulfillment_config/on_demand_clusters/index", status: interactor.status_code
      end
    end

    private

      def authorize_action
        authorize :admin, :actions
      end

      def on_demand_cluster_params
        params.require(:on_demand_cluster).permit(:name)
      end
  end
end
