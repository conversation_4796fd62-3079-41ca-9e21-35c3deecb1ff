module FulfillmentConfig
  class ShiftsController < BaseController
    before_action :authorize_action

    def index
      skip_policy_scope
      @show_inactive = params[:show_inactive]
      @cluster_shifts = []
      @stock_locations ||= get_stock_locations
      status_code = :ok
      if params[:date].present? && params[:store_external_id].present?
        @date = params[:date]
        @store_external_id = params[:store_external_id]

        shifts_interactor = ::FulfillmentConfig::GetShiftsByStore.call(date: @date, store_external_id: @store_external_id, show_inactive: @show_inactive)
        if shifts_interactor.success?
          if shifts_interactor.result.first.present? && shifts_interactor.result.first.try(:[], "slot_type") != "LONGER_DELIVERY"
            flash[:error] = I18n.t("fulfillment_config.shifts.e.wrong_slot_type")
            status_code = :internal_server_error
          else
            @cluster_shifts = shifts_interactor.result

            slots_interactor = ::FulfillmentConfig::GetSlotsByStoreV2.call(
              date: @date,
              store_external_id: @store_external_id,
              show_inactive: @show_inactive
            )
            if slots_interactor.success?
              @cluster_slots = slots_interactor.result
            else
              flash[:warning] = I18n.t("fulfillment_config.slots.e.cannot_fetch_slots", error: slots_interactor.message)
              status_code = slots_interactor.status_code
            end

            flash[:info] = I18n.t("fulfillment_config.shifts.controller.empty_result") if @cluster_shifts.empty?
          end
        else
          flash[:error] = I18n.t("fulfillment_config.shifts.e.cannot_fetch_shifts", error: shifts_interactor.message)
          status_code = shifts_interactor.status_code
        end
      end
      render :index, status: status_code
    end

    def upload
    end

    # POST /fulfillment_config/shifts
    def create
      interactor = ImportShifts.call(params: shifts_params)
      respond_to do |format|
        if interactor.success?
          format.html { redirect_to fulfillment_config_shifts_upload_path, notice: I18n.t("fulfillment_config.shifts.controller.uploaded") }
        else
          flash[:error] = interactor.message
          format.html { render :upload, status: interactor.status_code }
        end
      end
    end

    def download_format_file
      file = File.join(Rails.root, "/spec/fixtures/shift_upload.csv")
      send_file file, filename: "shift_template.csv"
    end

    # DELETE /fulfillment_config/shifts/:shift_id
    def destroy
      interactor = DeleteShift.call(shift_id: params[:shift_id])

      index_url = fulfillment_config_shifts_path(
        show_inactive: params[:show_inactive],
        date: params[:date],
        store_external_id: params[:store_external_id]
      )
      if interactor.success?
        redirect_to index_url, notice: I18n.t("fulfillment_config.shifts.controller.deleted")
      else
        flash[:error] = interactor.message
        redirect_to index_url
      end
    end

    def edit
      @shifts = []
      if params[:date].present? && params[:store_external_id].present? && params[:type].present? && %w(driver shopper).include?(params[:type])
        @date = params[:date]
        @store_external_id = params[:store_external_id]
        @type = params[:type]
        status_code = :ok

        interactor = ::FulfillmentConfig::GetShiftsForStoreAndDate.call(date: @date, store_external_id: @store_external_id, type: @type, show_inactive: true)
        if interactor.success?
          if interactor.result.present? && interactor.result[:slot_type] != "LONGER_DELIVERY"
            flash[:error] = I18n.t("fulfillment_config.shifts.e.wrong_slot_type")
            status_code = :internal_server_error
          else
            @store_name = interactor.result[:store_name]
            @shifts = interactor.result[:shifts]
            @time_zone = interactor.result[:time_zone]
            flash[:info] = I18n.t("fulfillment_config.shifts.controller.empty_result") if @shifts.empty?
          end
        else
          flash[:error] = I18n.t("fulfillment_config.shifts.e.cannot_fetch_shifts", error: interactor.message)
          status_code = interactor.status_code
        end
      end
      render :edit, status: status_code
    end

    def update
      interactor = ::FulfillmentConfig::UpdateShifts.call(params: update_shift_params, updater: current_user)

      if interactor.success?
        flash[:info] = I18n.t("fulfillment_config.shifts.info.update_success")
        redirect_to fulfillment_config_shifts_edit_path(
          store_external_id: params[:stock_location_id],
          date: params[:date],
          type: params[:shift_type].downcase
        )
      else
        @store_external_id = params[:stock_location_id]
        @date = params[:date]
        @type = params[:shift_type].downcase
        @store_name = params[:stock_location_name]
        @time_zone = params[:time_zone]
        @shifts = params[:shifts]
        @formatted = true
        flash[:error] = I18n.t("fulfillment_config.shifts.e.cannot_update_shifts")
        session[:fulfillment_config_errors] = interactor.message
        render :edit, status: interactor.status_code
      end
    end


    private

      def authorize_action
        authorize :admin, :actions
      end

      def shifts_params
        params.permit(:file)
      end

      def update_shift_params
        params.permit(
          :stock_location_id,
          :stock_location_name,
          :shift_type,
          :date,
          :time_zone,
          shifts: [
            :id,
            :start_time,
            :end_time,
            :count
          ]
        )
      end
  end
end
