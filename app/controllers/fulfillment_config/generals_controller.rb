module FulfillmentConfig
  class GeneralsController < BaseController
    before_action :authorize_action
    before_action :set_suppliers, :set_states, :set_clusters, only: [:edit]

    # GET /fulfillment_config/stock_location/generals
    def index
      skip_policy_scope
      @stock_locations ||= get_stock_locations
    end

    # GET /fulfillment_config/stock_locations/generals/:stock_location_id/edit
    def edit
      if @suppliers.nil? || @states.nil? || @clusters.nil?
        alerts = [ @suppliers_alert, @states_alert, @clusters_alert ]
        flash[:alert] = alerts.compact.join(", ")
        render :index, status: :bad_gateway
      end

      interactor = ::FulfillmentConfig::GetStockLocation.call(id: params[:stock_location_id])

      if interactor.success?
        @stock_location = interactor.result
      else
        flash[:alert] = interactor.message
        render :index, status: interactor.status_code
      end
    end

    # PUT /fulfillment_config/stock_locations/generals/:stock_location_id
    def update
      interactor = ::FulfillmentConfig::UpdateStockLocation.call(
        id: params[:stock_location_id],
        stock_location: stock_location_params.to_h,
        updater: current_user
      )

      if interactor.success?
        redirect_to fulfillment_config_generals_path, notice: t(".success", store_name: stock_location_params[:name])
      else
        session[:fulfillment_generals_validation] = interactor.errors
        session[:fulfillment_generals_old_values] = stock_location_params
        flash[:alert] = interactor.message
        redirect_to fulfillment_config_general_edit_path(params[:stock_location_id])
      end
    end

    private
      def authorize_action
        authorize :admin, :actions
      end

      def set_suppliers
        iso_name = Apartment::Tenant.current.upcase
        cache_key = "suppliers_#{ iso_name }"
        @suppliers = Rails.cache.fetch(cache_key, skip_nil: true, expires_in: 15.minute) do
          get_suppliers = ::FulfillmentConfig::GetSuppliers.call
          if get_suppliers.success?
            get_suppliers.result
          else
            @suppliers_alert = get_suppliers.message
            break
          end
        end
      end

      def set_states
        iso_name = Apartment::Tenant.current.upcase
        cache_key = "states_#{ iso_name }"
        @states = Rails.cache.fetch(cache_key, skip_nil: true, expires_in: 15.minute) do
          get_states = ::FulfillmentConfig::GetStates.call
          if get_states.success?
            get_states.result
          else
            @states_alert = get_states.message
            break
          end
        end
      end

      def set_clusters
        iso_name = Apartment::Tenant.current.upcase
        cache_key = "clusters_#{ iso_name }"
        @clusters = Rails.cache.fetch(cache_key, skip_nil: true, expires_in: 15.minute) do
          get_clusters = ::FulfillmentConfig::GetClustersByCountry.call
          if get_clusters.success?
            get_clusters.result
          else
            @clusters_alert = get_clusters.message
            break
          end
        end
      end

      def stock_location_params
        params.require(:stock_location).permit(
          :name,
          :code,
          :active,
          :supplier_id,
          :open_at,
          :close_at,
          :ask_receipt_number,
          :enable_cut_off,
          :ddf_type,
          :type,
          :enable_chat,
          :state_id,
          :cluster_id,
          :address1,
          :address2,
          :lat,
          :lon,
          preferences: {}
        )
      end
  end
end
