module FulfillmentConfig
  class ManualAssignmentsController < BaseController
    before_action :authorize_action

    def index
      skip_policy_scope

      if params[:search_string].present?
        params[:search_string] = params[:search_string].strip
      end

      @order_number = nil
      @user_email = nil
      if params[:search_type] == "order_number"
        @order_number = params[:search_string]
      elsif params[:search_type] == "user_email"
        @user_email = params[:search_string]
      end

      # Default Assigmnet Log period
      @start_date = 6.months.ago.to_date
      @end_date = Date.today
      @datatable_options = [@start_date, @end_date, @user_email, @order_number].map(&:to_s).to_json

      @batches = []
      return if @order_number.blank? && @user_email.blank?

      interactor = ::FulfillmentConfig::GetActiveBatches.call(order_number: @order_number, user_email: @user_email)
      if interactor.success?
        @batches = interactor.result
      else
        flash[:error] = interactor.message
        render :index, status: interactor.status_code
      end
    end

    def view_available_fleets
      @batch_id = params[:batch_id]
      @order_number = params[:order_number]
      @origin_fleet_id = params[:origin_fleet_id]
      @origin_fleet_email = params[:origin_fleet_email]
      @assignable_type = params[:assignable_type]

      interactor = ::FulfillmentConfig::GetAvailableFleet.call(batch_id: @batch_id)

      if interactor.success?
        @users = interactor.result
      else
        flash[:error] = interactor.error_messages[0]
        redirect_to fulfillment_config_manual_assignment_path
      end
    end

    def update_batch_user
      batch_id = update_batch_user_params[:batch_id]
      email = update_batch_user_params[:email]
      user_id = update_batch_user_params[:user_id]
      # manual assignment log attributes
      actor_id = current_user.id
      order_number = update_batch_user_params[:order_number]
      origin_fleet_id = update_batch_user_params[:origin_fleet_id]
      origin_fleet_email = update_batch_user_params[:origin_fleet_email]
      assignable_type = update_batch_user_params[:assignable_type]
      reason = update_batch_user_params[:reason]
      reason_description = update_batch_user_params[:other_reason]

      argv = {
        batch_id: batch_id,
        order_number: order_number,
        actor_id: actor_id,
        user_id: user_id,
        email: email,
        origin_fleet_id: origin_fleet_id,
        origin_fleet_email: origin_fleet_email,
        assignable_type: assignable_type,
        reason: reason,
        reason_description: reason_description
      }
      interactor = UpdateBatchFleet.call(argv)

      index_url = fulfillment_config_manual_assignment_path(search_type: "user_email", search_string: email)
      flash_message = interactor.message.present? ? interactor.message : interactor.response_message
      if interactor.success?
        flash[:info] = flash_message
        redirect_to index_url
      else
        flash[(interactor.assignment_success? ? :info : :error)] = flash_message
        redirect_to fulfillment_config_manual_assignment_path
      end
    end

    def switch_to_hf
      argv = {
        batch_id: switch_to_hf_params[:batch_id],
        shipment_number: switch_to_hf_params[:shipment_number],
        order_number: switch_to_hf_params[:order_number],
        actor_id: current_user.id,
        user_id: current_user.id,
        email: current_user.email,
        assignable_type: "delivery_batch",
        reason: "switch_to_hf"
      }

      interactor = SwitchTplToFleetAndLog.call(argv)

      redirect_url = if switch_to_hf_params[:redirect_url].present?
        switch_to_hf_params[:redirect_url]
      else
        fulfillment_config_manual_assignment_path
      end
      if interactor.success?
        flash[:info] = I18n.t(:switched_to_hf_successfully,
                              scope: "fulfillment_config.manual_assignments.info",
                              batch_id: interactor.batch_id)
      else
        flash[:error] = interactor.message
      end

      redirect_to redirect_url
    end

    def switch_to_tpl
      argv = {
        batch_id: switch_to_tpl_params[:batch_id],
        order_number: switch_to_tpl_params[:order_number],
        tpl_vendor: switch_to_tpl_params[:tpl_vendor],
        tpl_vehicle_type: switch_to_tpl_params[:tpl_vehicle_type],
        actor_id: current_user.id,
        user_id: current_user.id,
        email: current_user.email,
        assignable_type: "delivery_batch",
        reason: "switch_to_#{switch_to_tpl_params[:tpl_vendor]}_#{switch_to_tpl_params[:tpl_vehicle_type]}"
      }

      interactor = SwitchFleetToTplAndLog.call(argv)

      redirect_url = if switch_to_tpl_params[:redirect_url].present?
        switch_to_tpl_params[:redirect_url]
      else
        fulfillment_config_manual_assignment_path
      end
      if interactor.success?
        flash[:info] = I18n.t(:switched_to_tpl_successfully, scope: "fulfillment_config.manual_assignments.info")
      else
        flash[:error] = interactor.message
      end

      redirect_to redirect_url
    end

    def unpool
      argv = {
        batch_id: unpool_params[:batch_id],
        shipment_number: unpool_params[:shipment_number],
        order_number: unpool_params[:order_number],
        actor_id: current_user.id,
        user_id: current_user.id,
        email: current_user.email,
        assignable_type: unpool_params[:assignable_type],
        reason: "unpool"
      }

      interactor = UnpoolAndLog.call(argv)

      redirect_url = if unpool_params[:redirect_url].present?
        unpool_params[:redirect_url]
      else
        fulfillment_config_manual_assignment_path
      end
      if interactor.success?
        flash[:info] = I18n.t(:unpool_successfully, scope: "fulfillment_config.manual_assignments.info")
      else
        flash[:error] = interactor.message
      end

      redirect_to redirect_url
    end

    private

      def authorize_action
        authorize :admin, :actions
      end

      def update_batch_user_params
        params.permit(:batch_id, :email, :user_id, :order_number, :origin_fleet_email, :origin_fleet_id, :assignable_type, :reason, :other_reason)
      end

      def switch_to_hf_params
        params.permit(:batch_id, :shipment_number, :order_number, :redirect_url)
      end

      def switch_to_tpl_params
        params.permit(:batch_id, :order_number, :tpl_vendor, :tpl_vehicle_type, :redirect_url)
      end

      def unpool_params
        params.permit(:batch_id, :shipment_number, :order_number, :assignable_type, :redirect_url)
      end
  end
end
