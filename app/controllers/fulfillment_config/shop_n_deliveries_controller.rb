module FulfillmentConfig
  class ShopNDeliveriesController < BaseController
    before_action :authorize_action

    # GET /fulfillment_config/stock_location/shop_n_deliveries
    def index
      skip_policy_scope
      @stock_locations ||= get_stock_locations
    end

    # GET /fulfillment_config/stock_location/shop_n_deliveries/:stock_location_id/edit
    def edit
      interactor = ::FulfillmentConfig::GetStockLocation.call(id: params[:stock_location_id])
      if interactor.success?
        @stock_location = interactor.result
      else
        redirect_to fulfillment_config_shop_n_deliveries_path, alert: interactor.message
      end
    end

    # PUT /fulfillment_config/stock_location/shop_n_deliveries/:stock_location_id
    def update
      interactor = ::FulfillmentConfig::UpdateStockLocation.call(
        id: params[:stock_location_id],
        stock_location: stock_location_params.to_h,
        updater: current_user
      )
      if interactor.success?
        redirect_to fulfillment_config_shop_n_deliveries_path, notice: t(".success")
      else
        session[:fulfillment_shop_n_deliveries_validation] = interactor.errors
        session[:fulfillment_shop_n_deliveries_old_values] = stock_location_params
        redirect_to(
          fulfillment_config_shop_n_delivery_edit_path(params[:stock_location_id]),
          alert: interactor.message
        )
      end
    end

    private

      def authorize_action
        authorize :admin, :actions
      end

      def stock_location_params
        params.require(:stock_location).permit(
          :shopper_average_picking_time_per_uniq_item,
          :shopper_queue_replacement_time,
          :shopper_handover_to_driver_time,
          :max_shopping_volume,
          :shopping_batch_notified_offset,
          :enable_multi_batch_shopping,
          :prepicking_offset,
          :crossday_prepicking_enabled,
          :max_delivery_volume,
          :max_delivery_handover,
          :delivery_batch_notified_offset,
          preferences: [
            :express_set_slot_prioritize_on_demand,
            :maximum_traveled_distance
          ]
        )
      end
  end
end
