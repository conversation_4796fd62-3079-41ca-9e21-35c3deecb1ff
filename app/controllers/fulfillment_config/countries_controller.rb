module FulfillmentConfig
  class CountriesController < BaseController
    before_action :authorize_action

    # GET /fulfillment_config/countries
    def index
      skip_policy_scope
      interactor = ::FulfillmentConfig::GetCountries.call
      if interactor.success?
        @countries = interactor.result
      else
        @countries = []
        flash[:error] = interactor.message
      end
    end

    # GET /fulfillment_config/countries/:iso_name/edit
    def edit
      if Apartment::Tenant.current.upcase != params[:iso_name]
        redirect_to fulfillment_config_countries_path, alert: t(".invalid_country")
      end

      interactor = ::FulfillmentConfig::GetCountry.call(iso_name: params[:iso_name])
      if interactor.success?
        @country = interactor.result
      else
        redirect_to fulfillment_config_countries_path, alert: interactor.message
      end
    end

    # PUT /fulfillment_config/countries/:iso_name
    def update
      interactor = ::FulfillmentConfig::UpdateCountry.call(iso_name: params[:iso_name], country: country_params.to_h)
      if interactor.success?
        redirect_to fulfillment_config_countries_path, notice: t(".success")
      else
        session[:fulfillment_country_validation] = interactor.errors
        session[:fulfillment_country_old_values] = country_params
        redirect_to fulfillment_config_country_edit_path(params[:iso_name]), alert: interactor.message
      end
    end

    private

      def authorize_action
        authorize :admin, :actions
      end

      def country_params
        params.require(:country).permit(
          :require_receipt_number,
          :lower_weight_adjustment_threshold,
          :upper_weight_adjustment_threshold,
          :enable_chat_translation,
          :air_distance_threshold_configuration,
          :tax_required,
          :customer_service_phone,
          preferences: [
            :avoid_toll_on_delivery,
            :grab_express_max_delivery_volume,
            :grab_express_max_cod_amount,
            :grab_express_carton_box_height,
            :grab_express_carton_box_width,
            :grab_express_carton_box_depth,
            :grab_express_max_product_dimension,
            :delivery_geofence_radius,
            alcohol_restricted_delivery_times: [],
            recipient_for_failed_booking_grab_express_email_alert: []
          ]
        )
      end
  end
end
