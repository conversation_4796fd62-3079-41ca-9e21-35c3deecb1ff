module FulfillmentConfig
  class SlotsController < BaseController
    before_action :authorize_action, except: %i[index]
    before_action :set_slot_log, only: %i[download]

    # GET /fulfillment_config/slots
    def index
      @q = policy_scope(SlotLog, policy_scope_class: AdminPolicy::Scope).ransack(params[:q])
      @q.sorts = "created_at desc" if @q.sorts.empty?
      @slot_log_results = @q.result.page(params[:page])

      @cluster_slots = []
      @stock_locations ||= get_stock_locations
      @show_inactive = params[:show_inactive]

      if params[:date].present? && params[:store_external_id].present?
        @date = params[:date]
        @store_external_id = params[:store_external_id]

        set_time_zone

        interactor = ::FulfillmentConfig::GetSlotsByStore.call(date: @date, store_external_id: @store_external_id, show_inactive: @show_inactive)
        if interactor.success?
          @cluster_slots = interactor.result
        else
          flash[:error] = I18n.t("fulfillment_config.slots.e.cannot_fetch_slots", error: interactor.message)
        end
      end

      if params[:store_external_id].present? && @cluster_slots.present?
        @cluster_store_external_ids = @cluster_slots.first["stores"].map { |s| s["external_id"].to_s }
        @slot_logs = filter_slot_logs
      else
        @slot_logs = []
      end
    end

    # GET /fulfillment_config/slots/upload
    def upload
    end

    # POST /fulfillment_config/slots
    def create
      interactor = ImportSlots.call(params: slots_params, updater: current_user)
      respond_to do |format|
        if interactor.success?
          format.html { redirect_to fulfillment_config_slots_path, notice: I18n.t("fulfillment_config.slots.controller.uploaded") }
          format.json { render :show, status: :created, location: @slots }
        else
          flash[:error] = interactor.message
          format.html { render :upload }
          format.json { render json: interactor.message, status: :unprocessable_entity }
        end
      end
    end

    def download
      if @slot_log.present? && @slot_log.file.url.present?
        data = open(@slot_log.file.url)
        send_data data.read, filename: "slot-capacity-#{@slot_log.created_at}-#{@slot_log.creator.email}.csv"
      else
        flash[:error] = t(".e.download_failed")
        redirect_to fulfillment_config_slots_path
      end
    end

    def download_format_file
      file = File.join(Rails.root, "/spec/fixtures/slot_file_example.csv")
      send_file file, filename: "slot-capacity-example.csv"
    end

    def export
      @cluster_slots = []

      if params[:date].present? && params[:store_external_id].present?
        @date = params[:date]
        @store_external_id = params[:store_external_id]

        set_time_zone

        interactor = ::FulfillmentConfig::GetSlotsByStore.call(date: @date, store_external_id: @store_external_id, show_inactive: @show_inactive)
        if interactor.success?
          @cluster_slots = interactor.result
        else
          flash[:error] = I18n.t("fulfillment_config.slots.e.cannot_fetch_slots")
        end
      else
        flash[:error] = I18n.t("fulfillment_config.slots.e.cluster_slots_nil")
      end

      interactor = ExportSlots.call(cluster_slots: @cluster_slots, date: @date, utc_offset: @utc_offset)

      if interactor.success?
        send_data interactor.csv_file, filename: "slot-capacity-#{@date}-cluster-#{@cluster_slots.first.try(:[], "id")}.csv"
      else
        flash[:error] = interactor.message
        redirect_to fulfillment_config_slots_path
      end
    end

    private
      def authorize_action
        authorize :admin, :actions
      end

      def slots_params
        params.permit(:file, :date, :store_external_id, :utc_offset)
      end

      def set_slot_log
        @slot_log = SlotLog.find(params[:id])
      end

      def set_time_zone
        @stock_locations ||= get_stock_locations
        time_zone = @stock_locations.first["state"]["time_zone"]
        @utc_offset = TZInfo::Timezone.get(time_zone).current_period.utc_offset / 3600
      end

      def filter_slot_logs
        @slot_log_results.select do |sl|
          (@cluster_store_external_ids &
            (sl.store_external_ids.present? ? sl.store_external_ids.split(",") : [])
          ).present?
        end
      end
  end
end
