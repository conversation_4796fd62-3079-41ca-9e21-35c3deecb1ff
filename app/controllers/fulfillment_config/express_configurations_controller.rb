module FulfillmentConfig
  class ExpressConfigurationsController < BaseController
    before_action :authorize_action
    before_action :set_on_demand_clusters, :set_stock_location, only: [:edit]

    # GET /fulfillment_config/express_configuration
    def index
      skip_policy_scope
      @stock_locations ||= get_stock_locations
    end

    def edit
      interactor = ::FulfillmentConfig::GetExpressConfigurationByStore.call(stock_location_id: params[:stock_location_id])
      if interactor.success?
        @express_configuration = interactor.result
      else
        render :index, status: interactor.status_code
      end
    end

    # PUT /fulfillment_config/express_configuration/:stock_location_id
    def update
      interactor = ::FulfillmentConfig::UpdateExpressConfigurations.call(
        stock_location_id: params[:stock_location_id],
        params: express_configuration_params.to_h
      )

      if interactor.success?
        redirect_to fulfillment_config_express_configurations_path, notice: t(".success")
      else
        session[:fulfillment_express_configurations_validation] = interactor.errors
        session[:fulfillment_express_configurations_old_values] = express_configuration_params
        flash[:alert] = interactor.message
        redirect_to fulfillment_config_express_configuration_edit_path(params[:stock_location_id])
      end
    end

    private

      def authorize_action
        authorize :admin, :actions
      end

      def set_on_demand_clusters
        iso_name = Apartment::Tenant.current.upcase
        cache_key = "on_demand_clusters_#{ iso_name }"
        @on_demand_clusters = Rails.cache.fetch(cache_key, skip_nil: true, expires_in: 15.minute) do
          get_on_demand_clusters = ::FulfillmentConfig::GetOnDemandClustersByCountry.call
          if get_on_demand_clusters.success?
            get_on_demand_clusters.result
          else
            @on_demand_clusters_alert = get_on_demand_clusters.message
            break
          end
        end
      end

      def set_stock_location
        error_msg = ""
        cache_key = "fulfillment_stock_location_#{ params[:stock_location_id] }"
        @stock_location = Rails.cache.fetch(cache_key, skip_nil: true, expires_in: 1.minute) do
          sl_interactor = ::FulfillmentConfig::GetStockLocation.call(id: params[:stock_location_id])
          if sl_interactor.success?
            sl_interactor.result
          else
            error_msg = sl_interactor.message
            break
          end
        end
        if @stock_location.nil?
          redirect_to fulfillment_config_express_configurations_path, alert: error_msg
        end
      end

      def express_configuration_params
        params.require(:express_configuration).permit(
          :enable_on_demand_delivery,
          :on_demand_cluster_id,
          :on_demand_delivery_fee,
          :instant_delivery_cutoff_time,
          :express_additional_fee
        )
      end
  end
end
