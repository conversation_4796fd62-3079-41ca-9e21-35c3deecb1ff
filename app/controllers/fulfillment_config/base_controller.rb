module FulfillmentConfig
  class BaseController < ::ApplicationController
    include FulfillmentCacheHelper
    include Feedbackable

    def set_layout
      "fulfillment_config"
    end

    private

      def get_stock_locations
        stock_locations = get_stock_locations_by_country! # may throw exception
        stock_locations || []
      rescue => e
        flash.now[:error] = e.message
        []
      end

      def get_clusters
        clusters = get_clusters_by_country! # may throw exception
        clusters || []
      rescue => e
        flash.now[:error] = e.message
        []
      end
  end
end
