module FulfillmentConfig
  class OnDemandOrdersController < BaseController
    before_action :authorize_action

    def index
      skip_policy_scope
      if params[:order_number].present?
        @order_number = params[:order_number]
        interactor = ::FulfillmentConfig::GetOnDemandOrderByNumber.call(order_number: @order_number)
        if interactor.success?
          @on_demand_order = interactor.result
        else
          session[:fulfillment_od_search_errors] = interactor.error_messages
          flash[:error] = interactor.message
        end
      end
    end

    def update_user
      order_number = update_user_params[:order_number]
      user_id = update_user_params[:user_id]
      interactor =  ::FulfillmentConfig::UpdateOnDemandOrderUser.call(
        order_number: order_number,
        user_id: user_id
      )
      index_url = fulfillment_config_on_demand_orders_path(order_number: order_number)
      if interactor.success?
        flash[:info] = interactor.response_message
        redirect_to index_url
      else
        flash[:error] = interactor.response_message
        redirect_to index_url
      end
    end

    private

      def authorize_action
        authorize :admin, :actions
      end

      def update_user_params
        params.permit(:order_number, :user_id)
      end
  end
end
