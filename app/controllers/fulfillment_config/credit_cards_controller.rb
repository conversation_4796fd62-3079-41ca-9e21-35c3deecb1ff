module FulfillmentConfig
  class CreditCardsController < BaseController
    before_action :authorize_action

    # GET /fulfillment_config/credit_cards
    def index
      skip_policy_scope
      @loading = false
      @error_message = nil
      @credit_cards = []

      begin
        @loading = true
        response = Service::Fulfillment::CreditCard.find_all
        
        if response.status == 200 && response.body["response"]
          @credit_cards = response.body["response"]
        else
          @error_message = "Failed to fetch credit cards. Please try again later."
        end
      rescue => e
        Rails.logger.error "Credit Cards API Error: #{e.message}"
        @error_message = "Unable to connect to credit card service. Please try again later."
      ensure
        @loading = false
      end
    end

    # GET /fulfillment_config/credit_cards/:id
    def show
      skip_policy_scope
      @credit_card
      @users = Service::Fulfillment::User.find_all
      # Credit card details fetched in before_action
    end

    private

    def authorize_action
      authorize :admin, :actions
    end

    def set_credit_card
      card_id = params[:id]
      
      begin
        response = Service::Fulfillment::CreditCard.find_by_id(card_id)
        if response.status == 200 && response.body["response"]
          @credit_card = response.body["response"]
          
          unless @credit_card
            flash[:error] = "Credit card not found."
            redirect_to fulfillment_config_credit_cards_path
          end
        else
          flash[:error] = "Failed to fetch credit card details."
          redirect_to fulfillment_config_credit_cards_path
        end
      rescue => e
        Rails.logger.error "Credit Card Detail API Error: #{e.message}"
        flash[:error] = "Unable to connect to credit card service."
        redirect_to fulfillment_config_credit_cards_path
      end
    end
  end
end