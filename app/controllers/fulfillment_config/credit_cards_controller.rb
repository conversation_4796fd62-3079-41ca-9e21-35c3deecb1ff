module FulfillmentConfig
  class CreditCardsController < BaseController
    before_action :authorize_action
    before_action :set_credit_card, only: [:show, :update]

    # GET /fulfillment_config/credit_cards
    def index
      skip_policy_scope
      @loading = false
      @error_message = nil
      @credit_cards = []

      begin
        @loading = true
        response = Service::Fulfillment::CreditCard.find_all

        if response.status == 200 && response.body["response"]
          @credit_cards = response.body["response"]
        else
          @error_message = "Failed to fetch credit cards. Please try again later."
        end
      rescue => e
        Rails.logger.error "Credit Cards API Error: #{e.message}"
        @error_message = "Unable to connect to credit card service. Please try again later."
      ensure
        @loading = false
      end
    end

    # GET /fulfillment_config/credit_cards/:id
    def show
      skip_policy_scope
      load_users
      # Credit card details fetched in before_action
    end

    # PUT /fulfillment_config/credit_cards/:id
    def update
      skip_policy_scope

      begin
        response = Service::Fulfillment::CreditCard.update(params[:id], credit_card_params)

        if response.status == 200
          flash[:success] = "Credit card updated successfully."
        else
          flash[:error] = "Failed to update credit card. Please try again."
        end
      rescue => e
        Rails.logger.error "Credit Card Update API Error: #{e.message}"
        flash[:error] = "Unable to connect to credit card service. Please try again."
      end

      redirect_to fulfillment_config_credit_card_path(params[:id])
    end

    private

    def authorize_action
      authorize :admin, :actions
    end

    def load_users
      begin
        response = Service::Fulfillment::User.find_all
        if response.status == 200 && response.body["user"]
          @users = response.body["user"]
        else
          @users = []
          Rails.logger.error "Failed to fetch users: #{response.status}"
        end
      rescue => e
        Rails.logger.error "Users API Error: #{e.message}"
        @users = []
      end
    end

    def set_credit_card
      card_id = params[:id]

      begin
        # For now, we'll fetch all cards and find the one by index or last_digits
        # since the API doesn't seem to have individual card endpoints
        response = Service::Fulfillment::CreditCard.find_by_id(card_id)

        if response.status == 200 && response.body["response"]
          @credit_card = response.body["response"]

          unless @credit_card
            flash[:error] = "Credit card not found."
            redirect_to fulfillment_config_credit_cards_path
          end
          @current_selected_user_id = @credit_card["users"].present? ? @credit_card["users"][0]["id"] : nil
        else
          flash[:error] = "Failed to fetch credit card details."
          redirect_to fulfillment_config_credit_cards_path
        end
      rescue => e
        Rails.logger.error "Credit Card Detail API Error: #{e.message}"
        flash[:error] = "Unable to connect to credit card service."
        redirect_to fulfillment_config_credit_cards_path
      end
    end

    def credit_card_params
      params.permit(:user_id, :state)
    end
  end
end