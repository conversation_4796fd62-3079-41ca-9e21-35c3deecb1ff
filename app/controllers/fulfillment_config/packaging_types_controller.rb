module FulfillmentConfig
  class PackagingTypesController < BaseController
    include CountryProvider
    before_action :authorize_action
    before_action :set_current_locale
    before_action :set_fulfillment_country_id_or_redirect_to_index, except: :index

    # GET /fulfillment_config/packaging_types
    def index
      skip_policy_scope

      set_fulfillment_country_id_or_set_nil
      if @fulfillment_country_id.nil?
        @packaging_types = []
        return
      end

      interactor = ::FulfillmentConfig::GetPackagingTypesByCountry.call(country_id: @fulfillment_country_id)
      if interactor.success?
        @packaging_types = interactor.result
      else
        @packaging_types = []
        flash[:error] = interactor.message
      end
    end

    def new
      @packaging_type = @packaging_type ||
        Service::Fulfillment::PackagingType.new({}, @fulfillment_country_id, is_new: true)
    end

    def create
      interactor = ::FulfillmentConfig::CreatePackagingType.call(
        country_id: @fulfillment_country_id,
        packaging_type: packaging_type_params.to_h
      )
      if interactor.success?
        redirect_to fulfillment_config_packaging_types_path, notice: t(".success")
      else
        session[:fulfillment_packaging_type_validation] = interactor.errors
        session[:fulfillment_packaging_type_old_values] = packaging_type_params
        @packaging_type = Service::Fulfillment::PackagingType.new(
          packaging_type_params.to_h,
          @fulfillment_country_id,
          is_new: true
        )
        redirect_to fulfillment_config_packaging_type_new_path, alert: interactor.message
      end
    end

    def edit
      interactor = ::FulfillmentConfig::GetPackagingType.call(
        country_id: @fulfillment_country_id,
        id: params[:id]
      )
      if interactor.success?
        @packaging_type = Service::Fulfillment::PackagingType.new(
          interactor.result,
          @fulfillment_country_id,
          id: interactor.result["id"],
          is_new: false
        )
      else
        redirect_to fulfillment_config_packaging_types_path, alert: interactor.message
      end
    end

    def update
      interactor = ::FulfillmentConfig::UpdatePackagingType.call(
        country_id: @fulfillment_country_id,
        id: params[:id],
        packaging_type: packaging_type_params.to_h
      )
      if interactor.success?
        redirect_to fulfillment_config_packaging_types_path, notice: t(".success")
      else
        session[:fulfillment_packaging_type_validation] = interactor.errors
        session[:fulfillment_packaging_type_old_values] = packaging_type_params
        redirect_to fulfillment_config_packaging_type_edit_path(params[:id]), alert: interactor.message
      end
    end


    private

      def authorize_action
        authorize :admin, :actions
      end

      def set_current_locale
        @current_locale = Apartment::Tenant.current
      end

      def set_fulfillment_country_id_or_set_nil
        set_fulfillment_country_id do |country_interactor|
          if country_interactor.success?
            country_interactor.result["id"]
          else
            flash[:error] = country_interactor.message
            nil
          end
        end
      end

      def set_fulfillment_country_id_or_redirect_to_index
        set_fulfillment_country_id_or_redirect(fulfillment_config_packaging_types_path)
      end

      def packaging_type_params
        params.require(:packaging_type)
          .permit(
            :internal_name,
            :active,
            :tracked,
            :image)
          .tap do |whitelist|
            whitelist[:client_names] = params[:packaging_type][:client_names].permit!
            if params[:packaging_type][:snd_names].present?
              whitelist[:snd_names] = params[:packaging_type][:snd_names].permit!
            end
          end
      end
  end
end
