module FulfillmentConfig
  module CountryProvider
    extend ActiveSupport::Concern

    private

      def set_fulfillment_country_id_or_redirect(redirect_url)
        error_msg = ""
        set_fulfillment_country_id do |interactor|
          if interactor.success?
            interactor.result["id"]
          else
            error_msg = interactor.message
            nil
          end
        end

        if @fulfillment_country_id.nil?
          flash[:error] = error_msg
          redirect_to redirect_url
        end
      end

      def set_fulfillment_country_id(&proc)
        iso_name = Apartment::Tenant.current.upcase
        cache_key = "fulfillment_country_id_#{ iso_name }"
        @fulfillment_country_id = Rails.cache.fetch(cache_key, skip_nil: true, expires_in: 30.minute) do
          interactor = ::FulfillmentConfig::GetCountry.call(iso_name: iso_name)
          yield(interactor)
        end
      end
  end
end
