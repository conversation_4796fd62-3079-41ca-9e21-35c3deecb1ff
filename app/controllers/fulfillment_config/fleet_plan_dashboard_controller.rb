module FulfillmentConfig
  class FleetPlanDashboardController < BaseController
    before_action :authorize_action

    def index
      skip_policy_scope

      @date = params[:date] || nil
      @cluster_id = params[:cluster_id]
      @clusters ||= get_clusters

      respond_to do |format|
        format.html { }
        format.json {
          if !(@date.present? && @cluster_id.present?)
            return render json: { message: I18n.t("fulfillment_config.long_slots.e.invalid_json_params") }, status: :bad_request
          end

          interactor = ::FulfillmentConfig::GetFleetPlanDashboard.call(date: @date, cluster_id: @cluster_id)
          if interactor.success?
            render json: interactor.result
          else
            render json: { message: interactor.message }, status: interactor.status_code
          end
        }
      end
  end


  private

    def authorize_action
      authorize :admin, :actions
    end
  end
end
