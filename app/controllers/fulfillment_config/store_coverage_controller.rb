module FulfillmentConfig
  class StoreCoveragesController < BaseController
    before_action :authorize_action
    after_action :clear_errors, only: [:index]

    def index
      skip_policy_scope
      @stock_locations ||= get_stock_locations
      @preselect = params["preselect"]
      @errors = session[:errors]
    end

    def show
      respond_to do |format|
        coverage_interactor = ::FulfillmentConfig::GetStockLocationCoverage.call(external_id: params[:external_id])
        if coverage_interactor.success?
          coverage = coverage_interactor.result
          format.json { render json: coverage }
        else
          format.json { render json: { error: coverage_interactor.message }, status: :unprocessable_entity }
        end
      end
    end

    def edit
      render :edit, locals: { prev: params["prev"], center: params["center"] }
    end

    def download_kml
      interactor = ::FulfillmentConfig::ConvertToKml.call(payload: params["store_coverages"])
      if interactor.success?
        now_str = DateTime.now.in_time_zone(helpers.get_tz).strftime("%Y_%m_%d_%H_%M_%S")
        send_data interactor.kml_file, filename: "fms_store_coverages_#{now_str}.kml"
      else
        errors = [{ message: interactor.message }]
        session[:errors] = errors
        redirect_to fulfillment_config_store_coverages_path
      end
    end

    def upload_kml
      interactor = ::FulfillmentConfig::GetStoreCoveragesFromKml.call(kml_file: params["kml_file"])

      @uploaded_store_coverages = interactor.store_coverages
      errors = [{ message: interactor.message }] if interactor.failure?

      render :edit, locals: { prev: params["prev"], center: params["center"], errors: errors }
    end

    def update
      interactor = FulfillmentConfig::UpdateStoreCoverages.call(coverages_form: update_params[:store_coverages])

      prev = update_params[:store_coverages].map { |store| store["id"] }
      infos = []
      if interactor.success?
        if interactor.errors.count == 0
          return redirect_to fulfillment_config_store_coverages_path(preselect: prev)
        else
          infos = [{ message: "Success for stores: #{ interactor.results.map(&:id).join(", ") }" }]
          errors = interactor.messages
          center = [interactor.results.first.center.lat, interactor.results.first.center.lon]
        end
      else
        errors = interactor.respond_to?(:message) ? [{ message: interactor.message }] : interactor.messages
        center = [update_params[:store_coverages].first[:center][:lat], update_params[:store_coverages].first[:center][:lon]]
      end
      render :edit, locals: { prev: prev.to_s, center: center.to_s, errors: errors, infos: infos }
    end

    private

      def authorize_action
        authorize :admin, :actions
      end

      def update_params
        params.permit(
          store_coverages: [
            :id,
            :name,
            center: [:lat, :lon],
            polygon: [[:lat, :lon]]
          ]
        )
      end

      def clear_errors
        session[:errors] = nil
      end
  end
end
