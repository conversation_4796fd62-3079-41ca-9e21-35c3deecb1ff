module FulfillmentConfig
  class PackagingsController < BaseController
    include CountryProvider

    before_action :authorize_action

    # GET /fulfillment_config/packaging_stock_locations
    def store_index
      @stock_locations ||= get_stock_locations
    end

    # GET /fulfillment_config/stock_locations/:stock_location_id/packagings
    def index
      skip_policy_scope

      set_stock_location_or_redirect
      interactor = ::FulfillmentConfig::GetPackagings.call(stock_location_id: params[:stock_location_id])
      if interactor.success?
        @packagings = interactor.result
      else
        @packagings = []
        flash[:error] = interactor.message
      end
    end

    # GET /fulfillment_config/stock_locations/:stock_location_id/packagings/new
    def new
      set_fulfillment_country_id_or_redirect fulfillment_config_packagings_path(params[:stock_location_id])

      interactor = ::FulfillmentConfig::GetPackagingTypesByCountry.call(
        country_id: @fulfillment_country_id,
        active_only: true
      )
      if interactor.success?
        @packaging_types = interactor.result
        @packaging = Service::Fulfillment::Packaging.new({}, params[:stock_location_id])
        set_stock_location_or_redirect
      else
        redirect_to fulfillment_config_packagings_path(params[:stock_location_id]), alert: interactor.message
      end
    end

    # POST /fulfillment_config/stock_locations/:stock_location_id/packagings
    def create
      set_fulfillment_country_id_or_redirect fulfillment_config_packagings_path(params[:stock_location_id])
      interactor = ::FulfillmentConfig::CreatePackaging.call(
        country_id:  @fulfillment_country_id,
        stock_location_id: params[:stock_location_id],
        params: packaging_params
      )
      if interactor.success?
        redirect_to fulfillment_config_packagings_path(params[:stock_location_id]), notice: t(".success")
      else
        session[:fulfillment_packaging_validation] = interactor.errors
        session[:fulfillment_packaging_old_values] = packaging_params
        redirect_to fulfillment_config_new_packaging_path(params[:stock_location_id]), alert: interactor.message
      end
    end

    # GET /fulfillment_config/stock_locations/:stock_location_id/packagings/:id/edit
    def edit
      set_fulfillment_country_id_or_redirect fulfillment_config_packagings_path(params[:stock_location_id])

      pkg_type_interactor = ::FulfillmentConfig::GetPackagingTypesByCountry.call(
        country_id: @fulfillment_country_id,
        active_only: true
      )
      pkg_interactor = ::FulfillmentConfig::GetPackaging.call(
        stock_location_id: params[:stock_location_id],
        id: params[:id]
      )

      if pkg_interactor.success? && pkg_type_interactor.success?
        @packaging = pkg_interactor.result
        if !@packaging.active
          redirect_to fulfillment_config_packagings_path(params[:stock_location_id]), alert: t(".inactive_packaging_type")
        end
        @packaging_types = pkg_type_interactor.result
        set_stock_location_or_redirect
      else
        redirect_to(
          fulfillment_config_packagings_path(params[:stock_location_id]),
          alert: pkg_interactor.message
        )
      end
    end

    # PUT /fulfillment_config/stock_locations/:stock_location_id/packagings/:id
    def update
      interactor = ::FulfillmentConfig::UpdatePackaging.call(
        stock_location_id: params[:stock_location_id],
        id: params[:id],
        params: packaging_params
      )
      if interactor.success?
        redirect_to fulfillment_config_packagings_path(params[:stock_location_id]), notice: t(".success")
      else
        session[:fulfillment_packaging_validation] = interactor.errors
        session[:fulfillment_packaging_old_values] = packaging_params
        redirect_to fulfillment_config_edit_packaging_path(params[:stock_location_id]), alert: interactor.message
      end
    end

    # PUT /fulfillment_config/stock_locations/:stock_location_id/packagings/reorder
    def reorder_sequence
      interactor = ::FulfillmentConfig::ReorderPackagingSequence.call(
        stock_location_id: params[:stock_location_id],
        initial_sequence: packaging_sequences_params[:initial_sequence],
        final_sequence: packaging_sequences_params[:final_sequence]
      )
      if interactor.success?
        redirect_to fulfillment_config_packagings_path(params[:stock_location_id]), notice: t(".success")
      else
        redirect_to fulfillment_config_packagings_path(params[:stock_location_id]), alert: interactor.message
      end
    end

    # DELETE /fulfillment_config/stock_locations/:stock_location_id/packagings/:id
    def remove
      interactor = ::FulfillmentConfig::RemovePackaging.call(
        id: params[:id],
        stock_location_id: params[:stock_location_id]
      )
      if interactor.success?
        redirect_to fulfillment_config_packagings_path(params[:stock_location_id]), notice: t(".success")
      else
        redirect_to fulfillment_config_packagings_path(params[:stock_location_id]), alert: interactor.message
      end
    end

    private

      def set_stock_location_or_redirect
        error_msg = ""
        cache_key = "fulfillment_stock_location_#{ params[:stock_location_id] }"
        @stock_location = Rails.cache.fetch(cache_key, skip_nil: true, expires_in: 1.minute) do
          sl_interactor = ::FulfillmentConfig::GetStockLocation.call(id: params[:stock_location_id])
          if sl_interactor.success?
            sl_interactor.result
          else
            error_msg = sl_interactor.message
            break
          end
        end
        if @stock_location.nil?
          redirect_to fulfillment_config_packaging_stock_locations_path, alert: error_msg
        end
      end

      def authorize_action
        authorize :admin, :actions
      end

      def packaging_params
        params.require(:packaging).permit(
          :packaging_type_id,
          :max_quantity,
          :price,
          :default
        )
      end

      def packaging_sequences_params
        params.require(:packagings).permit(:initial_sequence, :final_sequence)
      end
  end
end
