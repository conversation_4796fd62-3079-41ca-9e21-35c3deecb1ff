module FulfillmentConfig
  class ManualAssignmentLogsController < BaseController
    include InputHelper
    include ValidationHelper

    before_action :authorize_action
    before_action :apply_index_params, only: :index

    def index
      skip_policy_scope

      @query = ManualAssignmentLog.between_period(@start_date, @end_date)
      options = {
        start_date: @start_date,
        end_date: @end_date
      }
      options[:email] = @user_email if @user_email
      options[:order_number] = @order_number if @order_number
      @assignment_logs_datatable = FulfillmentConfig::ManualAssignmentLogDatatable.new(view_context, @query, options)

      respond_to do |format|
        format.html { render partial: "fulfillment_config/manual_assignment_logs/index", layout: false }
        format.json { render json: @assignment_logs_datatable, status: :ok }
      end
    end

    private

      def authorize_action
        authorize :admin, :actions
      end

      def apply_index_params
        @start_date = params[:start_date] || params.dig(:dataTableOptions, 0) || 6.months.ago.to_date
        @end_date = params[:end_date] || params.dig(:dataTableOptions, 1) || Date.today
        @user_email = (params[:search_type] == "user_email" ? params[:search_string] : nil).presence || params.dig(:dataTableOptions, 2)
        @order_number = (params[:search_type] == "order_number" ? params[:search_string] : nil).presence || params.dig(:dataTableOptions, 3)

        unless valid_date?(@start_date) && valid_date?(@end_date)
          respond_to do |format|
            format.html {
              flash[:error] = I18n.t("admin.settlement_logs.e.invalid_date_format")
              render partial: "fulfillment_config/manual_assignment_logs/index", layout: false, status: :unprocessable_entity
            }
            format.json { head :unprocessable_entity }
          end
        end
      end
    # end of private block
  end
end
