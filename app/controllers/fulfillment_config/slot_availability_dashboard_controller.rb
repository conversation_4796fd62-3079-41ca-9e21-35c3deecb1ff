module FulfillmentConfig
  class SlotAvailabilityDashboardController < BaseController
    before_action :authorize_action

    def index
      skip_policy_scope

      @date = params[:date]
      @cluster_ids = params[:cluster_ids]&.split(",")&.map(&:to_i)&.uniq
      @line_item_count = params[:line_item_count].present? ? params[:line_item_count].try(:to_i) : nil
      @total_volume = params[:total_volume].present? ? params[:total_volume].try(:to_f) : nil
      @distance_km = params[:distance].present? ? params[:distance].try(:to_f) : nil
      control_params = [ @date, @cluster_ids, @line_item_count, @total_volume, @distance_km ]

      respond_to do |format|
        format.html {
          @available_clusters ||= get_available_clusters
          if !(control_params.all?(&:present?))
            @clusters = []
            if control_params.any?(&:present?)
              flash[:error] = get_invalid_params_message(@cluster_ids, @date, @distance_km, @line_item_count, @total_volume)
            end
            render :index
          else
            cached_cluster_availabilities = @cluster_ids.map { |id| get_cached_availability(id) }.select(&:present?)
            cached_ids = cached_cluster_availabilities.map { |cluster| cluster["id"] }
            interactor = FulfillmentConfig::GetSlotAvailabilityDashboard.call(
              cluster_ids: @cluster_ids.select { |cid| !cached_ids.include?(cid) },
              distance_km: @distance_km,
              date: @date,
              line_item_count: @line_item_count,
              total_volume: @total_volume
            )

            int_to_time = Proc.new { |i| i.to_s.length == 1 ? "0#{ i }:00" : "#{ i }:00" }

            int_to_datetime = Proc.new { |i| "#{@date}T#{int_to_time.call(i)}" }

            if interactor.success?
              cluster_availabilities = interactor.result["clusters"]
              cluster_availabilities.each { |cluster| store_cache_availability(cluster) }
              @clusters = interactor.result["clusters"].concat(cached_cluster_availabilities)
              render locals: { int_to_time: int_to_time, int_to_datetime: int_to_datetime }
            else
              @cluster = []
              flash[:error] = interactor.message
            end
          end
        }
        format.json {
          if !(@cluster_ids.present? &&
              @distance_km.present? &&
              @date.present? &&
              @line_item_count.present? &&
              @total_volume.present?)
            return render json: {
              message: get_invalid_params_message(@cluster_ids, @date, @distance_km, @line_item_count, @total_volume)
            }, status: 400
          end

          interactor = FulfillmentConfig::GetSlotAvailabilityDashboard.call(
            cluster_ids: @cluster_ids,
            distance_km: @distance_km,
            date: @date,
            line_item_count: @line_item_count,
            total_volume: @total_volume
          )
          if interactor.success?
            render json: interactor.result
          else
            render json: { message: interactor.message }, status: 400
          end
        }
      end
    end


    private

      def authorize_action
        authorize :admin, :actions
      end

      def get_available_clusters
        cache_key = "clusters_#{Apartment::Tenant.current.upcase}"
        clusters = Rails.cache.fetch(cache_key, skip_nil: true, expires_in: 15.minute) do
          interactor = ::FulfillmentConfig::GetClustersByCountry.call
          if interactor.success?
            interactor.result
          else
            raise interactor.message # prevent caching on error
          end
        end
        clusters || []
      rescue => e
        flash[:error] = e.message
        []
      end

      def get_cached_availability(id)
        key = get_cache_key(id)
        Rails.cache.fetch(key)
      end

      def get_cache_key(id)
        "slot_availability_#{ @date }_#{ id }_d#{ @distance_km }_i#{ @line_item_count }_v#{ @total_volume }"
      end

      def store_cache_availability(cluster)
        key = get_cache_key(cluster["id"])
        Rails.cache.fetch(key, skip_nil: true, expires_in: 5.minute) { cluster }
      end

      def get_invalid_params_message(cluster_ids, date, distance_km, line_item_count, total_volume)
        base_message = I18n.t("fulfillment_config.slot_availability_dashboard.e.invalid_json_params")
        missing_params = []
        missing_params.push(I18n.t("fulfillment_config.slot_availability_dashboard.index.cluster")) unless cluster_ids.present?
        missing_params.push(I18n.t("fulfillment_config.slot_availability_dashboard.index.date")) unless date.present?
        missing_params.push(I18n.t("fulfillment_config.slot_availability_dashboard.index.distance_km")) unless distance_km.present?
        missing_params.push(I18n.t("fulfillment_config.slot_availability_dashboard.index.line_item_count")) unless line_item_count.present?
        missing_params.push(I18n.t("fulfillment_config.slot_availability_dashboard.index.total_volume_l")) unless total_volume.present?
        "#{base_message}(#{missing_params.join(", ")})"
      end
  end
end
