module FulfillmentConfig
  class LongSlotsController < BaseController
    before_action :authorize_action

    def index
      skip_policy_scope
      @show_inactive = params[:show_inactive]
      @cluster_slots = []
      @cluster_shifts = []
      @stock_locations ||= get_stock_locations
      status_code = :ok
      if params[:date].present? && params[:store_external_id].present?
        @date = params[:date]
        @store_external_id = params[:store_external_id]

        slots_interactor = ::FulfillmentConfig::GetSlotsByStoreV2.call(date: @date, store_external_id: @store_external_id, show_inactive: @show_inactive)
        if slots_interactor.success?
          if slots_interactor.result.first.present? && slots_interactor.result.first.try(:[], "slot_type") != "LONGER_DELIVERY"
            flash[:error] = I18n.t("fulfillment_config.long_slots.e.wrong_slot_type")
            status_code = :internal_server_error
          else
            @cluster_slots = slots_interactor.result


            shifts_interactor = ::FulfillmentConfig::GetShiftsByStore.call(
              date: @date,
              store_external_id: @store_external_id,
              show_inactive: @show_inactive
            )
            if shifts_interactor.success?
              @cluster_shifts = shifts_interactor.result
            else
              flash[:warning] = I18n.t("fulfillment_config.shifts.e.cannot_fetch_shifts", error: shifts_interactor.message)
              status_code = shifts_interactor.status_code
            end

            flash[:info] = I18n.t("fulfillment_config.long_slots.controller.empty_result") if @cluster_slots.empty?
          end
        else
          flash[:error] = I18n.t("fulfillment_config.long_slots.e.cannot_fetch_slots", error: slots_interactor.message)
          status_code = slots_interactor.status_code
        end
      end
      render :index, status: status_code
    end

    def upload
    end

    # POST /fulfillment_config/long_slots
    def create
      interactor = ImportLongSlots.call(params: slots_params)
      respond_to do |format|
        if interactor.success?
          format.html { redirect_to fulfillment_config_long_slots_upload_path, notice: I18n.t("fulfillment_config.long_slots.controller.uploaded") }
        else
          flash[:error] = interactor.message
          format.html { render :upload, status: interactor.status_code }
        end
      end
    end

    def download_format_file
      file = File.join(Rails.root, "/spec/fixtures/long_slot_template.csv")
      send_file file, filename: "long_slot_template.csv"
    end

    # DELETE /fulfillment_config/long_slots/:slot_id
    def destroy
      interactor = DeleteSlot.call(slot_id: params[:slot_id])
      respond_to do |format|
        index_url = fulfillment_config_long_slots_path(
          show_inactive: params[:show_inactive],
          date: params[:date],
          store_external_id: params[:store_external_id]
        )
        if interactor.success?
          format.html { redirect_to index_url, notice: I18n.t("fulfillment_config.long_slots.controller.deleted") }
        else
          flash[:error] = interactor.message
          format.html { redirect_to index_url }
        end
      end
    end

    def edit
      @slots = []
      if params[:date].present? && params[:store_external_id].present?
        @date = params[:date]
        @store_external_id = params[:store_external_id]
        @show_inactive = params[:show_inactive]
        status_code = :ok

        interactor = ::FulfillmentConfig::GetSlotsByStoreAndDate.call(date: @date, store_external_id: @store_external_id, show_inactive: @show_inactive)
        if interactor.success?
          if interactor.result.present? && interactor.result[:slot_type] != "LONGER_DELIVERY"
            flash[:error] = I18n.t("fulfillment_config.long_slots.e.wrong_slot_type")
            status_code = :internal_server_error
          else
            @store_name = interactor.result[:store_name]
            @slots = interactor.result[:slots]
            @time_zone = interactor.result[:time_zone]
            flash[:info] = I18n.t("fulfillment_config.long_slots.controller.empty_result") if @slots.empty?
          end
        else
          flash[:error] = I18n.t("fulfillment_config.long_slots.e.cannot_fetch_slots", error: interactor.message)
          status_code = interactor.status_code
        end
      end
      render :edit, status: status_code
    end

    def update
      edit_url = fulfillment_config_long_slots_edit_path(
        store_external_id: params[:store_external_id],
        date: params[:date]
      )

      interactor = ::FulfillmentConfig::UpdateLongSlots.call(params: update_slots_params, updater: current_user)
      if interactor.success?
        flash[:info] = I18n.t("fulfillment_config.long_slots.controller.updated")
        redirect_to edit_url
      else
        @slots = interactor.slots.presence || []
        flash[:error] = I18n.t("fulfillment_config.long_slots.e.cannot_update_slots", error: interactor.message)
        session[:fulfillment_config_errors] = interactor.message
        render :edit, status: interactor.status_code
      end
    end

    private

      def authorize_action
        authorize :admin, :actions
      end

      def slots_params
        params.permit(:file)
      end

      def update_slots_params
        params.permit(
          :store_external_id,
          :date,
          :time_zone,
          slots: [
            :id,
            :start_time,
            :end_time,
            :open
          ]
        )
      end
  end
end
