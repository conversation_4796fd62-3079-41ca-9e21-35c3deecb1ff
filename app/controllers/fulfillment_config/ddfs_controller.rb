module FulfillmentConfig
  class DdfsController < BaseController
    before_action :authorize_action

    def index
      skip_policy_scope
      @q = policy_scope(DdfLog, policy_scope_class: AdminPolicy::Scope).ransack(params[:q])
      @q.sorts = "created_at desc" if @q.sorts.empty?
      @stock_locations = @q.result.page(params[:page])

      interactor = ::FulfillmentConfig::GetStockLocationsByCountry.call
      if interactor.success?
        @stock_locations = interactor.result
      else
        @stock_locations = []
        flash[:error] = interactor.message
      end
    end

    def show
      sl_interactor = ::FulfillmentConfig::GetStockLocation.call(id: params[:stock_location_id])
      if sl_interactor.success?
        @stock_location = sl_interactor.result
      else
        redirect_to fulfillment_config_ddfs_path, alert: sl_interactor.message
      end

      ddf_interactor = ::FulfillmentConfig::GetDdfByStore.call(id: params[:stock_location_id])
      if ddf_interactor.success?
        @ddf = ddf_interactor.result
      else
        redirect_to fulfillment_config_ddfs_path, alert: ddf_interactor.message
      end
    end

    def edit
      sl_interactor = ::FulfillmentConfig::GetStockLocation.call(id: params[:stock_location_id])
      if sl_interactor.success?
        @stock_location = sl_interactor.result
      else
        redirect_to fulfillment_config_ddfs_path, alert: sl_interactor.message
      end
    end

    def update
      interactor = ::FulfillmentConfig::UpdateDdfByStockLocation.call(
        params: ddfs_params.merge(stock_location_id: params[:stock_location_id]),
        updater: current_user)
      if interactor.success?
        redirect_to fulfillment_config_ddf_path(stock_location_id: params[:stock_location_id]), notice: I18n.t("fulfillment_config.ddfs.controller.updated")
      else
        flash[:error] = interactor.message
        session[:fulfillment_ddf_validation] = interactor.errors
        redirect_to fulfillment_config_ddf_edit_path(params[:stock_location_id])
      end
    end

    def download_format_file
      file = File.join(Rails.root, "/spec/fixtures/files/ddf_template.xlsx")
      send_file file, filename: "ddf_template.xlsx"
    end

    private
      def authorize_action
        authorize :admin, :actions
      end

      def ddfs_params
        params.permit(:file)
      end
  end
end
