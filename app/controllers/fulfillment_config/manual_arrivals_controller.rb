module FulfillmentConfig
  class ManualArrivalsController < BaseController
    before_action :authorize_action

    def index
      skip_policy_scope

      @order_number = params[:search_string]
      @batches = []
      @shipment = nil
      @delivery_job = nil

      respond_to do |format|
        format.html do
          if params[:search_string]
            interactor = ::FulfillmentConfig::GetDeliveryJobFromBatches.call(order_number: @order_number)
            if interactor.success?
              fetch_log_interactor = ::FulfillmentConfig::GetOrderShipmentUpdateLog.call(order_number: @order_number)
              @batches = interactor.batches
              @shipment = interactor.shipment
              @delivery_job = interactor.delivery_job
              @current_delivery_state = interactor.current_delivery_state
              @logs = fetch_log_interactor.success? ? fetch_log_interactor.logs : []

              if @shipment["is_payment_clear"].present?
                flash[:error] = interactor.message
                redirect_to fulfillment_config_manual_arrival_path
              end
            else
              flash[:error] = interactor.message
              redirect_to fulfillment_config_manual_arrival_path
            end
          end
        end
      end
    end

    def update_shipment_status
      current_status = update_status_params[:current_status].gsub(/^(shopper_|driver_)/, "")

      interactor = UpdateDeliveryState.call(
        actor_id: current_user.id,
        order_number: params[:order_number],
        shipment_number: update_status_params[:shipment_number],
        attribute_name: "state",
        previous_value: update_status_params[:previous_status],
        current_value: current_status,
        reason: update_status_params[:reason],
        note: update_status_params[:note]
      )

      respond_to do |format|
        format.html do
          if interactor.success?
            flash[:notice] = t("fulfillment_config.manual_arrivals.index.update_successfully",
                               order_number: interactor.order_number,
                               status: update_status_params[:current_status])
          else
            flash[:error] = interactor.message
          end
          redirect_to fulfillment_config_manual_arrival_path(search_string: interactor.order_number)
        end
      end
    end

    private

      def authorize_action
        authorize :admin, :actions
      end

      def update_status_params
        params.require(:update_status).permit(:shipment_number,
                                              :order_number,
                                              :previous_status,
                                              :current_status,
                                              :reason,
                                              :note)
      end
    # end of private block
  end
end
