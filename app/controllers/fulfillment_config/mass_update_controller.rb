module FulfillmentConfig
  class MassUpdateController < BaseController
    before_action :authorize_action
    before_action :set_mass_update_log, only: %i[index upload]

    # GET /fulfillment_config/stock_location/mass_update
    def index
      skip_policy_scope
    end

    # POST /fulfillment_config/stock_location/mass_update/download
    def download_template
      @stock_locations ||= get_stock_locations

      interactor = GetPopulatedMassUpdateTemplate.call(
        params: download_template_params,
        stock_locations: @stock_locations
      )
      if interactor.success?
        send_data(interactor.file, filename: interactor.filename)
      else
        flash[:error] = interactor.message
        redirect_to fulfillment_config_mass_update_index_path
      end
    end

    # POST /fulfillment_config/stock_location/mass_update/upload
    def upload
      interactor = ImportStoreMassUpdate.call(params: upload_params, creator: current_user,)
      if interactor.success?
        if interactor.has_failing_validation
          @file = interactor.file
          @filename = "mass-update-with-validation-#{Date.current}.csv"
          flash.now[:alert] = t(".success_with_exception")
          render :index
        else
          flash[:notice] = t(".success")
          redirect_to fulfillment_config_mass_update_index_path
        end
      else
        flash[:error] = interactor.message
        redirect_to fulfillment_config_mass_update_index_path
      end
    end

    private
      def authorize_action
        authorize :admin, :actions
      end

      def download_template_params
        params.permit(:store_by_keyword, config_to_modify: [])
      end

      def upload_params
        params.permit(:file)
      end

      def set_mass_update_log
        @q = policy_scope(MassUpdateLog, policy_scope_class: AdminPolicy::Scope).ransack(params[:q])
        @q.sorts = "updated_at desc" if @q.sorts.empty?
        @mass_update_logs = @q.result.page(params[:page])
      end
  end
end
