module Admin
  class UserInventoriesController < BaseController
    before_action :set_user, only: %i[show]

    # GET /admin/user_inventories
    def index
      @q = policy_scope(User.user, policy_scope_class: AdminPolicy::<PERSON>ope)
      respond_to do |format|
        format.html
        format.json { render json: Admin::UserInventoriesDatatable.new(view_context, @q) }
      end
    end

    # GET /admin/user_inventories/:id
    def show
      authorize :admin, :actions
    end

    # GET /admin/user_inventories/export
    def export
      authorize :admin, :actions

      interactor = ExportInventorySummary.call
      send_data interactor.file, filename: interactor.filename
    end

    private

      # Use callbacks to share common setup or constraints between actions.
      def set_user
        @user = User.friendly.find(params[:id])
      end
  end
end
