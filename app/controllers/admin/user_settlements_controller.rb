class Admin::UserSettlementsController < Admin::BaseController
  # GET /admin/user_settlements
  def index
    @q = policy_scope(User.user.distinct, policy_scope_class: AdminPolicy::Scope)
    respond_to do |format|
      format.html
      format.json { render json: Admin::UserSettlementDatatable.new(view_context, @q) }
    end
  end

  def export
    authorize :admin, :actions

    MailSettlementReportJob.perform_later(current_user.email)

    flash[:notice] = t(".requested", email: current_user.email)
    redirect_to admin_user_settlements_path
  end
end
