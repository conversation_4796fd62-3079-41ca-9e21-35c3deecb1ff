class Admin::ShoppingCashController < Admin::BaseController
  before_action :authorize_action, except: %i[index]
  before_action :set_date_range, only: %i[index]
  before_action :set_shopping_cash, only: %i[destroy]

  # # GET /shopping_cash
  def index
    @q = policy_scope(SettlementLog.shopping_cash.between_date(@start_date, @end_date), policy_scope_class: AdminPolicy::Scope).ransack(params[:q])
    @q.sorts = "created_at desc" if @q.sorts.empty?
    @shopping_cash = @q.result.page(params[:page])
    @shopping_cash = @shopping_cash.pluck(:log_id).uniq.map { |log_id| SettlementLog.latest_log_state(log_id) }
  end

  # GET /shopping_cash/upload
  def upload
  end

  def download_format_file
    file = File.join(Rails.root, "/spec/fixtures/shopping_cash_file.csv")
    send_file file, filename: "shopping-cash-example.csv"
  end

  # POST /shooping_cash
  def create
    interactor = ImportShoppingCash.call(params: shopping_cash_params, updater: current_user)
    respond_to do |format|
      if interactor.success?
        format.html { redirect_to index_shopping_cash_admin_settlement_logs_path, notice: I18n.t("admin.shopping_cash.controller.uploaded") }
        format.json { render :show, status: :created, location: @shopping_cash }
      else
        flash[:error] = interactor.message
        format.html { render :upload }
        format.json { render json: interactor.message, status: :unprocessable_entity }
      end
    end
  end

  def destroy
    interactor = CancelShoppingCash.call(model: @shopping_cash)
    if interactor.success?
      redirect_to index_shopping_cash_admin_settlement_logs_path, notice: t(".success_delete")
    else
      flash[:error] = interactor.message
      redirect_to index_shopping_cash_admin_settlement_logs_path
    end
  end

  private

    def set_shopping_cash
      @shopping_cash = SettlementLog.find(params[:id])
    end

    def authorize_action
      authorize :admin, :actions
    end

    def set_date_range
      @start_date = params[:start_date].present? ? params[:start_date] : Date.today - 1.day
      @end_date = params[:end_date].present? ? params[:end_date] : Date.today
    end

    def shopping_cash_params
      params.permit(:start_date, :end_date, :file, :balance_type)
    end
end
