class Admin::InventoriesController < Admin::BaseController
  before_action :set_inventory, only: %i[show edit update destroy]
  before_action :authorize_action, except: %i[index]

  # GET /inventories
  def index
    @q = policy_scope(Inventory.includes(:creator).items, policy_scope_class: AdminPolicy::Scope).ransack(params[:q])
    @q.sorts = "names asc" if @q.sorts.empty?
    @inventories = @q.result.page(params[:page])
  end

  # GET /inventories/new
  def new
    @inventory = Inventory.new
  end

  # GET /inventories/1/edit
  def edit
    # defined on before_action
  end

  # POST /inventories
  # POST /inventories.json
  def create
    interactor = CreateInventory.call(params: inventory_params, creator: current_user)
    @inventory = interactor.model

    respond_to do |format|
      if interactor.success?
        format.html { redirect_to admin_inventories_path, notice: "Inventory was successfully created." }
        format.json { render :show, status: :ok, location: @inventory }
      else
        flash[:error] = interactor.message
        format.html { render :new, status: interactor.status_code }
        format.json { render json: @inventory.errors, status: interactor.status_code }
      end
    end
  end

  # PATCH/PUT /inventories/1
  # PATCH/PUT /inventories/1.json
  def update
    interactor = UpdateInventory.call(model: @inventory, params: inventory_params)
    @inventory = interactor.model

    respond_to do |format|
      if interactor.success?
        format.html { redirect_to admin_inventories_path, notice: "Inventory was successfully updated." }
        format.json { render :show, status: :ok, location: @inventory }
      else
        flash[:error] = interactor.message
        format.html { render :edit, status: interactor.status_code }
        format.json { render json: @inventory.errors, status: interactor.status_code }
      end
    end
  end

  # DELETE /inventories/1
  # DELETE /inventories/1.json
  def destroy
    interactor = DeleteInventory.call(model: @inventory)
    respond_to do |format|
      if interactor.success?
        format.html { redirect_to admin_inventories_url, notice: "Inventory was successfully destroyed." }
        format.json { head :no_content }
      else
        flash[:error] = interactor.message
        format.html { render :index, status: interactor.status_code }
        format.json { head interactor.status_code }
      end
    end
  end

  # GET /inventories/export
  def export
    interactor = ExportInventory.call
    send_data interactor.file, filename: interactor.filename
  end

  private

    def set_inventory
      @inventory = Inventory.find(params[:id])
    end

    def authorize_action
      authorize :admin, :actions
    end

    def inventory_params
      params.require(:inventory).permit(:name)
    end
end
