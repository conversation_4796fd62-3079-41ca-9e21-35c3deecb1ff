class Admin::PayrollAfterGrossesController < Admin::BaseController
  before_action :set_payroll_after_gross, only: %i[show edit update destroy]
  before_action :authorize_action

  # GET /payroll_after_gross
  def index
    @q = policy_scope(scoped_collection, policy_scope_class: AdminPolicy::Scope).ransack(params[:q])
    @q.sorts = "created_at desc" if @q.sorts.empty?
    @payroll_after_grosses = @q.result.page(params[:page])
  end

  # GET /payroll_after_grosses/:id
  def show
    # defined in before action
  end

  # GET /payroll_after_grosses/new
  def new
    @payroll_after_gross = AdjustmentCondition.new
    @adjustment_after_gross = AdjustmentAfterGross.new
  end

  # GET /payroll_after_grosses/:id/edit
  def edit
    # defined in before action
  end

  # POST /payroll_after_grosses
  def create
    interactor = CreatePayrollAfterGross.call(params: payroll_after_grosses_params, creator: current_user)
    @payroll_after_gross = interactor.model

    if @payroll_after_gross.blank?
      @payroll_after_gross = AdjustmentCondition.new
      @adjustment_after_gross = AdjustmentAfterGross.new
    end

    respond_to do |format|
      if interactor.success?
        format.html { redirect_to admin_payroll_after_grosses_path, notice: t(".created") }
        format.json { render :show, status: :created, location: @payroll_after_gross }
      else
        flash[:error] = interactor.message
        format.html { render :new }
        format.json { render json: @payroll_after_gross.errors, status: :unprocessable_entity }
      end
    end
  end

  # PUT/PATCH /payroll_after_grosses/:id
  def update
    interactor = UpdatePayrollAfterGross.call(model: @payroll_after_gross, params: payroll_after_grosses_params, updater: current_user)
    @payroll_after_gross = interactor.model

    respond_to do |format|
      if interactor.success?
        format.html { redirect_to admin_payroll_after_grosses_path, notice: t(".updated") }
        format.json { render :show, status: :ok, location: @payroll_after_gross }
      else
        flash[:error] = interactor.message
        format.html { render :edit }
        format.json { render json: @payroll_after_gross.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /payroll_after_gross/:id
  def destroy
    interactor = DeletePayrollAfterGross.call(model: @payroll_after_gross, updater: current_user)
    @payroll_after_gross = interactor.model

    respond_to do |format|
      if interactor.success?
        format.html { redirect_to admin_payroll_after_grosses_path, notice: t(".destroyed") }
        format.json { head :no_content }
      else
        flash[:error] = interactor.message
        format.html { redirect_to admin_payroll_after_grosses_path }
        format.json { head :no_content }
      end
    end
  end

  private
    def authorize_action
      authorize :admin, :actions
    end

    def payroll_after_grosses_params
      params.require(:payroll_after_gross).permit(
        :kpi_target_id,
        :number,
        :description,
        :fact_id,
        :comparison_operator,
        :target_amount,
        :is_unconditional,
        :is_after_gross,
        adjustment_after_gross: [
          :percentage_amount
        ],
        overrided_by_conditions: [],
      )
    end

    def set_payroll_after_gross
      @payroll_after_gross = AdjustmentCondition.find(params[:id])
      @adjustment_after_gross = @payroll_after_gross.adjustment_after_gross
    end

    def scoped_collection
      AdjustmentCondition.payroll_query.includes(:adjustment_after_gross).after_gross
    end
  # end of private block
end
