class Admin::FormDataController < Admin::BaseController
  # GET /particular_form/form_data
  def index
    @qb = policy_scope(Bank.all, policy_scope_class: AdminPolicy::Scope).ransack(params[:q])
    @qb.sorts = "name asc" if @qb.sorts.empty?
    @qr = policy_scope(ReferralChannel.kept, policy_scope_class: AdminPolicy::Scope).ransack(params[:q])
    @qr.sorts = "name asc" if @qr.sorts.empty?
    @banks = @qb.result.page(params[:page])
    @referral_channel = @qr.result.page(params[:page])
  end
end
