class Admin::UserMeritRedemptionsController < Admin::BaseController
  before_action :set_merit_redemption, only: [:show, :approve, :confirm, :reject]

  # GET '/admin/user_merit_redemptions'
  def index
    if params[:status].present?
      @filter = params[:status]
      statuses = MeritRedemption.statuses
      @q = policy_scope(base_collection, policy_scope_class: AdminPolicy::Scope).ransack(status_eq: statuses[@filter])
      @q.sorts = "updated_at desc" if @q.sorts.empty?
      @user_merit_redemptions = @q.result.page(params[:page])
      @redeem_policies = Policy.where(policy_type: "redeem")
    else
      @filter = "all"
      @q = policy_scope(base_collection, policy_scope_class: AdminPolicy::Scope).ransack(params[:q])
      @q.sorts = "updated_at desc" if @q.sorts.empty? # Ransack default (initial) sort order
      @user_merit_redemptions = @q.result.page(params[:page]) # Ransack search/filter results, paginated by <PERSON><PERSON><PERSON>.
      @redeem_policies = Policy.where(policy_type: "redeem")
    end
  end

  # GET '/admin/user_merit_redemptions/:id'
  def show
    authorize :admin, :actions
    @merit_reward = @user_merit_redemption.merit_reward
  end

  # POST '/admin/user_merit_redemptions/:id'
  # POST '/admin/user_merit_redemptions/:id.json'
  def approve
    authorize :admin, :actions

    interactor = ApproveMeritRedemption.call(
      model: @user_merit_redemption,
      approver: current_user,
    )

    @user_merit_redemption = interactor.model

    respond_to do |format|
      if interactor.success?
        format.html { redirect_to admin_user_merit_redemption_path(@user_merit_redemption), notice: t(".redemption_approved") }
        format.json { render :show, status: :created, location: @user_merit_redemption }
      else
        # Set error message to flash
        flash[:error] = interactor.message

        format.html { redirect_to admin_user_merit_redemption_path(@user_merit_redemption) }
        format.json { render json: @user_merit_redemption.errors, status: :unprocessable_entity }
      end
    end
  end

  # POST '/admin/user_merit_redemptions/:id'
  # POST '/admin/user_merit_redemptions/:id.json'
  def confirm
    authorize :admin, :actions

    interactor = ConfirmMeritRedemption.call(
      model: @user_merit_redemption,
      confirmer: current_user,
    )

    @user_merit_redemption = interactor.model

    respond_to do |format|
      if interactor.success?
        format.html { redirect_to admin_user_merit_redemption_path(@user_merit_redemption), notice: t(".redemption_confirmed") }
        format.json { render :show, status: :created, location: @user_merit_redemption }
      else
        # Set error message to flash
        flash[:error] = interactor.message

        format.html { redirect_to admin_user_merit_redemption_path(@user_merit_redemption) }
        format.json { render json: @user_merit_redemption.errors, status: :unprocessable_entity }
      end
    end
  end

  # POST '/admin/user_merit_redemptions/:id'
  # POST '/admin/user_merit_redemptions/:id.json'
  def reject
    authorize :admin, :actions

    interactor = RejectMeritRedemption.call(
      model: @user_merit_redemption,
      rejecter: current_user,
      reject_reason: params[:reject_reason]
    )

    @user_merit_redemption = interactor.model

    respond_to do |format|
      if interactor.success?
        format.html { redirect_to admin_user_merit_redemption_path(@user_merit_redemption), notice: t(".redemption_rejected") }
        format.json { render :show, status: :created, location: @user_merit_redemption }
      else
        # Set error message to flash
        flash[:error] = interactor.message

        format.html { redirect_to admin_user_merit_redemption_path(@user_merit_redemption) }
        format.json { render json: @user_merit_redemption.errors, status: :unprocessable_entity }
      end
    end
  end

  private

    def set_merit_redemption
      @user_merit_redemption = MeritRedemption.find(params[:id])
    end

    def base_collection
      MeritRedemption.includes(:merit_reward, :requester)
    end
  # end of private block
end
