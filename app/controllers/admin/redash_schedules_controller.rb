class Admin::RedashSchedulesController < Admin::BaseController
  before_action :set_redash_schedule, only: %i[show edit update destroy get_redash_column_names]
  before_action :authorize_action

  def index
    @q = policy_scope(RedashSchedule.includes(:jobs).kept, policy_scope_class: AdminPolicy::Scope).ransack(params[:q])
    @q.sorts = "created_at desc" if @q.sorts.empty?
    @redash_schedules = @q.result.page(params[:page])
  end

  def show
    # defined on before_action
  end

  def new
    @redash_schedule = RedashSchedule.new
  end

  def create
    interactor = ::Redash::CreateRedashSchedule.call(params: redash_schedule_params, creator: current_user)

    if interactor.success?
      redirect_to admin_redash_schedules_path, notice: t(".success")
    else
      @redash_schedule = interactor.model
      flash[:error] = interactor.message
      render :new, status: interactor.status_code
    end
  end

  def edit
    # defined on before_action
  end

  def update
    interactor = ::Redash::UpdateRedashSchedule.call(model: @redash_schedule, params: redash_schedule_params, updater: current_user)
    @redash_schedule = interactor.model

    if interactor.success?
      redirect_to admin_redash_schedule_path(@redash_schedule), notice: t(".success")
    else
      flash[:error] = interactor.message
      render :edit, status: interactor.status_code
    end
  end

  def destroy
    interactor = ::Redash::DeleteRedashSchedule.call(model: @redash_schedule, updater: current_user) # Interactor

    if interactor.success?
      redirect_to admin_redash_schedules_path, notice: t(".success")
    else
      flash[:error] = interactor.message
      render :show, status: interactor.status_code
    end
  end

  def search
    interactor = ::Redash::GetAllQueries.call(search_term: params[:search_term])

    if interactor.success?
      render json: interactor.result
    else
      render json: interactor.message, status: interactor.status
    end
  end

  def get_redash_column_names
    @column_names ||= [] # array to populate column names of selected query in Redash Schedule

    if @redash_schedule.batches.present?
      @column_names = @redash_schedule.batches[0].facts.pluck(:column).uniq
    end

    respond_to do |format|
      format.json {
        render json: { column_names: @column_names }
      }
    end
  end

  private

    def set_redash_schedule
      id = params[:id] || params[:redash_schedule_id].to_i
      @redash_schedule = RedashSchedule.find(id)
    end

    def authorize_action
      authorize :admin, :actions
    end

    def redash_schedule_params
      params.require(:redash_schedule).permit(:current_query_id, :period_start, :period_type)
    end
end
