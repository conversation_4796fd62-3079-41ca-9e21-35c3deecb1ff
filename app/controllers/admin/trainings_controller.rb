require Rails.root.join("app", "queries", "search", "available_keywords_query.rb").to_s

class Admin::TrainingsController < Admin::BaseController
  before_action :authorize_action
  before_action :set_type, only: %i[update_type create_module duplicate_module]
  before_action :set_module, only: %i[destroy_module update_module duplicate_module]

  # GET /trainings
  def index
    @q = policy_scope(TrainingType.includes(:roles), policy_scope_class: AdminPolicy::Scope).ransack(params[:q])
    @q.sorts = "seq"
    @training_types = @q.result
  end

  # PUT /trainings/tracks/:training_track_id/reorder
  def reorder_module
    interactor = ReorderModuleSequence.call(
      training_track_id: params[:training_track_id],
      initial_sequence: module_sequences_params[:initial_sequence],
      final_sequence: module_sequences_params[:final_sequence]
    )
    if interactor.success?
      redirect_to admin_trainings_path(), notice: t(".success")
    else
      flash[:error] = interactor.message
      redirect_to admin_trainings_path()
    end
  end

  # POST /trainings/tracks
  def create_type
    interactor = CreateTrainingType.call(name: params["name"], icon: params["icon"], role_tags: params["role_tags"])
    if interactor.failure?
      flash[:error] = interactor.message
    else
      flash[:notice] = t(".training_type_created")
    end
    redirect_to admin_trainings_path
  end

  # PATCH /trainings/tracks/:type_id
  def update_type
    interactor = UpdateTrainingType.call(model: @type, name: params["name"], icon: params["icon"], role_tags: params["role_tags"])
    if interactor.failure?
      flash[:error] = interactor.message
    else
      flash[:notice] = t(".training_type_updated")
    end
    redirect_to admin_trainings_path
  end

  # POST /trainings/tracks/:type_id/modules
  def create_module
    interactor = CreateTrainingModule.call(training_type: @type, name: params["name"], icon: params["icon"], search_keywords: params["search_keywords"])
    if interactor.failure?
      flash[:error] = interactor.message
    else
      flash[:notice] = t(".training_module_created")
    end
    redirect_to admin_trainings_path
  end

  # DELETE /trainings/modules/:module_id
  def destroy_module
    interactor = DeleteAndReorderTrainingModules.call(model: @module)
    if interactor.success?
      flash[:notice] = t(".training_module_deleted")
      redirect_to admin_trainings_path
    else
      flash[:error] = interactor.message
      redirect_to admin_trainings_path
    end
  end

  # PATCH /trainings/modules/:module_id
  def update_module
    interactor = UpdateTrainingModule.call(model: @module, name: params["name"], icon: params["icon"], search_keywords: params["search_keywords"])
    if interactor.failure?
      flash[:error] = interactor.message
    else
      flash[:notice] = t(".training_module_updated")
    end
    redirect_to admin_trainings_path
  end

  # POST /trainings/modules/:module_id/duplicate
  def duplicate_module
    interactor = ValidateTrainingModuleDuplication.call(modul_origin: @module, track_target: @type, creator: current_user)
    if interactor.success?
      redirect_to admin_training_submodules_index_path(module_id: @module.id), notice: t(".success", email: current_user.email)
    else
      flash[:error] = interactor.message
      redirect_to admin_training_submodules_index_path(module_id: @module.id)
    end
  end

  private

    def authorize_action
      authorize :admin, :actions
    end

    def module_sequences_params
      params.require(:modules).permit(:initial_sequence, :final_sequence)
    end

    def set_type
      @type = TrainingType.find(params[:type_id])
    rescue ActiveRecord::RecordNotFound
      flash[:error] = I18n.t(".admin.trainings.e.type_not_found")
      redirect_to admin_trainings_path
    end

    def set_module
      @module = TrainingModule.find(params[:module_id])
    rescue ActiveRecord::RecordNotFound
      flash[:error] = I18n.t(".admin.trainings.e.module_not_found")
      redirect_to admin_trainings_path
    end
end
