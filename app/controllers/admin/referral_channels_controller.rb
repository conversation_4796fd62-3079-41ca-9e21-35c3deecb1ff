class Admin::ReferralChannelsController < Admin::BaseController
  before_action :set_referral_channel, only: %i[show edit update destroy]

  # GET /referral_channels/new
  def new
    authorize :admin, :actions
    @referral_channel = ReferralChannel.new
  end

  # GET /referral_channels/1/edit
  def edit
    authorize :admin, :actions
  end

  # POST /referral_channels
  def create
    authorize :admin, :actions

    interactor = CreateReferralChannel.call(referral_channel_params)
    @referral_channel = interactor.model
    respond_to do |format|
      if interactor.success?
        format.html { redirect_to admin_form_data_path, notice: "Referral was successfully created." }
        format.json { render :show, status: :created, location: @referral_channel }
      else
        flash[:error] = interactor.message
        format.html { render :new }
        format.json { render json: @referral_channel.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /referral_channels/1
  def update
    authorize :admin, :actions

    interactor = UpdateReferralChannel.call(model: @referral_channel, params: referral_channel_params)
    @faq_category = interactor.model

    respond_to do |format|
      if interactor.success?
        format.html { redirect_to admin_form_data_path, notice: "Referral was successfully updated." }
        format.json { render :show, status: :ok, location: @referral_channel }
      else
        flash[:error] = interactor.message
        format.html { render :edit }
        format.json { render json: @referral_channel.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /referral_channels/1
  def destroy
    authorize :admin, :actions

    interactor = DeleteReferralChannel.call(model: @referral_channel)
    respond_to do |format|
      if interactor.success?
        format.html { redirect_to admin_form_data_url, notice: "Referral was successfully destroyed." }
        format.json { head :no_content }
      else
        format.html { redirect_to admin_form_data_url, notice: "Failed to delete Referral." }
        format.json { head :no_content }
      end
    end
  end

  private

    # Use callbacks to share common setup or constraints between actions.
    def set_referral_channel
      @referral_channel = ReferralChannel.find(params[:id])
    end

    # Never trust parameters from the scary internet, only allow the white list through.
    def referral_channel_params
      params.require(:referral_channel).permit(:name)
    end
end
