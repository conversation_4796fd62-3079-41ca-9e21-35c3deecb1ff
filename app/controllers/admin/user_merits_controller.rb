class Admin::UserMeritsController < Admin::BaseController
  before_action :set_user, only: %i[new create show_log]

  # GET /user_merits/:user_id/new
  def new
    @user_merit = UserMerit.new
    authorize :admin, :actions

    @merits = @role.merits
  end

  # POST /user_merits/:user_id
  def create
    authorize :admin, :actions

    interactor = AddUserMerit.call(user: @user, params: user_merit_params, creator: current_user) # Interactor
    @user_merit = interactor.model

    respond_to do |format|
      if interactor.success?
        format.html { redirect_to admin_user_merit_strikes_path, notice: t(".success_create") }
        format.json { render :show, status: :created, location: @merit }
      else
        flash[:error] = interactor.message
        @merits = @role.merits
        format.html { render :new, status: interactor.status_code }
        format.json { render json: @user_merit.errors, status: interactor.status_code }
      end
    end
  end

  # GET /user_merits/:user_id/log
  def show_log
    @user_merits = UserMerit.includes(:creator).where(user_id: @user.id)
    authorize :admin, :actions
  end

  private

    # Use callbacks to share common setup or constraints between actions.
    def set_user
      @user = User.friendly.find(params[:user_id])

      # Check user's role, if nil, redirect back with alert.
      @role = FmsRole.where(id: @user.fms_role_id).first
      if @role.nil?
        redirect_to admin_user_merit_strikes_path, alert: t(".missing_role")
      end
    end

    # Never trust parameters from the scary internet, only allow the white list through.
    def user_merit_params
      params.require(:user_merit).permit(:remark, :merit_id)
    end
end
