module Admin
  class PoliciesController < BaseController
    before_action :set_policy, only: %i[show edit update destroy]
    before_action :authorize_action, except: %i[index]

    # GET /policies
    def index
      @q = policy_scope(Policy, policy_scope_class: AdminPolicy::Scope).ransack(params[:q])
      @q.sorts = "updated_at desc" if @q.sorts.empty?
      @policies = @q.result.page(params[:page])
    end

    # GET /policies/1
    def show
      # defined on before_action
    end

    # GET /policies/new
    def new
      @policy = Policy.new
    end

    # GET /policies/1/edit
    def edit
      # defined on before_action
    end

    # POST /policies
    # POST /policies.json
    def create
      interactor = CreatePolicy.call(policy_params) # Interactor
      @policy = interactor.policy

      respond_to do |format|
        if interactor.success?
          format.html { redirect_to admin_policies_path, notice: "Policy was successfully created." }
          format.json { render :show, status: :created, location: @policy }
        else
          flash[:error] = interactor.message
          format.html { render :new }
          format.json { render json: @policy.errors, status: :unprocessable_entity }
        end
      end
    end

    # PATCH/PUT /policies/1
    # PATCH/PUT /policies/1.json
    def update
      respond_to do |format|
        if @policy.update(policy_params)
          format.html { redirect_to admin_policy_path, notice: "Policy was successfully updated." }
          format.json { render :show, status: :ok, location: @policy }
        else
          format.html { render :edit }
          format.json { render json: @policy.errors, status: :unprocessable_entity }
        end
      end
    end

    # DELETE /policies/1
    # DELETE /policies/1.json
    def destroy
      interactor = DeletePolicy.call(model: @policy)

      respond_to do |format|
        if interactor.success?
          format.html { redirect_to admin_policies_url, notice: "Policy was successfully destroyed." }
          format.json { head :no_content }
        else
          flash[:error] = interactor.message
          format.html { redirect_to admin_policies_path, alert: "Failed to delete policy" }
          format.json { head :no_content }
        end
      end
    end

    private

      # Use callbacks to share common setup or constraints between actions.
      def set_policy
        @policy = Policy.find(params[:id])
      end

      def authorize_action
        authorize :admin, :actions
      end

      # Never trust parameters from the scary internet, only allow the white list through.
      def policy_params
        params.require(:policy).permit(:content, :policy_type, :fms_role_id)
      end
  end
end
