module Admin
  class CardsController < BaseController
    include InputHelper

    before_action :set_card, only: %i[edit update]
    before_action :authorize_action, except: %i[index]

    # GET /admin/cards
    def index
      @q = policy_scope(Inventory.cards, policy_scope_class: AdminPolicy::<PERSON>ope)
      respond_to do |format|
        format.html
        format.json { render json: Admin::CardsDatatable.new(view_context, @q) }
      end
    end

    # GET /admin/cards/new
    def new
      @card = Inventory.new
    end

    # POST /admin/cards
    def create
      interactor = CreateCard.call(params: card_params, creator: current_user) # Interactor
      @card = interactor.model

      respond_to do |format|
        if interactor.success?
          format.html { redirect_to admin_cards_path, notice: t(".success") }
          format.json { render :show, status: :ok, location: @card }
        else
          flash[:error] = interactor.message
          format.html { render :new, status: interactor.status_code }
          format.json { render json: @card.errors, status: interactor.status_code }
        end
      end
    end

    # GET /admin/cards/:id/edit
    def edit
      # defined on before_action
    end

    # PATCH/PUT /admin/cards/:id
    def update
      interactor = UpdateCard.call(
        model: @card,
        params: card_params
      )
      @card = interactor.model

      respond_to do |format|
        if interactor.success?
          format.html { redirect_to admin_cards_path, notice: t(".success") }
          format.json { render :show, status: :ok, location: @card }
        else
          flash[:error] = interactor.message
          format.html { render :edit, status: interactor.status_code }
          format.json { render json: @card.errors, status: interactor.status_code }
        end
      end
    end

    # GET /admin/cards/export
    def export
      interactor = ExportCard.call

      if interactor.success?
        send_data interactor.csv_file, filename: "fms-cards-#{Date.today}.csv"
      else
        redirect_to admin_cards_path, alert: interactor.message
      end
    end

    private

      def set_card
        @card = Inventory.cards.find(params[:id])
      end

      def authorize_action
        authorize :admin, :actions
      end

      def card_params
        params[:card][:card_number] = params[:card][:card_number].remove("-")
        params[:card][:transaction_limit] = strip_currency_mask(params[:card][:transaction_limit])
        params[:card][:monthly_limit] = strip_currency_mask(params[:card][:monthly_limit])
        params.require(:card).permit(
          :card_number,
          :name_on_card,
          :valid_until_month,
          :valid_until_year,
          :transaction_limit,
          :monthly_limit,
          :is_active
        )
      end
  end
end
