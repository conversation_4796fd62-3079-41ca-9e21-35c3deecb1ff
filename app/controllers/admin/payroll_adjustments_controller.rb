class Admin::PayrollAdjustmentsController < Admin::BaseController
  before_action :set_payroll_adjustment, only: %i[show edit update destroy]
  before_action :authorize_action, except: %i[index]

  # GET /payroll_adjustments
  def index
    @q = policy_scope(scoped_collection, policy_scope_class: AdminPolicy::Scope).ransack(params[:q])
    @q.sorts = "created_at desc" if @q.sorts.empty?
    @payroll_adjustments = @q.result.page(params[:page])
  end

  # GET /payroll_adjustment/:id
  def show
    # defined in before action
  end

  # GET /payroll_adjustment/new
  def new
    @payroll_adjustment = AdjustmentCondition.new
    @adjustment_item = AdjustmentItem.new
  end

  # GET /payroll_adjustment/:id/edit
  def edit
    # defined in before action
  end

  # POST /payroll_adjustments
  def create
    interactor = CreatePayrollAdjustment.call(params: payroll_adjustments_params, creator: current_user)
    @payroll_adjustment = interactor.model

    if @payroll_adjustment.blank?
      @payroll_adjustment = AdjustmentCondition.new
      @adjustment_item = AdjustmentItem.new
    end

    respond_to do |format|
      if interactor.success?
        format.html { redirect_to admin_payroll_adjustments_path, notice: I18n.t("admin.payroll_adjustments.controller.created") }
        format.json { render :show, status: :created, location: @payroll_adjustment }
      else
        flash[:error] = interactor.message
        format.html { render :new }
        format.json { render json: @payroll_adjustment.errors, status: :unprocessable_entity }
      end
    end
  end

  # PUT/PATCH /payroll_adjustments/:id
  def update
    interactor = UpdatePayrollAdjustment.call(model: @payroll_adjustment, params: payroll_adjustments_params, updater: current_user)
    @payroll_adjustment = interactor.model

    respond_to do |format|
      if interactor.success?
        format.html { redirect_to admin_payroll_adjustments_path, notice: I18n.t("admin.payroll_adjustments.controller.updated") }
        format.json { render :show, status: :ok, location: @payroll_adjustment }
      else
        flash[:error] = interactor.message
        format.html { render :edit }
        format.json { render json: @payroll_adjustment.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /payroll_adjustments/:id
  def destroy
    interactor = DeletePayrollAdjustment.call(model: @payroll_adjustment, updater: current_user)
    @payroll_adjustment = interactor.model

    respond_to do |format|
      if interactor.success?
        format.html { redirect_to admin_payroll_adjustments_path, notice: I18n.t("admin.payroll_adjustments.controller.destroyed") }
        format.json { head :no_content }
      else
        flash[:error] = interactor.message
        format.html { redirect_to admin_payroll_adjustments_path }
        format.json { head :no_content }
      end
    end
  end

  private
    def set_payroll_adjustment
      @payroll_adjustment = AdjustmentCondition.find(params[:id])
      @adjustment_item = @payroll_adjustment.adjustment_item
    end

    def authorize_action
      authorize :admin, :actions
    end

    def payroll_adjustments_params
      params.require(:payroll_adjustment).permit(
        :kpi_target_id,
        :number,
        :description,
        :fact_id,
        :comparison_operator,
        :target_amount,
        :is_unconditional,
        :is_after_gross,
        adjustment_item: [
          :kpi_type,
          :multiplier_fact_id,
          :unit_amount,
          :max_cap_amount
        ],
        overrided_by_conditions: [],
      )
    end

    def scoped_collection
      AdjustmentCondition.payroll_query.includes(adjustment_item: :multiplier_fact).before_gross
    end
  # end of private block
end
