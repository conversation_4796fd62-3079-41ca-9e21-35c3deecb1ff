class Admin::FaqsController < Admin::BaseController
  before_action :set_admin_faq, only: %i[show edit update destroy]
  before_action :authorize_action, except: %i[index]

  # GET /admin/faqs/new
  def new
    @faq_category = FaqCategory.find(params[:faq_category_id])
    @faq = Faq.new
  end

  # GET /admin/faqs/1/edit
  def edit
    @faq_category = FaqCategory.find(params[:faq_category_id])
  end

  # POST /admin/faqs
  # POST /admin/faqs.json
  def create
    @faq_category = FaqCategory.find(params[:faq_category_id])
    interactor = CreateFaq.call(parent: @faq_category, params: faq_params)
    @faq = interactor.model

    respond_to do |format|
      if interactor.success?
        format.html { redirect_to admin_faq_categories_path, notice: "Faq was successfully created." }
        format.json { render :show, status: :created, location: @faq }
      else
        flash[:error] = interactor.message
        format.html { render :new }
        format.json { render json: @faq.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /admin/faqs/1
  # PATCH/PUT /admin/faqs/1.json
  def update
    interactor = UpdateFaq.call(model: @faq, params: faq_params)
    @faq = interactor.model

    respond_to do |format|
      if interactor.success?
        format.html { redirect_to admin_faq_categories_path, notice: "Faq was successfully updated." }
        format.json { render :show, status: :ok, location: @faq }
      else
        # @faq_category var is needed when there're validation errors
        @faq_category = FaqCategory.find(params[:faq_category_id])
        flash[:error] = interactor.message
        format.html { render :edit }
        format.json { render json: @faq.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /admin/faqs/1
  def destroy
    interactor = DeleteFaq.call(model: @faq)
    respond_to do |format|
      if interactor.success?
        format.html { redirect_to admin_faq_categories_url, notice: "Faq was successfully destroyed." }
        format.json { head :no_content }
      else
        format.html { redirect_to admin_faq_categories_url, notice: "Failed to delete Faq." }
        format.json { head :no_content }
      end
    end
  end

  private

    def set_admin_faq
      @faq = Faq.find(params[:id])
    end

    def authorize_action
      authorize :admin, :actions
    end

    def faq_params
      params.require(:faq).permit(:question, :answer)
    end
end
