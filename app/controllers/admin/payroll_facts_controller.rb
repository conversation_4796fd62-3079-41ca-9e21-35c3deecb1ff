class Admin::PayrollFactsController < Admin::BaseController
  before_action :authorize_action
  before_action :set_date, only: :index
  before_action :set_payroll_fact, only: %i[update destroy]
  before_action :set_payroll_fact_log, only: %i[download_csv]

  # GET /payroll_facts
  def index
    skip_policy_scope

    interactor = GetPayrollFacts.call(start_date: @start_date, end_date: @end_date)
    if interactor.success?
      @facts = interactor.records
      @column_names = interactor.payroll_fact_column_names
      @updaters_hash = interactor.updaters_hash
    else
      flash[:error] = interactor.message
      @facts = []
      @column_names = []
      @updaters_hash = {}
    end
  end

  # GET /payroll_facts/columns
  def columns_index
    @payroll_facts = policy_scope(PayrollFact.all, policy_scope_class: AdminPolicy::Scope)
  end

  # GET /payroll_facts/upload
  def upload_index
    @q = policy_scope(PayrollFactLog, policy_scope_class: AdminPolicy::Scope).ransack(params[:q])
    @q.sorts = "updated_at desc" if @q.sorts.empty?
    @payroll_fact_logs = @q.result.page(params[:page])
  end

  # POST /payroll_facts/
  def create
    interactor = CreatePayrollFact.call(params: payroll_fact_params)
    if interactor.success?
      flash[:notice] = t(".create_success")
    else
      flash[:error] = interactor.message
    end
    redirect_to admin_payroll_facts_columns_path
  end

  # PUT /payroll_facts/
  def update
    interactor = UpdatePayrollFact.call(model: @payroll_fact, params: payroll_fact_params)
    if interactor.success?
      flash[:notice] = t(".update_success")
    else
      flash[:error] = interactor.message
    end
    redirect_to admin_payroll_facts_columns_path
  end

  # DELETE /payroll_facts/
  def destroy
    interactor = DeletePayrollFact.call(model: @payroll_fact)
    if interactor.success?
      flash[:notice] = t(".delete_success")
    else
      flash[:error] = interactor.message
    end
    redirect_to admin_payroll_facts_columns_path
  end

  # POST /payroll_facts/generate_template
  def generate_template
    interactor = GeneratePayrollFactTemplate.call(column_names: payroll_fact_columns_params["names"])

    if interactor.success?
      send_data(interactor.file, filename: interactor.filename)
    else
      flash[:error] = interactor.message
      redirect_to admin_payroll_facts_columns_path
    end
  end

  # POST /payroll_facts/upload
  def upload
    interactor = ImportPayrollFacts.call(file: payroll_fact_file_param["file"], creator: current_user)
    if interactor.success?
      flash[:notice] = t(".success_upload", email: current_user.email)
    else
      flash[:error] = interactor.message
    end
    redirect_to admin_payroll_facts_upload_index_path
  end

  # GET /payroll_facts/download_csv/:id
  def download_csv
    if @payroll_fact_log.present? && @payroll_fact_log.uploaded_file.try(:url).present?
      date = @payroll_fact_log.updated_at.strftime("%d%b%y")
      data = open(@payroll_fact_log.uploaded_file.url)
      send_data(data.read, filename: "fms-payroll-fact-upload-#{date}.csv")
    else
      flash[:error] = t(".e.download_failed")
      redirect_to admin_payroll_facts_columns_path
    end
  end

  private
    def authorize_action
      authorize :admin, :actions
    end

    def set_date
      @start_date = params[:start_date] || Date.today - 1.month
      @end_date = params[:end_date] || Date.today
    end

    def set_payroll_fact
      @payroll_fact = PayrollFact.find(params[:id])
    rescue ActiveRecord::RecordNotFound
      flash[:error] = I18n.t("admin.payroll_facts.set_payroll_fact.payroll_fact_not_found")
      redirect_to admin_payroll_facts_columns_path
    end

    def set_payroll_fact_log
      @payroll_fact_log = PayrollFactLog.find(params[:id])
    rescue ActiveRecord::RecordNotFound
      flash[:error] = I18n.t("admin.payroll_facts.set_payroll_fact_log.payroll_fact_log_not_found")
      redirect_to admin_payroll_facts_upload_index_path
    end

    def payroll_fact_params
      params.require(:payroll_fact).permit(:name)
    end

    def payroll_fact_columns_params
      params.require(:payroll_fact).permit(:names)
    end

    def payroll_fact_file_param
      params.permit(:file)
    end
end
