module Admin
  class CardAssignmentsController < BaseController
    # GET /admin/user_inventories/:user_id/assign_card
    def new
      authorize :admin, :actions

      @user = User.friendly.find(params[:user_id])
      @cards = Inventory.assignable_cards
      if @cards.empty?
        redirect_to admin_user_inventory_path(@user), alert: t(".no_assignable_cards")
      end
    end

    # POST /admin/user_inventories/:user_id/assign_card
    def create
      authorize :admin, :actions

      @user = User.friendly.find(params[:user_id])

      interactor = AssignCard.call(
        user: @user,
        creator: current_user,
        params: card_assignment_params
      )
      @inventory_life_cycle = interactor.model

      respond_to do |format|
        if interactor.success?
          format.html { redirect_to admin_user_inventory_path(@user), notice: t(".success") }
          format.json { render :show, status: :ok, location: @inventory_life_cycle }
        else
          flash[:error] = interactor.message
          @cards = Inventory.assignable_cards
          format.html { render :new, status: interactor.status_code }
          format.json { render json: @inventory_life_cycle.errors, status: interactor.status_code }
        end
      end
    end

    private

      # Never trust parameters from the scary internet, only allow the white list through.
      def card_assignment_params
        params.require(:card_assignment).permit(:card_id, :attachment)
      end
  end
end
