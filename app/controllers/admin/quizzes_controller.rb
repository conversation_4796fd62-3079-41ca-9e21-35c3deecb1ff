class Admin::QuizzesController < Admin::BaseController
  before_action :authorize_action
  before_action :set_module, only: %i[new create duplicate]
  before_action :set_quiz, except: %i[new create]
  before_action :set_quiz_module, only: %i[show]
  before_action :set_quiz_type, only: %i[show]

  # GET /trainings/modules/:module_id/quizzes/new
  def new
    @quiz = Quiz.new
  end

  # POST /trainings/modules/:module_id/quizzes
  def create
    interactor = CreateQuiz.call(params: quiz_params)
    if interactor.success?
      redirect_to admin_training_submodules_index_path(module_id: @module.id),
        notice: t(".created")
    else
      @quiz = interactor.model
      flash[:error] = interactor.message
      render :new
    end
  end

  # GET /trainings/quizzes/:quiz_id/edit
  def edit
    # @quiz defined by set_quiz in before_action
  end

  # PATCH /trainings/quizzes/:quiz_id
  def update
    interactor = UpdateQuiz.call(model: @quiz, params: quiz_params)
    if interactor.success?
      redirect_to admin_quiz_path(),
        notice: t(".updated")
    else
      @quiz = interactor.model
      flash[:error] = interactor.message
      render :edit
    end
  end

  # GET /trainings/quizzes/:id
  def show
    @questions = @quiz.questions.order(:created_at)
  end

  # POST /trainings/quizzes/:quiz_id/duplicate
  def duplicate
    interactor = ValidateQuizDuplication.call(quiz: @quiz, module_target: @module, creator: current_user)

    if interactor.success?
      flash[:notice] = t(".success", email: current_user.email)
    else
      flash[:error] = interactor.message
    end
    redirect_to admin_training_submodules_index_path(module_id: @quiz.training_module_id)
  end

  private

    def authorize_action
      authorize :admin, :actions
    end

    def set_module
      @module = TrainingModule.find(params[:module_id])
    rescue ActiveRecord::RecordNotFound
      flash[:error] = I18n.t("admin.training_submodules.e.invalid_training_module")
      redirect_to admin_trainings_path
    end

    def quiz_params
      params.require(:quiz)
        .permit(:name, :description, :training_module_id, :questions_per_session, :success_score_threshold, :published_at, :time_limit, :attempt_limit)
    end

    def set_quiz
      @quiz = Quiz.find(params[:id] || params[:quiz_id])
    rescue ActiveRecord::RecordNotFound
      flash[:error] = I18n.t("admin.quizzes.e.invalid_quiz")
      redirect_to admin_trainings_path
    end

    def set_quiz_module
      @module = @quiz.training_module
      if @module.nil?
        flash[:error] = I18n.t("admin.quizzes.e.invalid_module", quiz_name: @quiz.name)
        redirect_to admin_trainings_path
      end
    end

    def set_quiz_type
      @type = @module.training_type
      if @type.nil?
        flash[:error] = I18n.t("admin.quizzes.e.invalid_track", quiz_name: @quiz.name)
        redirect_to admin_trainings_path
      end
    end
end
