class Admin::PerformanceSchemesController < Admin::BaseController
  before_action :set_performance_scheme, only: %i[show edit update]
  before_action :authorize_action, except: %i[index]

  def index
    @q = policy_scope(PerformanceScheme.includes(:fms_role, :creator), policy_scope_class: AdminPolicy::Scope).ransack(params[:q])
    @q.sorts = "updated_at desc" if @q.sorts.empty?
    @performance_schemes = @q.result.page(params[:page])
  end

  def show
    # defined on before_action
  end

  def new
    @performance_scheme = PerformanceScheme.new
  end

  def create
    interactor = CreatePerformanceScheme.call(params: kpi_params, user: current_user)
    @performance_scheme = interactor.model

    respond_to do |format|
      if interactor.success?
        format.html { redirect_to admin_performance_schemes_path, notice: t(".create_successful") }
        format.json { head :no_content }
      else
        flash[:error] = interactor.message
        format.html { render :new }
        format.json { head :no_content }
      end
    end
  end

  def edit
    # defined on before_action
  end

  def update
    interactor = UpdatePerformanceScheme.call(model: @performance_scheme, params: kpi_params, user: current_user)
    @performance_scheme = interactor.model

    respond_to do |format|
      if interactor.success?
        format.html { redirect_to admin_performance_scheme_path(@performance_scheme), notice: t(".update_successful") }
        format.json { head :show, status: :ok, location: @performance_scheme }
      else
        flash[:error] = interactor.message
        format.html { render :edit }
        format.json { render json: @performance_scheme.errors, status: :unprocessable_entity }
      end
    end
  end

  private

    def set_performance_scheme
      @performance_scheme = PerformanceScheme.find(params[:id])
    end

    def authorize_action
      authorize :admin, :actions
    end

    def kpi_params
      params.require(:performance_scheme).permit(:fms_role_id, :period_start, :period_type, :is_active, kpis: [])
    end
end
