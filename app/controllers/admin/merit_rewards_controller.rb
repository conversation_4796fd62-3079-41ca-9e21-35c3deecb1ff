module Admin
  class MeritRewardsController < BaseController
    before_action :set_merit_reward, only: %i[show edit update destroy]

    # GET /merit_rewards
    def index
      @q = policy_scope(MeritReward, policy_scope_class: AdminPolicy::Scope).ransack(params[:q])
      @q.sorts = "updated_at desc" if @q.sorts.empty?
      @merit_rewards = @q.result.page(params[:page])
    end

    # GET /merit_rewards/1
    def show
      authorize :admin, :actions
    end

    # GET /merit_rewards/new
    def new
      @merit_reward = MeritReward.new
      authorize :admin, :actions
    end

    # GET /merit_rewards/1/edit
    def edit
      authorize :admin, :actions
    end

    # POST /merit_rewards
    # POST /merit_rewards.json
    def create
      authorize :admin, :actions

      interactor = CreateMeritReward.call(merit_reward_params) # interactor
      @merit_reward = interactor.merit_reward

      respond_to do |format|
        if interactor.success?
          format.html { redirect_to admin_merit_rewards_path, notice: t(".success") }
          format.json { render :show, status: :ok, location: @merit_reward }
        else
          flash[:error] = interactor.message
          format.html { render :new, status: interactor.status_code }
          format.json { render json: @merit_reward.errors, status: interactor.status_code }
        end
      end
    end

    # PATCH/PUT /merit_rewards/1
    # PATCH/PUT /merit_rewards/1.json
    def update
      authorize :admin, :actions

      interactor = UpdateMeritReward.call(model: @merit_reward, params: merit_reward_params)
      @merit_reward = interactor.model

      respond_to do |format|
        if interactor.success?
          format.html { redirect_to admin_merit_reward_path, notice: t(".success") }
          format.json { render :show, status: :ok, location: @merit_reward }
        else
          flash[:error] = interactor.message
          format.html { render :edit, status: interactor.status_code }
          format.json { render json: @merit_reward.errors, status: interactor.status_code }
        end
      end
    end

    # DELETE /merit_rewards/1
    # DELETE /merit_rewards/1.json
    def destroy
      authorize :admin, :actions

      interactor = DeleteMeritReward.call(model: @merit_reward) # interactor

      respond_to do |format|
        if interactor.success?
          format.html { redirect_to admin_merit_rewards_url, notice: t(".success") }
          format.json { head :ok }
        else
          format.html { render :show, status: interactor.status_code, alert: t(".failed") }
          format.json { head interactor.status_code }
        end
      end
    end

    private

      # Use callbacks to share common setup or constraints between actions.
      def set_merit_reward
        @merit_reward = MeritReward.find(params[:id])
      end

      # Never trust parameters from the scary internet, only allow the white list through.
      def merit_reward_params
        params.require(:merit_reward).permit(:name, :required_point, :redemption_limit, :description)
      end
  end
end
