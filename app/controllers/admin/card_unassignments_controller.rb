module Admin
  class CardUnassignmentsController < BaseController
    # POST /admin/user_inventories/:user_id/unassign_card
    def create
      authorize :admin, :actions

      @user = User.friendly.find(params[:user_id])

      interactor = UnassignCard.call(
        user: @user,
        creator: current_user,
        card_id: params[:card_id]
      )
      @inventory_life_cycle = interactor.model

      respond_to do |format|
        if interactor.success?
          format.html { redirect_to admin_user_inventory_path(@user), notice: t(".success") }
          format.json { render :show, status: :ok, location: @inventory_life_cycle }
        else
          flash[:error] = interactor.message
          format.html { render template: "admin/user_inventories/show", status: interactor.status_code }
          format.json { render json: @inventory_life_cycle.errors, status: interactor.status_code }
        end
      end
    end
  end
end
