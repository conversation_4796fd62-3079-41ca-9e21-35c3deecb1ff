class Admin::QuizAppealsController < Admin::BaseController
  before_action :authorize_action
  before_action :set_appeal, only: %i[approve reject]

  # GET /quiz_appeals
  def index
    @q = policy_scope(QuizAttemptAppeal.all, policy_scope_class: AdminPolicy::<PERSON><PERSON>)
    respond_to do |format|
      format.html
      format.json { render json: Admin::QuizAppealsDatatable.new(view_context, @q) }
    end
  end

  # POST /quiz_appeals/:id/approve
  def approve
    interactor = ApproveQuizAttemptAppeal.call(model: @appeal, approver: current_user)
    if interactor.success?
      flash[:notice] = t(".appeal_approved")
    else
      flash[:error] = interactor.message
    end
    redirect_to admin_quiz_appeals_path
  end

  # POST /quiz_appeals/:id/reject
  def reject
    interactor = RejectQuizAttemptAppeal.call(model: @appeal, rejecter: current_user)
    if interactor.success?
      flash[:notice] = t(".appeal_rejected")
    else
      flash[:error] = interactor.message
    end
    redirect_to admin_quiz_appeals_path
  end

  private
    def authorize_action
      authorize :admin, :actions
    end

    def set_appeal
      @appeal = QuizAttemptAppeal.find(params[:appeal_id])
    rescue ActiveRecord::RecordNotFound
      flash[:error] = I18n.t(".admin.quiz_appeals.e.appeal_not_found")
      redirect_to admin_quiz_appeals_path
    end
end
