class Admin::LettersController < Admin::BaseController
  before_action :set_letter, only: %i[show edit update destroy]
  before_action :authorize_action, except: %i[index]

  # GET /admin/letters
  def index
    @q = policy_scope(Letter, policy_scope_class: AdminPolicy::Scope).ransack(params[:q])
    @q.sorts = "updated_at desc" if @q.sorts.empty?
    @letters = @q.result
  end

  # GET /admin/letters/1
  def show
    # defined on before_action
  end

  # GET /admin/letters/new
  def new
    @letter = Letter.new
  end

  # GET /admin/letters/1/edit
  def edit
    # defined on before_action
  end

  # POST /admin/letters
  def create
    interactor = CreateLetter.call(params: letter_params, creator: current_user)
    @letter = interactor.model

    respond_to do |format|
      if interactor.success?
        format.html { redirect_to admin_letters_path, notice: t(".success") }
        format.json { render :show, status: :created, location: @letter }
      else
        flash[:error] = interactor.message
        format.html { render :new }
        format.json { render json: @letter.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /admin/letters/1
  def update
    interactor = UpdateLetter.call(model: @letter, params: letter_params, updater: current_user)
    @letter = interactor.model

    respond_to do |format|
      if interactor.success?
        format.html { redirect_to admin_letters_path, notice: t(".success") }
        format.json { render :show, status: :ok, location: @letter }
      else
        flash[:error] = interactor.message
        format.html { render :edit }
        format.json { render json: @letter.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /admin/letters/1
  def destroy
    interactor = DeleteLetter.call(model: @letter, updater: current_user)
    @letter = interactor.model

    respond_to do |format|
      if interactor.success?
        format.html { redirect_to admin_letters_path, notice: t(".success") }
        format.json { head :no_content }
      else
        format.html { redirect_to admin_letters_path, alert: "Failed to delete letter." }
        format.json { render json: @letter.errors, status: :unprocessable_entity }
      end
    end
  end

  private

    def set_letter
      @letter = Letter.friendly.find(params[:id])
    end

    def authorize_action
      authorize :admin, :actions
    end

    def letter_params
      params.require(:letter).permit(
        :title,
        :description,
        :content,
        :attachment,
        :signing_method,
        :displayed_in_onboarding,
        :displayed_in_offboarding,
        :published_at
      )
    end
end
