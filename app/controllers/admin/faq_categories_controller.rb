class Admin::FaqCategoriesController < Admin::BaseController
  before_action :set_faq_category, only: %i[show edit update destroy]

  # GET /faq_categories
  def index
    @q = policy_scope(FaqCategory.with_faqs, policy_scope_class: AdminPolicy::Scope).ransack(params[:q])
    @q.sorts = "names asc" if @q.sorts.empty?
    @faq_categories = @q.result.page(params[:page])
  end

  # GET /faq_categories/new
  def new
    @faq_category = FaqCategory.new
    authorize :admin, :actions
  end

  # GET /faq_categories/1/edit
  def edit
    authorize :admin, :actions
  end

  # POST /faq_categories
  # POST /faq_categories.json
  def create
    authorize :admin, :actions

    interactor = CreateFaqCategory.call(faq_category_params)
    @faq_category = interactor.model

    respond_to do |format|
      if interactor.success?
        format.html { redirect_to admin_faq_categories_path, notice: "Faq category was successfully created." }
        format.json { render :show, status: :created, location: @faq_category }
      else
        flash[:error] = interactor.message
        format.html { render :new }
        format.json { render json: @faq_category.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /faq_categories/1
  # PATCH/PUT /faq_categories/1.json
  def update
    authorize :admin, :actions

    interactor = UpdateFaqCategory.call(model: @faq_category, params: faq_category_params)
    @faq_category = interactor.model

    respond_to do |format|
      if interactor.success?
        format.html { redirect_to admin_faq_categories_path, notice: "Faq category was successfully updated." }
        format.json { render :show, status: :ok, location: @faq_category }
      else
        flash[:error] = interactor.message
        format.html { render :edit }
        format.json { render json: @faq_category.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /faq_categories/1
  # DELETE /faq_categories/1.json
  def destroy
    authorize :admin, :actions

    interactor = DeleteFaqCategory.call(model: @faq_category)
    respond_to do |format|
      if interactor.success?
        format.html { redirect_to admin_faq_categories_url, notice: "Faq category was successfully destroyed." }
        format.json { head :no_content }
      else
        format.html { redirect_to admin_faq_categories_url, notice: "Failed to delete Faq category." }
        format.json { head :no_content }
      end
    end
  end

  private

    # Use callbacks to share common setup or constraints between actions.
    def set_faq_category
      @faq_category = FaqCategory.find(params[:id])
    end

    # Never trust parameters from the scary internet, only allow the white list through.
    def faq_category_params
      params.require(:faq_category).permit(:name)
    end
end
