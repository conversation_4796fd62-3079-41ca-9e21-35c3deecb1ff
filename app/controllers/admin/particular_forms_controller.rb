class Admin::ParticularFormsController < Admin::BaseController
  before_action :set_particular_form, only: %i[show destroy]
  before_action :authorize_action, exclude: %i[index index_trash]

  # GET /particular_forms
  def index
    @q = policy_scope(ParticularForm.kept, policy_scope_class: AdminPolicy::Scope).ransack(params[:q])
    @q.sorts = "created_at desc" if @q.sorts.empty?
    @particular_forms = @q.result.page(params[:page])
  end

  def show
    # defined by before_action
  end

  def destroy
    interactor = DeleteParticularForm.call(model: @particular_form)
    @particular_form = interactor.model

    respond_to do |format|
      if interactor.success?
        format.html { redirect_to admin_particular_forms_path, notice: t(".particular_form_destroy_notice") }
        format.json { render :show, status: :ok, location: @particular_form }
      else
        flash[:error] = interactor.message
        format.html { redirect_to admin_particular_forms_path }
        format.json { render json: @particular_form.errors, status: :unprocessable_entity }
      end
    end
  end

  private

    # Use callbacks to share common setup or constraints between actions.
    def set_particular_form
      @particular_form = ParticularForm.find(params[:id])
    end

    def authorize_action
      authorize :admin, :actions
    end
end
