class Admin::QuizQuestionsController < Admin::BaseController
  before_action :authorize_action
  before_action :set_quiz

  before_action :set_quiz_question, only: %i[edit update destroy]
  before_action :set_quiz_module, only: %i[new create edit update]
  before_action :set_quiz_type, only: %i[new create edit update]
  before_action :set_data_question_answers, only: %i[edit]

  # GET /trainings/quizzes/:quiz_id/quiz_questions/new
  def new
    # set quiz in before action
  end

  # POST /trainings/quizzes/:quiz_id/quiz_questions
  def create
    interactor = CreateQuizQuestionAnswers.call(quiz: @quiz, params: quiz_question_answers_params.to_h)
    if interactor.success?
      redirect_to admin_quiz_path(@quiz), notice: t(".success")
    else
      @data = quiz_question_answers_params
      flash[:error] = interactor.message
      render :new
    end
  end

  # DELETE /trainings/quizzes/:quiz_id/quiz_questions/:question_id
  def destroy
    interactor = DeleteQuizQuestionAnswers.call(quiz_question: @quiz_question)
    if interactor.success?
      flash[:notice] = t(".success")
    else
      flash[:error] = interactor.message
    end
    redirect_to admin_quiz_path(@quiz)
  end

  # GET /trainings/quizzes/:quiz_id/quiz_questions/:question_id/edit
  def edit
    # @data defined by set_data_question_answers in before_action
  end

  # PATCH /trainings/quizzes/:quiz_id/quiz_questions/:question_id
  def update
    interactor = UpdateQuizQuestionAnswers.call(model: @quiz_question, params: quiz_question_answers_params.to_h)
    if interactor.success?
      redirect_to admin_quiz_path(@quiz_question.quiz.id),
        notice: t(".updated")
    else
      @data = quiz_question_answers_params
      flash[:error] = interactor.message
      render :edit
    end
  end

  private

    def authorize_action
      authorize :admin, :actions
    end

    def set_quiz_question
      @quiz_question = QuizQuestion.find(params[:question_id])
    rescue ActiveRecord::RecordNotFound
      flash[:error] = I18n.t("admin.quiz_questions.e.invalid_quiz_questions")
      redirect_to admin_trainings_path
    end

    def set_quiz
      @quiz = Quiz.find(params[:quiz_id])
    rescue ActiveRecord::RecordNotFound
      flash[:error] = I18n.t("admin.quiz_questions.e.invalid_quiz")
      redirect_to admin_trainings_path
    end

    def quiz_question_answers_params
      params.permit(:question_body, :correct_answer_index, quiz_answers: ["0", "1", "2", "3"])
    end

    def set_data_question_answers
      quiz_answers_objects = QuizAnswer.where(quiz_question_id: @quiz_question.id)
      quiz_answers_hash = {}
      correct_answer_index = 0

      quiz_answers_objects.each do |quiz_answer|
        seq = (quiz_answer.seq - 1).to_s
        new_hash = { seq => quiz_answer.body }
        quiz_answers_hash = quiz_answers_hash.merge(new_hash)
        correct_answer_index = seq if quiz_answer.is_correct?
      end

      @data = {
        question_body: @quiz_question.body,
        correct_answer_index: correct_answer_index,
        quiz_answers: quiz_answers_hash
      }
    end

    def set_quiz_module
      @module = @quiz.training_module
      if @module.nil?
        flash[:error] = I18n.t("admin.quiz_questions.e.invalid_module", quiz_name: @quiz.name)
        redirect_to admin_trainings_path
      end
    end

    def set_quiz_type
      @type = @module.training_type
      if @type.nil?
        flash[:error] = I18n.t("admin.quiz_questions.e.invalid_track", quiz_name: @quiz.name)
        redirect_to admin_trainings_path
      end
    end
end
