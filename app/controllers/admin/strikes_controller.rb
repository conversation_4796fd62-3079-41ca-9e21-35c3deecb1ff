module Admin
  class StrikesController < BaseController
    before_action :set_strike, only: %i[show edit update destroy]
    before_action :authorize_action, except: %i[index]

    # GET /admin/strikes
    def index
      @q = policy_scope(Strike, policy_scope_class: AdminPolicy::Scope).ransack(params[:q])
      @q.sorts = "updated_at desc" if @q.sorts.empty?
      @strikes = @q.result.page(params[:page])
      @strike_policies = Policy.where(policy_type: "strike")
    end

    # GET /admin/strikes/1
    def show
      # defined on before_action
    end

    # GET /admin/strikes/new
    def new
      @strike = Strike.new
    end

    # GET /admin/strikes/1/edit
    def edit
      # defined on before_action
    end

    # POST /admin/strikes
    # POST /admin/strikes.json
    def create
      interactor = CreateStrike.call(strike_params) # Interactor
      @strike = interactor.model

      respond_to do |format|
        if interactor.success?
          format.html { redirect_to admin_strike_path(@strike), notice: t(".success") }
          format.json { render :show, status: :ok, location: @strike }
        else
          flash[:error] = interactor.message
          format.html { render :new, status: interactor.status_code }
          format.json { render json: @strike.errors, status: interactor.status_code }
        end
      end
    end

    # PATCH/PUT /admin/strikes/1
    # PATCH/PUT /admin/strikes/1.json
    def update
      interactor = UpdateStrike.call(model: @strike, params: strike_params) # Interactor
      @strike = interactor.model

      respond_to do |format|
        if interactor.success?
          format.html { redirect_to admin_strike_path(@strike), notice: t(".success") }
          format.json { render :show, status: :ok, location: @strike }
        else
          flash[:error] = interactor.message
          format.html { render :edit, status: interactor.status_code }
          format.json { render json: @strike.errors, status: interactor.status_code }
        end
      end
    end

    # DELETE /admin/strikes/1
    # DELETE /admin/strikes/1.json
    def destroy
      interactor = DeleteStrike.call(model: @strike) # Interactor

      respond_to do |format|
        if interactor.success?
          format.html { redirect_to admin_strikes_url, notice: t(".success") }
          format.json { head :ok }
        else
          format.html { render :show, status: interactor.status_code, alert: t(".failed") }
          format.json { head interactor.status_code }
        end
      end
    end

    private

      def set_strike
        @strike = Strike.find(params[:id])
      end

      def authorize_action
        authorize :admin, :actions
      end

      def strike_params
        params.require(:strike).permit(:name, :point)
      end
  end
end
