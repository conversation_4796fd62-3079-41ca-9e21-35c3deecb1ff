class Admin::BanksController < Admin::BaseController
  before_action :set_bank, only: %i[edit update destroy]
  before_action :authorize_action

  # GET /particular_form/banks/new
  def new
    @bank = Bank.new
  end

  # POST /particular_form/banks
  def create
    interactor = CreateBank.call(name: bank_params[:name])
    @bank = interactor.model

    respond_to do |format|
      if interactor.success?
        format.html { redirect_to admin_form_data_path, notice: interactor.notice }
        format.json { render :show, status: :created, location: @bank }
      else
        flash[:error] = interactor.message
        format.html { render :new }
        format.json { render json: @bank.errors, status: :unprocessable_entity }
      end
    end
  end

  # GET /particular_form/banks/:id/edit
  def edit
    # set_bank defined on before_action
  end

  # PATCH/PUT /particular_form/banks/:id
  def update
    interactor = UpdateBank.call(model: @bank, name: bank_params[:name])
    @bank = interactor.model

    respond_to do |format|
      if interactor.success?
        format.html { redirect_to admin_form_data_path, notice: "Bank was successfully updated." }
        format.json { render :show, status: :ok, location: @bank }
      else
        flash[:error] = interactor.message
        format.html { render :edit }
        format.json { render json: @bank.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /particular_form/banks/:id
  def destroy
    interactor = DeleteBank.call(model: @bank)
    @bank = interactor.model

    respond_to do |format|
      if interactor.success?
        format.html { redirect_to admin_form_data_path, notice: "Bank was successfully deleted." }
        format.json { render :show, status: :ok, location: @bank }
      else
        flash[:error] = interactor.message
        format.html { redirect_to admin_form_data_path }
        format.json { render json: @bank.errors, status: :unprocessable_entity }
      end
    end
  end

  private

    # Use callbacks to share common setup or constraints between actions.
    def set_bank
      @bank = Bank.find(params[:id])
    end

    def authorize_action
      authorize :admin, :actions
    end

    # Never trust parameters from the scary internet, only allow the white list through.
    def bank_params
      params.require(:bank).permit(:name)
    end
end
