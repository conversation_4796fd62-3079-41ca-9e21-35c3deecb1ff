class Admin::UserMeritStrikesController < Admin::BaseController
  # GET /admin/user_merit_strikes
  def index
    @q = policy_scope(User.user, policy_scope_class: AdminPolicy::<PERSON>ope)
    respond_to do |format|
      format.html
      format.json { render json: Admin::UserMeritStrikesDatatable.new(view_context, @q) }
    end
  end

  # GET /admin/user_merit_strikes/export
  def export
    authorize :admin, :actions
    send_data User.merit_strike_summary_csv, filename: "fms-bonus-penalty-summary-#{Date.today}.csv"
  end
end
