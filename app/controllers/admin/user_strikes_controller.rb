require Rails.root.join("app", "queries", "user_strikes_query.rb").to_s

class Admin::UserStrikesController < Admin::BaseController
  include DateFilterHelper

  before_action :authorize_action

  before_action :set_user, only: %i[new create show_log bulk_delete]
  before_action :set_user_strike, only: %i[destroy]
  append_before_action :set_show_log_default_date_range_params, :check_date_range_params, only: :show_log

  # GET /user_strikes/new
  def new
    @user_strike = UserStrike.new
    @strikes = Strike.all
  end

  # POST /user_strike/:user_id
  def create
    interactor = AddUserStrike.call(user: @user, params: user_strike_params, creator: current_user) # Interactor
    @user_strike = interactor.model

    respond_to do |format|
      if interactor.success?
        format.html { redirect_to admin_user_merit_strikes_path, notice: t(".success_create") }
        format.json { render :show, status: :created, location: @strike }
      else
        flash[:error] = interactor.message
        @strikes = Strike.all
        format.html { render :new, status_code: interactor.status_code }
        format.json { render json: @user_strike.errors, status: interactor.status_code }
      end
    end
  end

  # GET /user_strikes/:user_id/log
  def show_log
    @datatable_filters = [@start_date, @end_date]
    base_query = UserStrikesQuery.call(with_discarded: true, start_date: @start_date, end_date: @end_date).owned_by(@user)

    respond_to do |format|
      format.html do
        @user_strikes = base_query

        render "admin/user_strikes/show_log"
      end
      format.json do
        @user_strikes = Admin::UserStrikeDatatable.new(view_context, base_query, { start_date: @start_date, end_date: @end_date })

        render json: @user_strikes, status: :ok
      end
    end
  end

  # DELETE /user_strikes_delete/:id
  def destroy
    interactor = Organizers::DeleteUserStrike.call(model: @user_strike, destroyer: current_user)

    if interactor.success?
      flash[:notice] = t(".delete.success")
    else
      flash[:error] = interactor.message
    end

    redirect_to admin_user_strike_logs_path(user_id: @user_strike.user_id)
  end

  # POST /user_strikes/:user_id/bulk_delete
  def bulk_delete
    interactor = BulkDeleteUserStrike.call(user_strike_ids: params[:bulk_ids], user: @user, destroyer: current_user)

    if interactor.success?
      flash[:info] = t(".success", count: interactor.deleted_count)
    else
      flash[:error] = t(".action_failed", count: interactor.failed_count)
    end

    redirect_to admin_user_strike_logs_path(@user)
  end

  private

    # Use callbacks to share common setup or constraints between actions.
    def set_user
      @user = User.friendly.find(params[:user_id])
    end

    # Never trust parameters from the scary internet, only allow the white list through.
    def user_strike_params
      params.require(:user_strike).permit(:remark, :reference, :strike_id)
    end

    def authorize_action
      authorize :admin, :actions
    end

    def set_user_strike
      @user_strike = UserStrike.find(params[:id])
    rescue ActiveRecord::RecordNotFound
      flash[:error] = t(".e.user_strike_not_found")
      redirect_to admin_user_merit_strikes_path
    end

    # to config default date range on first penalty logs page load
    def set_show_log_default_date_range_params
      params[:start_date] ||= 6.months.ago.to_date
      params[:end_date] ||= Date.today
    end

    def check_date_range_params
      validate_date_range(Date.today - 6.months.ago.to_date)
    end
end
