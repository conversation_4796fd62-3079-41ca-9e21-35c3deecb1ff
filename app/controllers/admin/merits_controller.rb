module Admin
  class MeritsController < BaseController
    before_action :set_merit, only: %i[show edit update destroy]
    before_action :authorize_action, except: %i[index]

    # GET /admin/merits
    def index
      @q = policy_scope(Merit.includes(:fms_roles), policy_scope_class: AdminPolicy::Scope)
        .ransack(params[:q])
      @q.sorts = "names desc" if @q.sorts.empty?
      @merits = @q.result.page(params[:page])
      @merit_policies = Policy.where(policy_type: "merit")
    end

    # GET /admin/merits/1
    def show
      # defined on before_action
    end

    # GET /admin/merits/new
    def new
      @roles = FmsRole.all
      @merit = Merit.new
    end

    # GET /admin/merits/1/edit
    def edit
      @roles = FmsRole.all
    end

    # POST /admin/merits
    # POST /admin/merits.json
    def create
      interactor = CreateMerit.call(merit_params) # Interactor
      @merit = interactor.merit

      respond_to do |format|
        if interactor.success?
          format.html { redirect_to admin_merits_path(@merit), notice: t(".success") }
          format.json { render :show, status: :ok, location: @merit }
        else
          flash[:error] = interactor.message
          @roles = FmsRole.all
          format.html { render :new, status: interactor.status_code }
          format.json { render json: @merit.errors, status: interactor.status_code }
        end
      end
    end

    # PATCH/PUT /admin/merits/1
    # PATCH/PUT /admin/merits/1.json
    def update
      interactor = UpdateMerit.call(model: @merit, params: merit_params) # Interactor
      @merit = interactor.model
      respond_to do |format|
        if interactor.success?
          format.html { redirect_to admin_merits_path(@merit), notice: t(".success") }
          format.json { render :show, status: :ok, location: @merit }
        else
          flash[:error] = interactor.message
          @roles = FmsRole.all
          format.html { render :edit, status: interactor.status_code }
          format.json { render json: @merit.errors, status: interactor.status_code }
        end
      end
    end

    # DELETE /admin/merits/1
    # DELETE /admin/merits/1.json
    def destroy
      interactor = DeleteMerit.call(model: @merit) # Interactor

      respond_to do |format|
        if interactor.success?
          format.html { redirect_to admin_merits_url, notice: t(".success") }
          format.json { head :ok }
        else
          @roles = FmsRole.all
          format.html { render :show, status: interactor.status_code, alert: t(".failed") }
          format.json { head interactor.status_code }
        end
      end
    end

    private

      def set_merit
        @merit = Merit.find(params[:id])
      end

      def authorize_action
        authorize :admin, :actions
      end

      def merit_params
        # :fms_role_ids param contains array
        # and should be declared as :fms_role_ids => []
        params.require(:merit).permit(:name, :point, fms_role_ids: [])
      end
  end
end
