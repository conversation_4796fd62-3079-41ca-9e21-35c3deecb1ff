class Admin::NewsController < Admin::BaseController
  before_action :set_news, only: %i[show edit update destroy]
  before_action :authorize_action, except: %i[index]

  # GET /admin/news
  def index
    @q = policy_scope(News.includes(:author), policy_scope_class: AdminPolicy::Scope).ransack(params[:q])
    @q.sorts = "updated_at desc" if @q.sorts.empty?
    @news = @q.result.page(params[:page])
  end

  # GET /admin/news/1
  def show
    # defined on before_action
  end

  # GET /admin/news/new
  def new
    @news = News.new
  end

  # GET /admin/news/1/edit
  def edit
    # defined on before_action
  end

  # POST /admin/news
  def create
    interactor = CreateNewsPost.call(
      title: news_params[:title],
      body: news_params[:body],
      attachment: news_params[:attachment],
      published_at: news_params[:published_at],
      author: current_user,
      commentable: news_params[:commentable],
      host: request.base_url
    )
    @news = interactor.model

    if interactor.success?
      flash[:notice] =
        if interactor.notification_error_message.nil?
          t(".success")
        else
          interactor.notification_error_message
        end
      redirect_to admin_news_path(@news)
    else
      render :new
    end
  end

  # PATCH/PUT /admin/news/1
  def update
    interactor = UpdateNews.call(model: @news, params: news_params, host: request.base_url)
    @news = interactor.model

    if interactor.success?
      flash[:notice] =
        if interactor.notification_error_message.nil?
          t(".success")
        else
          interactor.notification_error_message
        end
      redirect_to admin_news_path(@news)
    else
      render :edit
    end
  end

  # DELETE /admin/news/1
  # DELETE /admin/news/1.json
  def destroy
    interactor = DeleteNews.call(model: @news)
    @news = interactor.model

    respond_to do |format|
      if interactor.success?
        format.html { redirect_to admin_news_index_url, notice: t(".success") }
        format.json { head :no_content }
      else
        format.html { redirect_to admin_news_index_url, error: t(".failure") }
        format.json { render json: @news.errors, status: :unprocessable_entity }
      end
    end
  end

  private

    def set_news
      @news = News.friendly.find(params[:id])
    end

    def authorize_action
      authorize :admin, :actions
    end

    def news_params
      params.require(:news).permit(:title, :body, :attachment, :published_at, :commentable)
    end
end
