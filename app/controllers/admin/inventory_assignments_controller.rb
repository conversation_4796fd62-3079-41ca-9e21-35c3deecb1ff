module Admin
  class InventoryAssignmentsController < BaseController
    before_action :set_user_inventory, only: %i[new create]

    # GET '/user_inventories/:id/assign'
    def new
      @assign_inventory = AssignInventory.new
      authorize :admin, :actions
    end

    def create
      authorize :admin, :actions

      interactor = AssignInventory.call(
        user: @user,
        inventory_id: assign_inventory_params[:inventory_id],
        creator: current_user,
      )
      @assign_inventory = interactor.model

      respond_to do |format|
        if interactor.success?
          format.html { redirect_to admin_user_inventory_path(@user), notice: t(".success_message") }
          format.json { render :show, status: :ok, location: @assign_inventory }
        else
          flash[:error] = interactor.message
          format.html { render :new, status: interactor.status_code }
          format.json { render json: @assign_inventory.errors, status: interactor.status_code }
        end
      end
    end

    private

      def set_user_inventory
        @user = User.friendly.find(params[:user_id])
        @inventories = Inventory.assignable_inventories(@user)
      end

      # Never trust parameters from the scary internet, only allow the white list through.
      def assign_inventory_params
        params.require(:inventory_assignment).permit(:inventory_id)
      end
  end
end
