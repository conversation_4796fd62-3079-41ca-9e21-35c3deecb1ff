class Admin::SettlementAdjustmentsController < Admin::BaseController
  before_action :authorize_action, except: %i[index]
  before_action :set_date_range, only: %i[index]
  before_action :set_settlement_adjustment_log, only: %i[retry]

  def index
    @q = policy_scope(SettlementLog.settlement_adjustment.between_date(@start_date, @end_date), policy_scope_class: AdminPolicy::Scope).ransack(params[:q])
    @q.sorts = "created_at desc" if @q.sorts.empty?
    @settlement_adjustments = @q.result.page(params[:page])
    @settlement_adjustments = @settlement_adjustments.pluck(:log_id).uniq.map { |log_id| SettlementLog.latest_log_state(log_id) }
  end

  def upload
    # render upload view
  end

  def download_template_file
    file = File.join(Rails.root, "/spec/fixtures/settlement_adjustment_file.csv")
    send_file file, filename: "settlement-adjustment-example.csv"
  end

  def create
    interactor = ImportSettlementAdjustmentCsv.call(file: settlement_adjustment_params[:file], creator: current_user)
    if interactor.success?
      redirect_to settlement_adjustments_admin_settlement_logs_path, notice: t(".uploaded")
    else
      flash[:error] = interactor.message
      render :upload
    end
  end

  def log
    @q = policy_scope(SettlementAdjustmentLog, policy_scope_class: AdminPolicy::Scope).ransack(params[:q])
    @q.sorts = "updated_at desc" if @q.sorts.empty?
    @settlement_adjustment_logs = @q.result.page(params[:page])
  end

  def retry
    interactor = RetryProcessSettlementAdjustment.call(model: @settlement_adjustment_log, updater: current_user)
    if interactor.success?
      flash[:notice] = t(".process_retried")
    else
      flash[:error] = interactor.message
    end
    redirect_to settlement_adjustment_log_admin_settlement_logs_path
  end

  private

    def authorize_action
      authorize :admin, :actions
    end

    def set_date_range
      @start_date = params[:start_date].present? ? params[:start_date] : Date.today - 1.day
      @end_date = params[:end_date].present? ? params[:end_date] : Date.today
    end

    def settlement_adjustment_params
      params.permit(:file)
    end

    def set_settlement_adjustment_log
      @settlement_adjustment_log = SettlementAdjustmentLog.find(params[:id])
    end
end
