class Admin::TrainingSubmodulesController < Admin::BaseController
  before_action :authorize_action
  before_action :set_module, except: %i[destroy]
  before_action :set_submodule, only: %i[edit update destroy]

  helper_method :selected_training_module

  # GET /trainings/modules/:module_id/submodules
  def index
    base_query = TrainingSubmodule.joins(training_module: :training_type)
      .select("training_submodules.*, training_modules.name as module_name")
      .where(training_module: @module)

    @q = policy_scope(base_query, policy_scope_class: AdminPolicy::Scope).ransack(params[:q])
    @q.sorts = "seq" if @q.sorts.empty?
    @submodules = @q.result.page(params[:page])

    @quiz = @module.quiz

    # for duplicate popups
    @types = TrainingType.order(:seq).pluck(:name, :id)
    @modules_without_quiz = fetch_modules_without_quiz
  end

  # GET /trainings/modules/:module_id/submodules/new
  def new
    @submodule = TrainingSubmodule.new
  end

  # GET /trainings/modules/:module_id/submodules/:submodule_id/edit
  def edit
    # @submodule defined by set_submodule in before_action
  end

  # POST /trainings/modules/:module_id/submodules
  def create
    interactor = CreateTrainingSubmodule.call(params: submodule_params, creator: current_user)
    if interactor.success?
      redirect_to admin_training_submodules_index_path(module_id: @module.id),
        notice: t(".created")
    else
      @submodule = interactor.model
      flash[:error] = interactor.message
      render :new
    end
  end

  # PATCH /trainings/modules/:module_id/submodules/:submodules_id
  def update
    interactor = UpdateTrainingSubmodule.call(model: @submodule, params: submodule_params, updater: current_user)
    if interactor.success?
      redirect_to admin_training_submodules_index_path(module_id: @module.id),
        notice: t(".updated")
    else
      @submodule = interactor.model
      flash[:error] = interactor.message
      render :edit
    end
  end

  # DELETE /trainings/modules/:module_id/submodules/:submodule_id
  def destroy
    interactor = DeleteTrainingContent.call(model: @submodule)
    if interactor.success?
      flash[:notice] = t(".submodule_deleted")
      redirect_to admin_training_submodules_index_path(module_id: params[:module_id])
    else
      flash[:error] = interactor.message
      redirect_to admin_training_submodules_index_path(module_id: params[:module_id])
    end
  end

  private

    def authorize_action
      authorize :admin, :actions
    end

    def set_module
      @module = TrainingModule.find(params[:module_id])
    rescue ActiveRecord::RecordNotFound
      flash[:error] = I18n.t("admin.training_submodules.e.invalid_training_module")
      redirect_to admin_trainings_path
    end

    def set_submodule
      @submodule = TrainingSubmodule.find(params[:submodule_id])
      unless @module.nil? || @submodule.training_module == @module
        flash[:error] = I18n.t("admin.training_submodules.e.module_submodule_mismatch", submodule_id: params[:submodule_id], module_id: params[:module_id])
        redirect_to admin_training_submodules_index_path(module_id: params[:module_id])
      end
    rescue ActiveRecord::RecordNotFound
      flash[:error] = I18n.t("admin.training_submodules.e.submodule_not_found")
      redirect_to admin_training_submodules_index_path(module_id: params[:module_id])
    end

    def submodule_params
      params.require(:training_submodule)
        .permit(:name, :training_module_id, :seq, :submodule_type, :content, search_keywords: [])
    end

    def fetch_modules_without_quiz
      TrainingModule.left_joins(:quiz).where(quizzes: { id: nil }).order(:seq).pluck(:training_type_id, :id, :name)
    end
end
