class Admin::SettlementLogsController < Admin::BaseController
  include InputHelper
  include ValidationHelper

  before_action :set_settlement_log, only: [:show, :approve, :reject, :cancel_uncollected]
  before_action :apply_index_params, only: :index

  def index
    @q = policy_scope(
      SettlementLog.between_date(@start_date, @end_date).where(action: SettlementLog.actions.keys[@status.to_i]),
      policy_scope_class: AdminPolicy::Scope
    )
    @settlement_logs_datatable = Admin::SettlementLogsDatatable.new(view_context, @q, { status: @status, start_date: @start_date })
    respond_to do |format|
      format.html
      format.json { render json: @settlement_logs_datatable }
    end
  end

  def show
    authorize :admin, :actions
  end

  # POST '/admin/settlement_logs/:id/reject'
  def reject
    authorize :admin, :actions

    interactor = RejectSettlementRequest.call(
      model: @settlement_log,
      rejecter: current_user,
      reject_reason: params[:reject_reason]
    )

    respond_to do |format|
      default_redirect = admin_settlement_log_path(@settlement_log)
      if interactor.success?
        format.html { redirect_back(fallback_location: default_redirect, notice: t(".request_being_processed", log_id: @settlement_log.log_id)) }
        format.json { render :show, status: :ok, location: @settlement_log }
      else
        # Set error message to flash
        flash[:error] = interactor.message
        format.html { render :show, status: interactor.status_code }
        format.json { render json: @settlement_log.errors, status: interactor.status_code }
      end
    end
  end

  def approve
    authorize :admin, :actions

    interactor = ApproveSettlementRequest.call(model: @settlement_log, approver: current_user) # Interactor

    respond_to do |format|
      default_redirect = admin_settlement_log_path(@settlement_log)
      if interactor.success?
        format.html { redirect_back(fallback_location: default_redirect, notice: t(".request_being_processed", log_id: @settlement_log.log_id)) }
        format.json { render :show, status: :ok, location: @settlement_log }
      else
        # Set error message to flash
        flash[:error] = interactor.message
        format.html { render :show, status: interactor.status_code }
        format.json { render json: @settlement_log.errors, status: interactor.status_code }
      end
    end
  end

  def new_uncollected
    authorize :admin, :actions
    @settlement_log = SettlementLog.new
  end

  def create_uncollected
    authorize :admin, :actions

    interactor = CreateUncollected.call(params: uncollected_params, creator: current_user)
    @settlement_log = interactor.model

    if interactor.success?
      redirect_to admin_settlement_logs_path, notice: t(".uncollected_created")
    else
      flash[:error] = interactor.message
      render :new_uncollected, status: interactor.status_code
    end
  end

  def cancel_uncollected
    authorize :admin, :actions

    interactor = CancelUncollected.call(model: @settlement_log)

    if interactor.success?
      redirect_to admin_settlement_logs_path, notice: t(".uncollected_cancelled")
    else
      flash[:error] = interactor.message
      render :show, status: interactor.status_code
    end
  end

  private
    def set_settlement_log
      requested_settlement = SettlementLog.find(params[:id])
      @settlement_log = SettlementLog.latest_log_state(requested_settlement.log_id)
    end

    def apply_index_params
      # default to 1 day ago if param not present
      @start_date = params[:start_date] || params.dig(:dataTableOptions, 0) || 1.days.ago.to_date
      @end_date = params[:end_date] || params.dig(:dataTableOptions, 1) || Date.today

      unless valid_date?(@start_date) && valid_date?(@end_date)
        flash[:error] = I18n.t("admin.settlement_logs.e.invalid_date_format")
        render :index, status: :unprocessable_entity
      end

      @status =
        if params.dig(:dataTableOptions, 2).present?
          params.dig(:dataTableOptions, 2)
        elsif %w[1 2 3 4 6 7 8].include? params[:status]
          params[:status]
        else
          "1" # defaults to REQUESTED
        end
    end

    def uncollected_params
      params[:amount] = strip_currency_mask(params[:amount])
      params.permit(:amount, :fleet_email, :description, :order_id)
    end
end
