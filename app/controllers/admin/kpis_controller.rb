class Admin::KpisController < Admin::BaseController
  before_action :set_kpi, only: %i[show edit update destroy]
  before_action :authorize_action, except: %i[index]

  # GET /kpis
  def index
    @q = policy_scope(base_collection, policy_scope_class: AdminPolicy::Scope).ransack(params[:q])
    @q.sorts = "updated_at desc" if @q.sorts.empty?
    @kpis = @q.result.page(params[:page])
  end

  # GET /kpis/1
  def show
    # defined on before_action
  end

  # GET /kpis/new
  def new
    @kpi = Kpi.new
  end

  # GET /kpis/1/edit
  def edit
    # set kpi_target attribute if kpi have kpi_target
    if @kpi.kpi_target?
      @kpi.comparison_operator = @kpi.kpi_target.comparison_operator
      @kpi.target_amount = @kpi.kpi_target.target_amount
      @kpi.redash_unit = @kpi.redash? ? @kpi.kpi_target.unit : nil
    end
  end

  # POST /kpis
  # POST /kpis.json
  def create
    interactor = CreateKpi.call(params: kpi_params, creator: current_user)
    @kpi = interactor.model

    respond_to do |format|
      if interactor.success?
        format.html { redirect_to admin_kpis_path, notice: "KPI was successfully created." }
        format.json { render :show, status: :created, location: @kpi }
      else
        flash[:error] = interactor.message
        format.html { render :new }
        format.json { render json: @kpi.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /kpis/1
  # PATCH/PUT /kpis/1.json
  def update
    interactor = UpdateKpi.call(model: @kpi, params: kpi_params, updater: current_user)
    @kpi = interactor.model

    respond_to do |format|
      if interactor.success?
        format.html { redirect_to admin_kpis_path, notice: "KPI was successfully updated." }
        format.json { render :show, status: :ok, location: @kpi }
      else
        flash[:error] = interactor.message
        format.html { render :edit }
        format.json { render json: @kpi.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /kpis/1
  # DELETE /kpis/1.json
  def destroy
    interactor = DeleteKpi.call(model: @kpi, updater: current_user)
    respond_to do |format|
      if interactor.success?
        format.html { redirect_to admin_kpis_path, notice: "KPI was successfully destroyed." }
        format.json { head :no_content }
      else
        flash[:error] = interactor.message
        format.html { redirect_to admin_kpis_path, notice: "Failed to delete KPI target." }
        format.json { head :no_content }
      end
    end
  end

  private

    # Use callbacks to share common setup or constraints between actions.
    def set_kpi
      @kpi = Kpi.find(params[:id])
    end

    def authorize_action
      authorize :admin, :actions
    end

    # Never trust parameters from the scary internet, only allow the white list through.
    def kpi_params
      params.require(:kpi).permit(:kpi_type, :name, :comparison_operator, :target_amount, :data_source, :redash_schedule, :redash_column, :redash_unit)
    end

    def base_collection
      Kpi.includes(:performance_schemes, :kpi_target, :updater)
    end
end
