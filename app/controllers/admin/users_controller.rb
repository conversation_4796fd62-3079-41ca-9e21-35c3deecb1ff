class Admin::UsersController < Admin::BaseController
  helper <PERSON><PERSON><PERSON><PERSON>

  before_action :set_user, only: %i[show edit update destroy user_my_profile]
  before_action :authorize_action, except: %i[index]
  before_action :apply_new_phone_number, only: :update

  # GET /admin/users
  def index
    @password = session[:password]
    session[:password] = nil

    @q = policy_scope(scoped_collection, policy_scope_class: AdminPolicy::Scope).ransack(params[:q])
    @q.sorts = "email asc" if @q.sorts.empty?
    @users = @q.result
  end

  # GET /admin/users/1
  def show
    # defined on before_action
  end

  # GET /admin/users/new
  def new
    @user = User.new
  end

  # POST /admin/users
  def create
    interactor = CreateSndUser.call(
      params: snd_user_params,
      fms_role_id: params[:fms_role_id],
      profile_picture: params[:profile_picture]
    )
    @user = interactor.model

    if interactor.success?
      session[:password] = interactor.snd_user["password"]
      redirect_to admin_users_path
    else
      flash[:error] = interactor.message
      render :new
    end
  end

  def user_my_profile
    @my_profile = @user.my_profile
  end

  # GET /admin/users/1/edit
  def edit
    redirect_to admin_users_path, alert: t(".cannot_edit_system_user") if @user.system?
  end

  # PATCH/PUT /admin/users/1
  def update
    # prevent 'system' user to be edited
    if @user.system?
      flash[:alert] = t(".cannot_edit_system_user")
      return render :edit
    end

    # Allow updating the user without changing its password (password field
    # will be blank). Remove the password key of the params hash if it's blank
    # (avoid validation error).
    if params[:user][:password].blank?
      params[:user].delete(:password)
      params[:user].delete(:password_confirmation)
    end

    interactor = UpdateUser.call(model: @user, params: user_params)
    @user = interactor.model
    if interactor.success?
      # If the user is editing himself, Devise will automatically logout.
      # To avoid asking the user to login, we'll login automatically here.
      bypass_sign_in(@user) if current_user == @user
      redirect_to [:admin, @user], notice: t(".success")
    else
      flash[:error] = interactor.message
      render :edit
    end
  end

  def export_users
    interactor = ExportUsers.call()
    if interactor.success?
      current_country = Apartment::Tenant.current
      current_date = Date.today.strftime("%d%b%y")
      send_data interactor.csv_file, filename: "fms_users_#{current_country}_export_#{current_date}.csv"
    else
      flash[:error] = interactor.message
      render :index
    end
  end

  private

    def set_user
      @user = User.friendly.find(params[:id])
    end

    def authorize_action
      authorize :admin, :actions
    end

    def user_params
      params.require(:user).permit(:email, :password, :password_confirmation,
                                   :first_name, :last_name, :country_id, :fms_role_id, :phone, :new_phone, roles: [])
    end

    def snd_user_params
      params.require(:snd_user).permit(:email, :first_name, :last_name, :is_active, :phone, :new_phone, roles: [])
    end

    def scoped_collection
      User.includes(:fms_role, :country).where.not(role: :system)
    end

    def apply_new_phone_number
      param_key = params[:snd_user].present? ? :snd_user : :user

      if params[param_key][:new_phone].to_s.strip.present?
        params[param_key][:phone] = params[param_key][:new_phone].to_s.strip
      end
    end
end
