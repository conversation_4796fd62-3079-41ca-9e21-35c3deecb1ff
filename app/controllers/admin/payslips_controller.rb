class Admin::PayslipsController < Admin::BaseController
  before_action :authorize_action, except: %i[index]
  before_action :set_payslip, only: %i[show export_payslip]
  before_action :set_payslip_batch, only: %i[payslip_batch publish_payslip_batch delete_payslip_batch]
  before_action :set_payroll_scheme, only: %i[create_payslips get_payroll_scheme_period]
  before_action :set_period_stepback, only: :create_payslips

  # GET /payslips
  def index
    @q = policy_scope(PayslipBatch.kept, policy_scope_class: AdminPolicy::Scope).ransack(params[:q])
    @q.sorts = "updated_at desc" if @q.sorts.empty?
    @payslip_batches = @q.result.page(params[:page])
    @last_payslip_job = PayslipJob.order(updated_at: :desc).first
  end

  # GET /payslip/batch/:batch_id
  def payslip_batch
    @payslips = Payslip.where(payslip_batch_id: @payslip_batch.id).includes(user: :fms_role)
    @payslip_totals = UserAdjustment.where(payslip_id: @payslips.pluck(:id)).group(:user_id).sum(:item_value)
  end

  # DELETE /payslip/batch/:batch_id
  def delete_payslip_batch
    interactor = DeletePayslipBatch.call(model: @payslip_batch, updater: current_user)
    if interactor.success?
      redirect_to admin_payslips_path, notice: I18n.t("admin.payslips.delete_payslip_batch.success")
    else
      flash[:error] = I18n.t("admin.payslips.delete_payslip_batch.failed", error_message: interactor.message)
      render :payslip_batch
    end
  end

  # GET /payslip/:id
  def show
    # defined in before_action
  end

  # GET /payslips/:id/export
  def export_payslip
    interactor = ExportPayslip.call(
                  user_adjustments: @user_adjustments,
                  payslip: @payslip
                )

    if interactor.success?
      send_data interactor.pdf, filename: interactor.filename
    else
      respond_to do |format|
        flash[:error] = interactor.message
        format.html { render :show }
      end
    end
  end

  def create_payslips
    current_user_id = current_user.id
    current_tenant = Apartment::Tenant.current
    begin
      CalculatePayrollSchemeWorker.perform_async(@payroll_scheme.id, current_user_id, current_tenant, @period_stepback)
    rescue => e
      respond_to do |format|
        flash[:error] = t("admin.payslips.controller.failed_payslip", error_message: e.message)
        format.html { redirect_to admin_payslips_path }
      end
    else
      respond_to do |format|
        format.html { redirect_to admin_payslips_path, notice: t("admin.payslips.controller.processing_payslip") }
      end
    end
  end

  def jobs
    @q = policy_scope(PayslipJob.includes(:payroll_scheme, :creator), policy_scope_class: AdminPolicy::Scope).ransack(params[:q])
    @q.sorts = "updated_at desc" if @q.sorts.empty?
    @payslip_jobs = @q.result.page(params[:page])
  end

  def get_payroll_scheme_period
    @payroll_scheme_periods ||= [] # array to populate periods of payroll scheme

    if @payroll_scheme.is_recurring?
      max_period = 9 # to determine how many periods will be shown
      for i in 0..max_period
        if @payroll_scheme.period_start_end_dates(i).present?
          @payroll_scheme_periods << set_date_format(@payroll_scheme.period_start_end_dates(i))
        else
          break
        end
      end
    else
      @payroll_scheme_periods << set_date_format(@payroll_scheme.period_start_end_dates)
    end

    # if request.xhr?
    respond_to do |format|
      format.html { render :generate }
      format.json {
        render json: { periods: @payroll_scheme_periods }
      }
    end
    # end
  end

  # POST payslips/batch/:batch_id/publish
  def publish_payslip_batch
    interactor = PublishPayslipBatch.call(
      payslip_batch: @payslip_batch,
      remark: params[:remark],
      updater: current_user
    )

    if interactor.success?
      flash[:notice] = t(".success")
    else
      flash[:error] = interactor.message
    end

    redirect_to admin_payslip_batch_path(batch_id: @payslip_batch.id)
  end

  private

    def authorize_action
      authorize :admin, :actions
    end

    # Use callbacks to share common setup or constraints between actions.
    def set_payslip
      @payslip = Payslip.find(params[:id])
      @user_adjustments = UserAdjustment.where(payslip_id: @payslip.id)
                                   .includes(adjustment_condition: [:adjustment_item])
                                   .order("adjustment_conditions.number ASC")
    rescue ActiveRecord::RecordNotFound
      flash[:error] = I18n.t("admin.payslips.e.payslip_not_found")
      redirect_to admin_payslips_path
    end

    def set_payslip_batch
      @payslip_batch = PayslipBatch.kept.find(params[:batch_id])
    rescue ActiveRecord::RecordNotFound
      flash[:error] = I18n.t("admin.payslips.e.payslip_batch_id_invalid")
      redirect_to admin_payslips_path
    end

    def set_payroll_scheme
      @payroll_scheme = PayrollScheme.find(params[:payroll_scheme_id])
    rescue ActiveRecord::RecordNotFound
      flash[:error] = I18n.t("admin.payslips.e.invalid_payroll_scheme_id")
      redirect_to admin_payslips_path
    end

    def set_date_format(date)
      [ date[0].strftime("%d %b %Y"), date[1].strftime("%d %b %Y") ]
    end

    def authorize_action
      authorize :admin, :actions
    end

    def set_period_stepback
      @period_stepback = Integer(params[:period_stepback], 10)
      if @period_stepback.negative?
        flash[:error] = I18n.t("admin.payslips.e.future_period_chosen")
        redirect_to admin_payslips_path
      end
    rescue ArgumentError
      flash[:error] = I18n.t("admin.payslips.e.invalid_period_stepback")
      redirect_to admin_payslips_path
    end
end
