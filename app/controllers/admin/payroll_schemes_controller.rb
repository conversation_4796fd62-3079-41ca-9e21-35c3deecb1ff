class Admin::PayrollSchemesController < Admin::BaseController
  before_action :set_payroll_scheme, only: %i[show edit update destroy]
  before_action :authorize_action, except: %i[index]

  # GET /payroll_schemes
  def index
    @q = policy_scope(PayrollScheme.includes(:fms_role), policy_scope_class: AdminPolicy::Scope).ransack(params[:q])
    @q.sorts = "updated_at desc" if @q.sorts.empty?
    @payroll_schemes = @q.result.page(params[:page])
  end

  # GET /payroll_scheme/:id
  def show
    # defined in before_action
  end

  # GET /payroll_scheme/new
  def new
    @roles = FmsRole.all
    @payroll_scheme = PayrollScheme.new
  end

  # GET /payroll_schemes/1/edit
  def edit
    @roles = FmsRole.all
  end

  # POST /payroll_scheme
  # POST /payroll_schemes.json
  def create
    interactor = CreatePayrollScheme.call(params: payroll_schemes_params, user: current_user)
    @payroll_scheme = interactor.model

    respond_to do |format|
      if interactor.success?
        format.html { redirect_to admin_payroll_schemes_path, notice: t(".success") }
        format.json { render :show, status: :created, location: @payroll_scheme }
      else
        @payroll_scheme = PayrollScheme.new
        flash[:error] = interactor.message
        format.html { render :new }
        format.json { render json: @payroll_scheme.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /payroll_scheme/1
  # PATCH/PUT /payroll_scheme/1
  def update
    interactor = UpdatePayrollScheme.call(model: @payroll_scheme, params: payroll_schemes_params, user: current_user)
    @payroll_scheme = interactor.model

    respond_to do |format|
      if interactor.success?
        format.html { redirect_to admin_payroll_schemes_path, notice: t(".success") }
        format.json { render :show, status: :ok, location: @payroll_scheme }
      else
        flash[:error] = interactor.message
        format.html { render :edit }
        format.json { render json: @payroll_scheme.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /payroll_scheme/1
  # DELETE /payroll_scheme/1.json
  def destroy
    interactor = DeletePayrollScheme.call(model: @payroll_scheme, updater: current_user)
    respond_to do |format|
      if interactor.success?
        format.html { redirect_to admin_payroll_schemes_path, notice: t(".success") }
        format.json { head :no_content }
      else
        flash[:error] = interactor.message
        format.html { redirect_to admin_payroll_schemes_path, notice: t(".failed") }
        format.json { head :no_content }
      end
    end
  end

  private

    # Use callbacks to share common setup or constraints between actions.
    def set_payroll_scheme
      @payroll_scheme = PayrollScheme.find(params[:id])
    end

    def authorize_action
      authorize :admin, :actions
    end

    # Never trust parameters from the scary internet, only allow the white list through.
    def payroll_schemes_params
      params.require(:payroll_scheme).permit(
        :name,
        :is_recurring,
        :period_start,
        :period_end,
        :period_type,
        :fms_role_id,
        :users_mode,
        user_ids: [],
        payroll_adjustment: [],
        payroll_after_gross: []
      )
    end
end
