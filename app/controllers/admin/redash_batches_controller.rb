class Admin::RedashBatchesController < Admin::BaseController
  before_action :authorize_action

  def index
    @q = policy_scope(RedashBatch.includes(:schedule).kept, policy_scope_class: AdminPolicy::Scope).ransack(params[:q])
    @q.sorts = "created_at desc" if @q.sorts.empty?
    @redash_batches = @q.result.page(params[:page])
    @last_redash_job = RedashJob.order(updated_at: :desc).first
  end

  def jobs
    @q = policy_scope(RedashJob.includes(:redash_schedule, :creator), policy_scope_class: AdminPolicy::Scope).ransack(params[:q])
    @q.sorts = "updated_at desc" if @q.sorts.empty?
    @redash_jobs = @q.result.page(params[:page])
  end

  private

    def authorize_action
      authorize :admin, :actions
    end
end
