module Admin
  class FmsRolesController < BaseController
    before_action :set_role, only: %i[show edit update destroy]
    before_action :authorize_action, except: %i[index]

    # GET /admin/fms_roles
    def index
      @q = policy_scope(FmsRole, policy_scope_class: AdminPolicy::Scope).ransack(params[:q])
      @q.sorts = "name desc" if @q.sorts.empty?
      @roles = @q.result.page(params[:page])
    end

    # GET /admin/fms_roles/1
    def show
      # defined on before_action
    end

    # GET /admin/fms_roles/new
    def new
      @role = FmsRole.new
    end

    # GET /admin/fms_roles/1/edit
    def edit
      # defined on before_action
    end

    # POST /admin/fms_roles
    # POST /admin/fms_roles.json
    def create
      interactor = CreateFmsRole.call(role_params) # Interactor
      @role = interactor.model

      respond_to do |format|
        if interactor.success?
          format.html { redirect_to admin_fms_role_path(@role), notice: "Role was successfully created." }
          format.json { render :show, status: :created, location: @role }
        else
          flash[:error] = interactor.message
          format.html { render :new }
          format.json { render json: @role.errors, status: :unprocessable_entity }
        end
      end
    end

    # PATCH/PUT /admin/fms_roles/1
    # PATCH/PUT /admin/fms_roles/1.json
    def update
      interactor = UpdateFmsRole.call(model: @role, params: role_params) # Interactor
      @role = interactor.model

      respond_to do |format|
        if interactor.success?
          format.html { redirect_to admin_fms_role_path(@role), notice: "Role was successfully updated." }
          format.json { render :show, status: :ok, location: @role }
        else
          flash[:error] = interactor.message
          format.html { render :edit }
          format.json { render json: @role.errors, status: :unprocessable_entity }
        end
      end
    end

    # DELETE /admin/fms_roles/1
    # DELETE /admin/fms_roles/1.json
    def destroy
      interactor = DeleteFmsRole.call(model: @role) # Interactor
      respond_to do |format|
        if interactor.success?
          format.html { redirect_to admin_fms_roles_url, notice: "FMS Role was successfully destroyed." }
          format.json { head :no_content }
        else
          format.html { redirect_to admin_fms_roles_url, alert: "Failed to delete FMS Role." }
          format.json { head :no_content }
        end
      end
    end

    private

      def set_role
        @role = FmsRole.find(params[:id])
      end

      def authorize_action
        authorize :admin, :actions
      end

      def role_params
        params.require(:fms_role).permit(:name, :role_type)
      end
  end
end
