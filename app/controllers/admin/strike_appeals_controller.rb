class Admin::StrikeAppealsController < Admin::BaseController
  before_action :set_strike_appeal, only: [:show, :approve, :reject]
  before_action :authorize_action, except: [:index]

  # GET '/admin/strike_appeals'
  def index
    @q = policy_scope(StrikeAppeal.includes(:requester_strike, :requester, :discarder), policy_scope_class: AdminPolicy::Scope).ransack(params[:q])
    @q.sorts = "updated_at desc" if @q.sorts.empty? # Ransack default (initial) sort order
    @strike_appeals = @q.result.page(params[:page]).per(20) # Ransack search/filter results, paginated by Ka<PERSON><PERSON>.
  end

  # GET '/admin/strike_appeals/:id'
  def show
    # defined on before_action
  end

  # POST '/admin/strike_appeals/:id/approve'
  def approve
    interactor = ApproveStrikeAppeal.call(model: @strike_appeal, approver: current_user) # Interactor
    @strike_appeal = interactor.model

    respond_to do |format|
      if interactor.success?
        format.html { redirect_to admin_strike_appeal_path(@strike_appeal), notice: t(".appeal_approved") }
        format.json { render :show, status: :ok, location: @strike_appeal }
      else
        # Set error message to flash
        flash[:error] = t(interactor.message)

        format.html { render :show, status: interactor.status_code }
        format.json { render json: @strike_appeal.errors, status: interactor.status_code }
      end
    end
  end

  # POST '/admin/strike_appeals/:id/reject'
  def reject
    interactor = RejectStrikeAppeal.call(
      model: @strike_appeal,
      rejecter: current_user,
      reject_reason: params[:reject_reason]
    )
    @strike_appeal = interactor.model

    respond_to do |format|
      if interactor.success?
        format.html { redirect_to admin_strike_appeal_path(@strike_appeal), notice: t(".appeal_rejected") }
        format.json { render :show, status: :ok, location: @strike_appeal }
      else
        # Set error message to flash
        flash[:error] = t(interactor.message)

        format.html { render :show, status: interactor.status_code }
        format.json { render json: @strike_appeal.errors, status: interactor.status_code }
      end
    end
  end

  private

    def set_strike_appeal
      @strike_appeal = StrikeAppeal.find(params[:id])
    end

    def authorize_action
      authorize :admin, :actions
    end
end
