class Admin::OpsFactsController < Admin::BaseController
  before_action :authorize_action, except: %i[index]
  before_action :set_date_range, only: %i[index]

  # GET /ops_facts
  def index
    rows = policy_scope(UserAdjustmentFact, policy_scope_class: AdminPolicy::Scope)
    interactor = FetchOpsFacts.call(rows: rows, start_date: @start_date, end_date: @end_date)

    if interactor.success?
      @ops_facts = interactor.records
    else
      flash[:error] = interactor.message
      @ops_facts = []
    end
  end

  # GET /ops_fact/upload
  def upload
    # for upload form page
  end

  # POST /ops_facts
  def create
    interactor = ImportOpsFacts.call(params: ops_facts_params, updater: current_user)
    respond_to do |format|
      if interactor.success?
        format.html { redirect_to admin_ops_facts_path, notice: I18n.t("admin.ops_facts.controller.uploaded") }
        format.json { render :show, status: :created, location: @ops_fact }
      else
        flash[:error] = interactor.message
        format.html { render :upload }
        format.json { render json: interactor.message, status: :unprocessable_entity }
      end
    end
  end

  private

    def authorize_action
      authorize :admin, :actions
    end

    def set_date_range
      @start_date = params[:start_date].present? ? params[:start_date] : Date.today - 1.month + 1.day
      @end_date = params[:end_date].present? ? params[:end_date] : Date.today
    end

    def ops_facts_params
      params.permit(:start_date, :end_date, :file)
    end
end
