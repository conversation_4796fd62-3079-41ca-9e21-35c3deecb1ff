class TinymceAssetsController < BaseController
  respond_to :json

  skip_before_action :verify_authenticity_token, only: :image_upload

  def image_upload
    authorize :admin, :actions

    # There's a problem when using:
    #   uploader = TinymceImageUploader.new
    #   uploader.store!(params[:file])
    # because TinymceImageUploader class (with Cloudinary::CarrierWave included)
    # always trying to find mounter Model
    # tinymce assets doesn't belongs to any model

    # so we use this:
    # https://cloudinary.com/documentation/upload_images
    uploader = Cloudinary::Uploader.upload(params[:file], folder: "tinymce_assets/")

    render json: {
      location: uploader["secure_url"],
      image: {
        url: uploader["secure_url"],
        height: uploader["height"].to_i,
        width: uploader["width"].to_i
      },
    }, layout: false, content_type: "text/html"
  end
end
