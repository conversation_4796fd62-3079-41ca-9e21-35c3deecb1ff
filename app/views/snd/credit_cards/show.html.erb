<div class="content-heading">
  <div>
    <%= t('.page_title') %>
  </div>
</div>

<div class="container-fluid">
  <div class="row">
    <div class="col-md-8">
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">
            <i class="fas fa-credit-card mr-2"></i>
            <%= t('.card_details') %>
          </h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label class="form-label"><%= t('.name') %></label>
                <p class="form-control-static"><%= @credit_card["name"] %></p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label class="form-label"><%= t('.last_digits') %></label>
                <p class="form-control-static">**** <%= @credit_card["last_digits"] %></p>
              </div>
            </div>
          </div>
          
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label class="form-label"><%= t('.expiry_month') %></label>
                <p class="form-control-static"><%= @credit_card["month"] %></p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label class="form-label"><%= t('.expiry_year') %></label>
                <p class="form-control-static"><%= @credit_card["year"] %></p>
              </div>
            </div>
          </div>
          
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label class="form-label"><%= t('.shopper_email') %></label>
                <p class="form-control-static"><%= @credit_card["shopper_email"] %></p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label class="form-label"><%= t('.shopper_phone') %></label>
                <p class="form-control-static"><%= @credit_card["shopper_phone"] %></p>
              </div>
            </div>
          </div>
          
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label class="form-label"><%= t('.state') %></label>
                <p class="form-control-static">
                  <% case @credit_card["state"] %>
                  <% when "ENABLED" %>
                    <span class="badge badge-success"><%= t('.state_enabled') %></span>
                  <% when "DISABLED" %>
                    <span class="badge badge-danger"><%= t('.state_disabled') %></span>
                  <% when "AUTOMATIC" %>
                    <span class="badge badge-info"><%= t('.state_automatic') %></span>
                  <% else %>
                    <span class="badge badge-secondary"><%= @credit_card["state"] %></span>
                  <% end %>
                </p>
              </div>
            </div>
          </div>
        </div>
        <div class="card-footer">
          <%= link_to t('actions.back'), snd_credit_cards_path, class: "btn btn-secondary" %>
        </div>
      </div>
    </div>
  </div>
</div>
