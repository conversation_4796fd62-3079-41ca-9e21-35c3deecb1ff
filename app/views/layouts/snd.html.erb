<% content_for :navigation_menu do %>
  <ul class="sidebar-nav">
    <li class="nav-heading">
      <span>Main Navigation</span>
    </li>
    <%# Menu items that that should only appear in the navbar of users with
    "user" role (non-admin users) %>
    <li class="<%= 'active' if controller?('news') %>">
      <a href="<%= root_path %>" title="Home">
        <em class="fas fa-home"></em>
        <span><%= t('navigation.home') %></span>
      </a>
    </li>
    <li class="<%= 'active' if controller?('snd/performances') %>">
      <a href="<%= snd_performances_path %>">
        <em class="fas fa-chart-line"></em>
        <span><%= t('navigation.performances') %></span>
      </a>
    </li>
    <li class="<%= 'active' if controller?('snd/faqs') %>">
      <a href="<%= snd_faqs_path %>" title="FAQ">
        <em class="icon-question"></em>
        <span><%= t('navigation.faq_category') %></span>
      </a>
    </li>
    <li class="<%= 'active' if controller?('snd/merit_strikes') %>">
      <a href="<%= snd_merit_strikes_path %>" title="Bonus / Penalty">
        <em class="fas fa-coins"></em>
        <span><%= t('navigation.merit_strike') %></span>
      </a>
    </li>
    <li class="<%= 'active' if controller?('snd/settlement_logs') %>">
      <a href="<%= snd_settlement_logs_path %>" title="Settlements">
        <em class="fas fa-money-bill"></em>
        <span><%= t('navigation.settlements') %></span>
      </a>
    </li>
    <li class="<%= 'active' if controller?('snd/inventories#show_items') %>">
      <a href="<%= snd_inventory_items_path %>" title="Inventory Items">
        <em class="fas fa-box-open"></em>
        <span><%= t('navigation.inventory_items') %></span>
      </a>
    </li>
    <li class="<%= 'active' if controller?('snd/user_payslips') %>">
      <a href="<%= snd_payslips_path %>" title="User payslip">
        <em class="fas fa-money-bill-alt"></em>
        <span><%= t('navigation.my_payslip') %></span>
      </a>
    </li>
    <li class="<%= 'active' if controller?('snd/inventories#show_cards' || 'snd/inventories#show_card_details') %>">
      <a href="<%= snd_inventory_cards_path %>" title="Cards">
        <em class="fas fa-credit-card"></em>
        <span><%= t('navigation.cards') %></span>
      </a>
    </li>
    <li class="<%= 'active' if controller?('snd/credit_cards') %>">
      <a href="<%= snd_credit_cards_path %>" title="Credit Cards">
        <em class="fas fa-credit-card"></em>
        <span><%= t('navigation.credit_cards') %></span>
      </a>
    </li>
    <%
      training_paths = [
        snd_training_index_path,
        snd_training_progress_path,
        snd_training_dashboard_path
      ]
    %>
    <li class="<%= 'active' if training_paths.include?(controller_path) %>">
      <a href="#training" title="Training" data-toggle="collapse">
        <em class="fas fa-book-open"></em>
        <span><%= t('navigation.training') %></span>
      </a>
      <ul id="training" class="sidebar-nav sidebar-subnav collapse">
        <li class="<%= 'active' if controller?('snd/training_dashboard' ) %>">
          <a href="<%= snd_training_dashboard_path %>"><span><%= t('navigation.training_dashboard') %></span></a>
        </li>
        <li class="<%= 'active' if controller?('snd/training_progress' ) %>">
          <a href="<%= snd_training_progress_path %>"><span><%= t('navigation.training_progress') %></span></a>
        </li>
        <li class="<%= 'active' if controller?('snd/trainings' ) %>">
          <a href="<%= snd_training_index_path %>"><span><%= t('navigation.training_modules') %></span></a>
        </li>
      </ul>
    </li>
    <li class="<%= 'active' if controller?('snd/order_histories') %>">
      <a href="<%= snd_order_history_index_path %>">
        <em class="fas fa-history"></em>
        <span><%= t('navigation.order_history') %></span>
      </a>
    </li>
    <li>
      <a href="#languages" data-toggle="collapse" title="<%= t('navigation.languages') %>">
        <em class="fas fa-language"></em>
        <span><%= t('navigation.languages') %></span>
      </a>
      <ul id="languages" class="sidebar-nav sidebar-subnav collapse">
        <% alternate_locales do |l| %>
          <li class="language">
            <%= link_to(t("navigation.locale-#{l}"), snd_set_locale_cookie_path(l), locale: l, title: t("navigation.locale-#{l}")) %>
          </li>
        <% end %>
      </ul>
    </li>
  </ul>
<% end %>
<%= render template: "layouts/application" %>
