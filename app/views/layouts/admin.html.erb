<% content_for :navigation_menu do %>
  <ul class="sidebar-nav">
    <li class="nav-heading">
      <span>Main Navigation</span>
    </li>

    <% access_list = user_access_list() %>

    <li class="<%= 'active' if controller?('news') %>">
      <a href="<%= root_path %>" title="Home">
        <em class="fas fa-home"></em>
        <span><%= t('navigation.home') %></span>
      </a>
    </li>

    <% if nav_accessible(access_list, admin_users_path) %>
      <li class="<%= 'active' if controller?('admin/users') %>">
        <a href="<%= admin_users_path %>" title="Users">
          <em class="fas fa-user"></em>
          <span><%= t('navigation.users') %></span>
        </a>
      </li>
    <% end %>

    <% onboarding_paths = [
      admin_form_data_path,
      admin_particular_forms_path
      ] %>
    <% if parent_nav_accessible(access_list, onboarding_paths) %>
      <li class="<%= 'active' if onboarding_paths.include?(controller_path) %>">
        <a href="#onboarding" title="Onboarding" data-toggle="collapse">
          <em class="fas fa-file-alt"></em>
          <span><%= t('navigation.onboarding') %></span>
        </a>
        <ul id="onboarding" class="sidebar-nav sidebar-subnav collapse">
          <% if nav_accessible(access_list, admin_particular_forms_path) %>
            <li class="<%= 'active' if controller?('admin/particular_forms') %>">
              <a href="<%= admin_particular_forms_path %>"><span><%= t('navigation.particular_forms') %></span></a>
            </li>
          <% end %>
          <% if nav_accessible(access_list, admin_form_data_path) %>
            <li class="<%= 'active' if controller?('admin/form_data') %>">
              <a href="<%= admin_form_data_path %>"><span><%= t('navigation.form_data') %></span></a>
            </li>
          <% end %>
        </ul>
      </li>
    <% end %>

    <% if nav_accessible(access_list, admin_news_index_path) %>
      <li class="<%= 'active' if controller?('admin/news') %>">
        <a href="<%= admin_news_index_path %>" title="News List">
          <em class="fas fa-newspaper"></em>
          <span><%= t('navigation.news') %></span>
        </a>
      </li>
    <% end %>

    <% if nav_accessible(access_list, admin_faq_categories_path) %>
      <li class="<%= 'active' if controller?('admin/faq_categories') %>">
        <a href="<%= admin_faq_categories_path %>" title="FAQ">
          <em class="icon-question"></em>
          <span><%= t('navigation.faq_category') %></span>
        </a>
      </li>
    <% end %>

    <% merit_strike_paths = [
      admin_merits_path,
      admin_strikes_path,
      admin_user_merit_strikes_path,
      admin_strike_appeals_path,
      admin_merit_rewards_path,
      admin_user_merit_redemptions_path
      ] %>
    <% if parent_nav_accessible(access_list, merit_strike_paths) %>
      <li class="<%= 'active' if merit_strike_paths.include?(controller_path) %>">
        <a href="#admin_merits_strikes" title="Bonus and Penalty" data-toggle="collapse">
          <em class="fas fa-coins"></em>
          <span><%= t('navigation.merit_strike') %></span>
        </a>
        <ul id="admin_merits_strikes" class="sidebar-nav sidebar-subnav collapse">
          <% if nav_accessible(access_list, admin_user_merit_strikes_path) %>
            <li class="<%= 'active' if controller?('admin/user_merit_strikes') %>">
              <a href="<%= admin_user_merit_strikes_path %>"><span><%= t('navigation.summary') %></span></a>
            </li>
          <% end %>
          <% if nav_accessible(access_list, admin_merits_path) %>
            <li class="<%= 'active' if controller?('admin/merits') %>">
              <a href="<%= admin_merits_path %>"><span><%= t('navigation.merit') %></span></a>
            </li>
          <% end %>
          <% if nav_accessible(access_list, admin_strikes_path) %>
            <li class="<%= 'active' if controller?('admin/strikes') %>">
              <a href="<%= admin_strikes_path %>"><span><%= t('navigation.strike') %></span></a>
            </li>
          <% end %>
          <% if nav_accessible(access_list, admin_strike_appeals_path) %>
            <li class="<%= 'active' if controller?('admin/strike_appeals') %>">
              <a href="<%= admin_strike_appeals_path %>"><span><%= t('navigation.strike_appeals') %></span></a>
            </li>
          <% end %>
          <% if nav_accessible(access_list, admin_merit_rewards_path) %>
            <li class="<%= 'active' if controller?('admin/merit_rewards') %>">
              <a href="<%= admin_merit_rewards_path %>"><span><%= t('navigation.merit_reward') %></span></a>
            </li>
          <% end %>
          <% if nav_accessible(access_list, admin_user_merit_redemptions_path) %>
            <li class="<%= 'active' if controller?('admin/user_merit_redemptions') %>">
              <a href="<%= admin_user_merit_redemptions_path %>"><span><%= t('navigation.merit_redemptions') %></span></a>
            </li>
          <% end %>
        </ul>
      </li>
    <% end %>

    <% performance_paths = [
      admin_kpis_path,
      admin_performance_schemes_path,
      admin_user_kpis_path
      ] %>
    <% if parent_nav_accessible(access_list, performance_paths) %>
      <li class="<%= 'active' if performance_paths.include?(controller_path) %>">
        <a href="#performances" data-toggle="collapse">
          <em class="fas fa-chart-line"></em>
          <span><%= t('navigation.performance') %></span>
        </a>
        <ul id="performances" class="sidebar-nav sidebar-subnav collapse">
          <% if nav_accessible(access_list, admin_kpis_path) %>
            <li class="<%= 'active' if controller?('admin/kpis') %>">
              <a href="<%= admin_kpis_path %>"><span><%= t('navigation.kpis') %></span></a>
            </li>
          <% end %>
          <% if nav_accessible(access_list, admin_performance_schemes_path) %>
            <li class="<%= 'active' if controller?('admin/performance_schemes') %>">
              <a href="<%= admin_performance_schemes_path %>"><span><%= t('navigation.performance_schemes') %></span></a>
            </li>
          <% end %>
          <% if nav_accessible(access_list, admin_user_kpis_path) %>
            <li class="<%= 'active' if controller?('admin/user_kpis') %>">
              <a href="<%= admin_user_kpis_path %>"><span><%= t('navigation.user_kpis') %></span></a>
            </li>
          <% end %>
        </ul>
      </li>
    <% end %>

    <% payroll_paths = [
      admin_payroll_schemes_path,
      admin_payroll_adjustments_path,
      admin_payroll_after_grosses_path,
      admin_payslips_path,
      admin_payroll_facts_path
      ] %>
    <% if parent_nav_accessible(access_list, payroll_paths) %>
      <li class="<%= 'active' if payroll_paths.include?(controller_path) %>">
        <a href="#payrolls" data-toggle="collapse">
          <em class="fas fa-money-check-alt"></em>
          <span><%= t('navigation.payroll') %></span>
        </a>
        <ul id="payrolls" class="sidebar-nav sidebar-subnav collapse">
          <% if nav_accessible(access_list, admin_payroll_schemes_path) %>
            <li class="<%= 'active' if controller?('admin/payroll_schemes') %>">
              <a href="<%= admin_payroll_schemes_path %>"><span><%= t('navigation.payroll_schemes') %></span></a>
            </li>
          <% end %>
          <% if nav_accessible(access_list, admin_payroll_adjustments_path) %>
            <li class="<%= 'active' if controller?('admin/payroll_adjustments') %>">
              <a href="<%= admin_payroll_adjustments_path %>"><span><%= t('navigation.payroll_adjustments') %></span></a>
            </li>
          <% end %>
          <% if nav_accessible(access_list, admin_payroll_after_grosses_path) %>
            <li class="<%= 'active' if controller?('admin/payroll_after_grosses') %>">
              <a href="<%= admin_payroll_after_grosses_path %>"><span><%= t('navigation.payroll_after_grosses') %></span></a>
            </li>
          <% end %>
          <% if nav_accessible(access_list, admin_payslips_path) %>
            <li class="<%= 'active' if controller?('admin/payslips') %>">
              <a href="<%= admin_payslips_path %>"><span><%= t('navigation.payslips') %></span></a>
            </li>
          <% end %>
          <% if nav_accessible(access_list, admin_payroll_facts_path) %>
            <li class="<%= 'active' if controller?('admin/payroll_facts') %>">
              <a href="<%= admin_payroll_facts_path %>"><span><%= t('navigation.payroll_facts') %></span></a>
            </li>
          <% end %>
        </ul>
      </li>
    <% end %>

    <% redash_paths = [
      admin_redash_schedules_path,
      admin_redash_batches_path
      ] %>
    <% if parent_nav_accessible(access_list, redash_paths) %>
      <li class="<%= 'active' if redash_paths.include?(controller_path) %>">
        <a href="#redash" data-toggle="collapse">
          <em class="fas fa-table"></em>
          <span><%= t('navigation.redash') %></span>
        </a>
        <ul id="redash" class="sidebar-nav sidebar-subnav collapse">
          <% if nav_accessible(access_list, admin_redash_schedules_path) %>
            <li class="<%= 'active' if controller?('admin/redash_schedules') %>">
              <a href="<%= admin_redash_schedules_path %>"><span><%= t('navigation.redash_schedules') %></span></a>
            </li>
          <% end %>
          <% if nav_accessible(access_list, admin_redash_batches_path) %>
            <li class="<%= 'active' if controller?('admin/redash_batches') %>">
              <a href="<%= admin_redash_batches_path %>"><span><%= t('navigation.redash_batches') %></span></a>
            </li>
          <% end %>
        </ul>
      </li>
    <% end %>

    <% inventory_paths = [
      admin_user_inventories_path,
      admin_inventories_path,
      admin_cards_path,
      admin_credit_cards_path
      ] %>
    <% if parent_nav_accessible(access_list, inventory_paths) %>
      <li class="<%= 'active' if inventory_paths.include?(controller_path) %>">
        <a href="#inventories" data-toggle="collapse">
          <em class="fas fa-boxes"></em>
          <span><%= t('navigation.inventories') %></span>
        </a>
        <ul id="inventories" class="sidebar-nav sidebar-subnav collapse">
          <% if nav_accessible(access_list, admin_user_inventories_path) %>
            <li class="<%= 'active' if controller?('admin/user_inventories') %>">
              <a href="<%= admin_user_inventories_path %>"><span><%= t('navigation.summary') %></span></a>
            </li>
          <% end %>
          <% if nav_accessible(access_list, admin_inventories_path) %>
            <li class="<%= 'active' if controller?('admin/inventories') %>">
              <a href="<%= admin_inventories_path %>"><span><%= t('navigation.inventories') %></span></a>
            </li>
          <% end %>
          <% if nav_accessible(access_list, admin_cards_path) %>
            <li class="<%= 'active' if controller?('admin/cards') %>">
              <a href="<%= admin_cards_path %>"><span><%= t('navigation.cards') %></span></a>
            </li>
          <% end %>
          <% if nav_accessible(access_list, admin_credit_cards_path) %>
            <li class="<%= 'active' if controller?('admin/credit_cards') %>">
              <a href="<%= admin_credit_cards_path %>"><span><%= t('navigation.credit_cards') %></span></a>
            </li>
          <% end %>
        </ul>
      </li>
    <% end %>

    <% if nav_accessible(access_list, admin_fms_roles_path) %>
      <li class="<%= 'active' if controller?('admin/fms_roles') %>">
        <a href="<%= admin_fms_roles_path %>">
          <em class="fas fa-user-shield"></em>
          <span><%= t('navigation.fms_roles') %></span>
        </a>
      </li>
    <% end %>

    <% settlement_paths = [
      admin_settlement_logs_path,
      admin_user_settlements_path
      ] %>

    <% if parent_nav_accessible(access_list, settlement_paths) %>
      <li class="<%= 'active' if settlement_paths.include?(controller_path) %>">
        <a href="#settlements" data-toggle="collapse">
          <em class="fas fa-money-bill"></em>
          <span><%= t('navigation.settlements') %></span>
        </a>
        <ul id="settlements" class="sidebar-nav sidebar-subnav collapse">
          <% if nav_accessible(access_list, admin_user_settlements_path) %>
            <li class="<%= 'active' if controller?('admin/user_settlements') %>">
              <a href="<%= admin_user_settlements_path %>"><span><%= t('navigation.summary') %></span></a>
            </li>
          <% end %>

          <% if nav_accessible(access_list, admin_settlement_logs_path) %>
            <li class="<%= 'active' if controller?('admin/settlement_logs') %>">
              <a href="<%= admin_settlement_logs_path %>"><span><%= t('navigation.settlements') %></span></a>
            </li>
          <% end %>

          <% if nav_accessible(access_list, index_shopping_cash_admin_settlement_logs_path) %>
            <li class="<%= 'active' if controller?('admin/shopping_cash') %>">
              <a href="<%= index_shopping_cash_admin_settlement_logs_path %>"><span><%= t('navigation.shopping_cash') %></span></a>
            </li>
          <% end %>

          <% if nav_accessible(access_list, settlement_adjustments_admin_settlement_logs_path) %>
            <li class="<%= 'active' if controller?('admin/settlement_adjustments') %>">
              <a href="<%= settlement_adjustments_admin_settlement_logs_path %>"><span><%= t('navigation.settlement_adjustments') %></span></a>
            </li>
          <% end %>
        </ul>
      </li>
    <% end %>

    <% if nav_accessible(access_list, admin_letters_path) %>
      <li class="<%= 'active' if controller?('admin/letters') %>">
        <a href="<%= admin_letters_path %>">
          <em class="fas fa-file-signature"></em>
          <span><%= t('navigation.letters') %></span>
        </a>
      </li>
    <% end %>

    <% if nav_accessible(access_list, admin_policies_path) %>
      <li class="<%= 'active' if controller?('admin/policies') %>">
        <a href="<%= admin_policies_path %>">
          <em class="fas fa-tasks"></em>
          <span><%= t('navigation.policy') %></span>
        </a>
      </li>
    <% end %>

    <% training_paths = [
      admin_trainings_path,
      admin_quiz_appeals_path
      ] %>
    <% if parent_nav_accessible(access_list, training_paths) %>
      <li class="<%= 'active' if training_paths.include?(controller_path) %>">
        <a href="#trainings" data-toggle="collapse">
          <em class="fas fa-book-open"></em>
          <span><%= t('navigation.trainings') %></span>
        </a>
        <ul id="trainings" class="sidebar-nav sidebar-subnav collapse">
          <% if nav_accessible(access_list, admin_trainings_path) %>
            <li class="<%= 'active' if controller?('admin/trainings') || controller?('admin/training_submodules') %>">
              <a href="<%= admin_trainings_path %>"><span><%= t('navigation.training_cms') %></span></a>
            </li>
            <li class="<%= 'active' if controller?('admin/quiz_appeals') %>">
              <a href="<%= admin_quiz_appeals_path %>"><span><%= t('navigation.quiz_appeal') %></span></a>
            </li>
          <% end %>
        </ul>
      </li>
    <% end %>
  </ul>
<% end %>
<%= render template: "layouts/application" %>
