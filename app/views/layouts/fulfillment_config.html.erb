<% content_for :navigation_menu do %>
  <ul class="sidebar-nav">
    <li class="nav-heading">
      <span>Fulfillment Configuration</span>
    </li>
    <% access_list = user_access_list() %>

    <% country_paths = [
      fulfillment_config_countries_path,
      fulfillment_config_packaging_types_path
      ] %>

    <% if parent_nav_accessible(access_list, country_paths) %>
      <li class="<%= 'active' if country_paths.include?(controller_path) %>">
        <a href="#countries" data-toggle="collapse">
          <em class="fas fa-globe"></em>
          <span><%= t('navigation.countries') %></span>
        </a>
        <ul id="countries" class="sidebar-nav sidebar-subnav collapse">
          <% if nav_accessible(access_list, fulfillment_config_countries_path) %>
            <li class="<%= 'active' if controller?('fulfillment_config/countries') %>">
              <a href="<%= fulfillment_config_countries_path %>"><span><%= t('navigation.country') %></span></a>
            </li>
          <% end %>
          <% if nav_accessible(access_list, fulfillment_config_packaging_types_path) %>
            <li class="<%= 'active' if controller?('fulfillment_config/packaging_types') %>">
              <a href="<%= fulfillment_config_packaging_types_path %>"><span><%= t('navigation.packaging_types') %></span></a>
            </li>
          <% end %>
        </ul>
      </li>
    <% end %>

    <% store_paths = [
      fulfillment_config_generals_path,
      fulfillment_config_shop_n_deliveries_path,
      fulfillment_config_packaging_stock_locations_path,
      fulfillment_config_ge_and_tpls_path,
      fulfillment_config_ddfs_path,
      fulfillment_config_store_coverages_path,
      fulfillment_config_mass_update_index_path
      ] %>

    <% if parent_nav_accessible(access_list, store_paths) %>
      <li class="<%= 'active' if store_paths.include?(controller_path) %>">
        <a href="#stores" data-toggle="collapse">
          <em class="fas fa-shopping-cart"></em>
          <span><%= t('navigation.stores') %></span>
        </a>
        <ul id="stores" class="sidebar-nav sidebar-subnav collapse">
          <% if nav_accessible(access_list, fulfillment_config_generals_path) %>
            <li class="<%= 'active' if controller?('fulfillment_config/generals') %>">
              <a href="<%= fulfillment_config_generals_path %>"><span><%= t('navigation.general') %></span></a>
            </li>
          <% end %>
          <% if nav_accessible(access_list, fulfillment_config_shop_n_deliveries_path) %>
            <li class="<%= 'active' if controller?('fulfillment_config/shop_n_deliveries') %>">
              <a href="<%= fulfillment_config_shop_n_deliveries_path %>"><span><%= t('navigation.shopping_and_delivery') %></span></a>
            </li>
          <% end %>
          <% if nav_accessible(access_list, fulfillment_config_packaging_stock_locations_path) %>
            <li class="<%= 'active' if controller?('fulfillment_config/packagings') %>">
              <a href="<%= fulfillment_config_packaging_stock_locations_path %>"><span><%= t('navigation.store_packaging') %></span></a>
            </li>
          <% end %>
          <% if nav_accessible(access_list, fulfillment_config_ge_and_tpls_path) %>
            <li class="<%= 'active' if controller?('fulfillment_config/ge_and_tpls') %>">
              <a href="<%= fulfillment_config_ge_and_tpls_path %>"><span><%= t('navigation.ge_and_3pl') %></span></a>
            </li>
          <% end %>
          <% if nav_accessible(access_list, fulfillment_config_ddfs_path) %>
            <li class="<%= 'active' if controller?('fulfillment_config/ddfs') %>">
              <a href="<%= fulfillment_config_ddfs_path %>"><span><%= t('navigation.ddfs') %></span></a>
            </li>
          <% end %>
          <% if nav_accessible(access_list, fulfillment_config_store_coverages_path) %>
            <li class="<%= 'active' if controller?('fulfillment_config/store_coverages') %>">
              <a href="<%= fulfillment_config_store_coverages_path %>"><span><%= t('navigation.store_coverages') %></span></a>
            </li>
          <% end %>
          <% if nav_accessible(access_list, fulfillment_config_mass_update_index_path) %>
            <li class="<%= 'active' if controller?('fulfillment_config/mass_update') %>">
              <a href="<%= fulfillment_config_mass_update_index_path %>"><span><%= t('navigation.mass_update') %></span></a>
            </li>
          <% end %>
        </ul>
      </li>
    <% end %>

    <% capacity_paths = [
      fulfillment_config_slots_path,
      fulfillment_config_long_slots_path,
      fulfillment_config_shifts_path,
      fulfillment_config_fleet_plan_dashboard_path,
      fulfillment_config_slot_availability_dashboard_path
      ] %>

    <% if parent_nav_accessible(access_list, country_paths) %>
      <li class="<%= 'active' if country_paths.include?(controller_path) %>">
        <a href="#capacities" data-toggle="collapse">
          <em class="fas fa-cube"></em>
          <span><%= t('navigation.capacities') %></span>
        </a>
        <ul id="capacities" class="sidebar-nav sidebar-subnav collapse">
          <% if nav_accessible(access_list, fulfillment_config_slots_path) %>
            <li class="<%= 'active' if controller?('fulfillment_config/slots') %>">
              <a href="<%= fulfillment_config_slots_path %>"><span><%= t('navigation.slots') %></span></a>
            </li>
          <% end %>
          <% if nav_accessible(access_list, fulfillment_config_long_slots_path) %>
            <li class="<%= 'active' if controller?('fulfillment_config/long_slots') %>">
              <a href="<%= fulfillment_config_long_slots_path %>"><span><%= t('navigation.long_slots') %></span></a>
            </li>
          <% end %>
          <% if nav_accessible(access_list, fulfillment_config_shifts_path) %>
            <li class="<%= 'active' if controller?('fulfillment_config/shifts') %>">
              <a href="<%= fulfillment_config_shifts_path %>"><span><%= t('navigation.shifts') %></span></a>
            </li>
          <% end %>
          <% if nav_accessible(access_list, fulfillment_config_fleet_plan_dashboard_path) %>
            <li class="<%= 'active' if controller?('fulfillment_config/fleet_plan_dashboard') %>">
              <a href="<%= fulfillment_config_fleet_plan_dashboard_path %>"><span><%= t('navigation.fleet_plan_dashboard') %></span></a>
            </li>
          <% end %>
          <% if nav_accessible(access_list, fulfillment_config_slot_availability_dashboard_path) %>
            <li class="<%= 'active' if controller?('fulfillment_config/slot_availability_dashboard') %>">
              <a href="<%= fulfillment_config_slot_availability_dashboard_path %>"><span><%= t('navigation.slot_availability_dashboard') %></span></a>
            </li>
          <% end %>
        </ul>
      </li>
    <% end %>

    <% dispatcher_dashboard_paths = [
      fulfillment_config_manual_assignment_path,
      fulfillment_config_manual_arrival_path
      ] %>
    <% if parent_nav_accessible(access_list, dispatcher_dashboard_paths) %>
      <li class="<%= 'active' if dispatcher_dashboard_paths.include?(controller_path) %>">
        <a href="#dispatcher_dashboard" data-toggle="collapse">
          <em class="fas fa-users"></em>
          <span><%= t('navigation.dispatcher_dashboard') %></span>
        </a>
        <ul id="dispatcher_dashboard" class="sidebar-nav sidebar-subnav collapse">
          <% if nav_accessible(access_list, fulfillment_config_manual_assignment_path) %>
            <li class="<%= 'active' if controller?('fulfillment_config/manual_assignments') %>">
              <a href="<%= fulfillment_config_manual_assignment_path %>"><span><%= t('navigation.manual_assignment') %></span></a>
            </li>
          <% end %>
          <% if nav_accessible(access_list, fulfillment_config_manual_assignment_path) %>
            <li class="<%= 'active' if controller?('fulfillment_config/manual_arrivals') %>">
              <a href="<%= fulfillment_config_manual_arrival_path %>"><span><%= t('navigation.manual_update_status') %></span></a>
            </li>
          <% end %>
          <% if nav_accessible(access_list, fulfillment_config_manual_order_mutation_path) %>
            <li class="<%= 'active' if controller?('fulfillment_config/manual_order_mutations') %>">
              <a href="<%= fulfillment_config_manual_order_mutation_path %>"><span><%= t('navigation.manual_order_mutation') %></span></a>
            </li>
          <% end %>
        </ul>
      </li>
    <% end %>

    <% on_demand_order_paths = [
      fulfillment_config_on_demand_orders_path,
      fulfillment_config_express_configurations_path,
      fulfillment_config_on_demand_clusters_path
      ] %>

    <% if parent_nav_accessible(access_list, on_demand_order_paths) %>
      <li class="<%= 'active' if on_demand_order_paths.include?(controller_path) %>">
        <a href="#on-demand-orders" data-toggle="collapse">
          <em class="fas fa-shipping-fast"></em>
          <span><%= t('navigation.express') %></span>
        </a>
        <ul id="on-demand-orders" class="sidebar-nav sidebar-subnav collapse">
          <% if nav_accessible(access_list, fulfillment_config_on_demand_orders_path) %>
            <li class="<%= 'active' if controller?('fulfillment_config/on_demand_orders') %>">
              <a href="<%= fulfillment_config_on_demand_orders_path %>"><span><%= t('navigation.manual_assignment') %></span></a>
            </li>
          <% end %>
          <% if nav_accessible(access_list, fulfillment_config_express_configurations_path) %>
            <li class="<%= 'active' if controller?('fulfillment_config/express_configurations') %>">
              <a href="<%= fulfillment_config_express_configurations_path %>"><span><%= t('navigation.configuration') %></span></a>
            </li>
          <% end %>
          <% if nav_accessible(access_list, fulfillment_config_on_demand_clusters_path) %>
            <li class="<%= 'active' if controller?('fulfillment_config/on_demand_clusters') %>">
              <a href="<%= fulfillment_config_on_demand_clusters_path %>"><span><%= t('navigation.on_demand_clusters') %></span></a>
            </li>
          <% end %>
        </ul>
      </li>
    <% end %>

    <% if nav_accessible(access_list, fulfillment_config_credit_cards_path) %>
      <li class="<%= 'active' if controller?('fulfillment_config/credit_cards') %>">
        <a href="<%= fulfillment_config_credit_cards_path %>" title="Credit Cards">
          <em class="fas fa-credit-card"></em>
          <span><%= t('navigation.credit_cards') %></span>
        </a>
      </li>
    <% end %>

  </ul>
<% end %>
<%= render template: "layouts/application" %>
