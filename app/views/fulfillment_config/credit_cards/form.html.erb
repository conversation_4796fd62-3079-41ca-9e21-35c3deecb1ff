<div class="content-heading">
  <div>
    <%= t('.page_title') %>
  </div>
</div>

<div class="container-fluid">
  <div class="row">
    <div class="col-md-8">
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">
            <i class="fas fa-credit-card mr-2"></i>
            <%= t('.card_details') %>
          </h5>
        </div>
        <div class="card-body">
          <div class="form-group row">
            <%= label_tag t('.name'), nil, class: "col-md-3 col-form-label" %>
            <div class="col-md-6">
              <p class="form-control-plaintext"><%= @credit_card["name"] %></p>
            </div>
          </div>

          <div class="form-group row">
            <%= label_tag t('.last_digits'), nil, class: "col-md-3 col-form-label" %>
            <div class="col-md-6">
              <p class="form-control-plaintext"><%= @credit_card["last_digits"] %></p>
            </div>
          </div>

          <div class="form-group row">
            <%= label_tag t('.expiry_month'), nil, class: "col-md-3 col-form-label" %>
            <div class="col-md-6">
              <p class="form-control-plaintext"><%= @credit_card["month"] %></p>
            </div>
          </div>

          <div class="form-group row">
            <%= label_tag t('.expiry_year'), nil, class: "col-md-3 col-form-label" %>
            <div class="col-md-6">
              <p class="form-control-plaintext"><%= @credit_card["year"] %></p>
            </div>
          </div>

          <%= form_with(url: "#", method: :put, class: "form-horizontal", local: true) do |f| %>
            <div class="form-group row">
              <%= f.label t('.shopper_email'), class: "col-md-3 col-form-label" %>
              <div class="col-md-6">
                <%= f.select :user_id,
                  if @current_selected_user_id.present?
                    [["Select user to assign", 0]] + @users.map { |u| [ u["email"], u["id"].to_i ] }
                  else
                    @users.map { |u| [ u["email"], u["id"].to_i ] }
                  end,
                  {
                    prompt: "Select user to assign",
                    selected: @current_selected_user_id
                  },
                  {
                    class: "my-sel-2 w-100",
                    required: true
                  } %>
              </div>
            </div>
            <div class="form-group row">
              <%= label_tag t('.state'), nil, class: "col-md-3 col-form-label" %>
              <div class="col-md-6">
                <%= f.select :state,
                  ["ENABLED", "DISABLED", "AUTOMATIC"],
                  {
                    prompt: "Select state",
                    selected: @credit_card["state"]
                  },
                  {
                    class: "my-sel-2 w-100",
                    required: true
                  } %>
            </div>
          </div>
            <div class="form-group row">
              <div class="col-md-3">
                <%= f.submit t('.update'),
                  class: "btn btn-primary btn-sm" %>
              </div>
            </div>
          <% end %>
        </div>
        <div class="card-footer">
          <%= link_to t('actions.back'), fulfillment_config_credit_cards_path, class: "btn btn-secondary" %>
        </div>
      </div>
    </div>
  </div>
</div>

<% content_for :body_area do %>
<script>
$(document).ready(function () {
  // Initialize Select2 for user selection
  $(".my-sel-2").select2({
    theme: "bootstrap4",
  });
});
</script>
<% end %>
