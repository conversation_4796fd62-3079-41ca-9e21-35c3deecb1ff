<div class="content-heading">
  <div>
    <%= t('.page_title') %>
  </div>
</div>

<div class="container-fluid">
  <% if @loading %>
    <div class="d-flex justify-content-center align-items-center" style="min-height: 200px;">
      <div class="spinner-border text-primary" role="status">
        <span class="sr-only">Loading...</span>
      </div>
      <span class="ml-2">Loading credit cards...</span>
    </div>
  <% elsif @error_message %>
    <div class="alert alert-danger" role="alert">
      <i class="fas fa-exclamation-triangle"></i>
      <%= @error_message %>
    </div>
  <% elsif @credit_cards.empty? %>
    <div class="empty-history mt-3 d-flex flex-column align-items-center">
      <div class="mb-2">
        <i class="fas fa-credit-card fa-3x text-muted"></i>
      </div>
      <div class="text-muted text-center w-75"><%= t(".no_credit_cards") %></div>
    </div>
  <% else %>
    <div>
      <%= link_to new_fulfillment_config_credit_card_path, class: "btn btn-primary" do %>
        <span class="fas fa-plus"></span> <%= t(".new_credit_card") %>
      <% end %>
    </div>
    <div class="card mt-2">
      <div class="card-body">
        <table class="table table-striped my-4 w-100" id="datatable1">
          <thead>
            <tr>
              <th scope="col"><%= t(".name") %></th>
              <th scope="col"><%= t(".last_digits") %></th>
              <th scope="col"><%= t(".month") %></th>
              <th scope="col"><%= t(".year") %></th>
              <th scope="col"><%= t(".shopper_email") %></th>
              <th scope="col"><%= t(".state") %></th>
              <th scope="col"><%= t("actions.column_name") %></th>
            </tr>
          </thead>
          <tbody>
            <% @credit_cards.each_with_index do |card, index| %>
              <tr id="credit_card_<%= index %>">
                <td class="card_name"><%= card["name"] %></td>
                <td class="last_digits">**** <%= card["last_digits"] %></td>
                <td class="month"><%= card["month"] %></td>
                <td class="year"><%= card["year"] %></td>
                <td class="shopper_email"><%= card["users"].present? ? card["users"][0]["email"] : "" %></td>
                <td class="state">
                  <% case card["state"] %>
                  <% when "ENABLED" %>
                    <span class="badge badge-success"><%= t('.state_enabled') %></span>
                  <% when "DISABLED" %>
                    <span class="badge badge-danger"><%= t('.state_disabled') %></span>
                  <% when "AUTOMATIC" %>
                    <span class="badge badge-info"><%= t('.state_automatic') %></span>
                  <% else %>
                    <span class="badge badge-secondary"><%= card["state"] %></span>
                  <% end %>
                </td>
                <td class="actions">
                  <%= link_to t("actions.view"), fulfillment_config_credit_card_path(card["id"]), 
                      class: "btn btn-sm btn-outline-primary" %>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>
    </div>
  <% end %>
</div>


