module TrainingsHelper
  def success_threshold_in_percentage(threshold)
    percentage = (threshold.to_f / 10) * 100
    number_with_precision(percentage, precision: 1, strip_insignificant_zeros: true)
  end

  def multiple_choice_letter(index)
    ("A".ord + index).chr
  end

  def visited_modules_slugs
    @visited_modules_slugs ||= @user.user_trainings
                .includes(:training_submodule)
                .inject({}) do |output, progress|
      module_id = progress.training_submodule.try(:training_module_id)
      # handle soft-deleted submodule
      if module_id
        submodule_id = progress.training_submodule_id
        output[module_id] ||= []
        output[module_id] << [submodule_id, progress.training_submodule.seq]
      end

      output
    end
  end

  # returns to current module last visited submodule if any visited,
  # otherwise returns first sequence of given module
  def url_to_next_submodule_sequence(training_module)
    id, seq = visited_modules_slugs[training_module.id].try(:last).presence || [nil, nil]
    return snd_training_submodules_url(module_slug: training_module.slug, seq: 1) if !id || !seq

    snd_training_submodules_url(module_slug: training_module.slug, seq: seq)
  end

  # TODO: Refactor!
  def module_unlocked?(training_module)
    prev_module = TrainingModule.kept.where(training_type_id: training_module.training_type_id)
                                     .where("COALESCE(seq, 0) < ?", training_module.seq.to_i)
                                     .order(seq: :desc)
                                     .first
    # in-between module access rules
    if prev_module && (submodules_completed?(prev_module) && module_quiz_passed?(prev_module))
      true
    # first training module visit
    elsif !prev_module && training_module.seq == 1
      true
    else
      false
    end
  end

  def module_quiz_passed?(training_module)
    return false unless training_module

    @user.quiz_sessions
                .joins(:quiz)
                .completed
                .select("DISTINCT ON (quiz_sessions.quiz_id) quiz_sessions.*")
                .where("quizzes.training_module_id = ?", training_module.id)
                .order("quiz_sessions.quiz_id, quiz_sessions.created_at DESC")
                .select { |qs| qs.passed? }
                .map { |qs| qs.quiz&.training_module_id }
                .compact
                .any?
  end

  # To check all submodules already completed
  def submodules_completed?(training_module)
    return false unless training_module

    visited_modules_slugs[training_module.id].try(:size).to_i >= training_module.submodules_count.to_i
  end

  def search_result_row_unlocked?(row)
    if row["source_type"].eql?(TrainingModule.name)
      visited_modules_slugs.keys.include?(row["module_id"].to_i) || row["seq"].to_i <= 1
    elsif row["source_type"].eql?(TrainingSubmodule.name)
      record = visited_modules_slugs[row["module_id"].to_i]
      return false unless record

      record.map(&:first).reject(&:nil?).map(&:to_i).include?(row["submodule_id"].to_i)
    else
      false
    end
  end

  def set_user(user)
    @user = user || current_user
  end

  def user_quiz_attempt?(training_module)
    QuizAttempt.joins(:quiz).where(user_id: @user.id).where(quizzes: { training_module_id: training_module.id }).any?
  end
end
