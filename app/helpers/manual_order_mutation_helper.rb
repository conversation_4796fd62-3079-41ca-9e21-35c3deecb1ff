module ManualOrderMutationHelper
  def reason_options_for(context_name)
    send("#{context_name}_context_reasons")
  rescue NoMethodError
    [other_reason]
  end

  def shipment_manual_order_mutation_context_reasons
    t("fulfillment_config.manual_order_mutations.form.reasons.shipment").map do |i18n_key, translation|
      [translation, i18n_key]
    end << other_reason
  end

  def other_reason
    [other_reason_text, "others"]
  end

  def validation_alert_message(_delivery_state)
    content_tag(:div, class: "d-flex flex-column justify-content-center text-center") do
      t("fulfillment_config.manual_order_mutations.e.invalid_delivery_state.other_html")
    end
  end

  def humanize_reason_string(context_name, reason)
    return other_reason_text if reason.eql?("others")

    t("fulfillment_config.manual_order_mutations.form.reasons.#{context_name}.#{reason}") rescue other_reason_text
  end

  def other_reason_text
    t("fulfillment_config.manual_order_mutations.form.reasons.others")
  end

  def humanize_job_state(state)
    t("fulfillment_config.manual_order_mutations.form.states.#{state}") rescue t("fulfillment_config.manual_order_mutation.e.unknown")
  end
end
