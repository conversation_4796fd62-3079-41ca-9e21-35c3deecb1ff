module PayrollHelper
  def facts_for_display
    PayrollFact.all.pluck(:id, :name).to_h
  end

  def payroll_batch_label_status(status)
    case status
    when "unpublished"
      I18n.t("admin.payslips.payslip_batch.unpublished_label")
    else
      status
    end
  end

  def payroll_batch_message(status)
    case status
    when "unpublished"
      I18n.t("admin.payslips.payslip_batch.unpublished_message")
    else
      ""
    end
  end

  def separate_user_adjustments_by_gross(user_adjustments)
    # UserAdjustment stores adjustment_condition_id(s) whether the adjustement_condition already deleted or not
    # so, we retrieve all adjustment_condition_id(s)
    if user_adjustments.present?
      adjustment_condition_ids = user_adjustments.pluck(:adjustment_condition_id)
    else
      raise I18n.t("admin.payslips.e.user_adjustments_nil")
    end

    # these two lines below using with_dicarded to filter based on is_after_gross field in AdjustmentCondition table
    after_gross = AdjustmentCondition.with_discarded.where(id: adjustment_condition_ids, is_after_gross: true)
    before_gross = AdjustmentCondition.with_discarded.where(id: adjustment_condition_ids, is_after_gross: false)

    # based on is_after_gross filter, they are divided into two variables below
    # for after gross, only display user_adjustment with is_condition_fulfilled TRUE
    after_gross_adjustments = user_adjustments.where(adjustment_condition_id: [after_gross.pluck(:id)], is_condition_achieved: true)
    before_gross_adjustments = user_adjustments.where(adjustment_condition_id: [before_gross.pluck(:id)])

    return after_gross_adjustments, before_gross_adjustments
  end

  def include_discarded_adjustment_condition(id)
    adjustment_condition = AdjustmentCondition.with_discarded.where(id: id)
    if adjustment_condition.present?
      adjustment_condition.first
    else
      raise I18n.t("admin.payslips.e.invalid_adjustment_condition_id")
    end
  end

  def include_discarded_adjustment_item(adjustment_condition_id)
    adjustment_item = AdjustmentItem.with_discarded.where(adjustment_condition_id: adjustment_condition_id)
    if adjustment_item.present?
      adjustment_item.first
    else
      raise I18n.t("admin.payslips.e.invalid_adjustment_condition_id")
    end
  end

  def include_discarded_adjustment_after_gross(adjustment_condition_id)
    adjustment_after_gross = AdjustmentAfterGross.with_discarded.where(adjustment_condition_id: adjustment_condition_id)
    if adjustment_after_gross.present?
      adjustment_after_gross.first
    else
      raise I18n.t("admin.payslips.e.invalid_adjustment_condition_id")
    end
  end
end
