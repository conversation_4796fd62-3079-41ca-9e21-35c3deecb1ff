module ManualArrival<PERSON><PERSON>per
  def reason_options_for_manual_arrival(context_name, data = {})
    send(:"#{context_name}_context_reasons", data)
  rescue NoMethodError
    [other_reason]
  end

  def shipment_manual_arrival_context_reasons(data)
    context = data.start_with?("shopper") ? "shopper" : "driver"
    t("fulfillment_config.manual_arrivals.form.reasons.shipment.#{context}").map do |i18n_key, translation|
      [translation, i18n_key]
    end << other_reason
  end

  def other_reason
    [other_reason_text, "others"]
  end

  def validation_alert_message(_delivery_state)
    content_tag(:div, class: "d-flex flex-column justify-content-center text-center") do
      t("fulfillment_config.manual_arrivals.e.invalid_delivery_state.other_html")
    end
  end

  def humanize_reason_string(context_name, reason)
    return other_reason_text if reason.eql?("others")

    t("fulfillment_config.manual_arrivals.form.reasons.#{context_name}.#{reason}") rescue other_reason_text
  end

  def other_reason_text
    t("fulfillment_config.manual_arrivals.form.reasons.others")
  end

  def humanize_job_state(state)
    t("fulfillment_config.manual_arrivals.form.states.#{state}") rescue t("fulfillment_config.manual_arrivals.e.unknown")
  end

  def states_option_for(context_name)
    send("#{context_name}_context_states")
  rescue NoMethodError
    []
  end

  def shipment_context_states
    {
      "driver_found_address" => t("fulfillment_config.manual_arrivals.form.states.driver.found_address"),
      "shopper_started" => t("fulfillment_config.manual_arrivals.form.states.shopper.started")
    }
  end
end
