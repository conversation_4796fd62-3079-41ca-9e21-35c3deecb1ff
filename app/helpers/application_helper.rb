module ApplicationHelper
  include ViewStyleHelper

  def controller?(*controller)
    controller.include?(params[:controller])
  end

  # Returns the current layout name. Allows asserting the rendered layout name
  # in our feature specs as an alternative to the deprecated #render_template
  # matcher.
  def layout_name
    # _layout is a private method, this may break at any time. It requires an
    # array containing a stringas an argument but the string value does not
    # affect the output.
    controller.send :_layout, ["some_string_here"]
  end

  def alternate_locales
    I18n.available_locales.map { |l|
      yield(l)
    }.join.html_safe
  end

  def google_oauth2_client_id
    Devise.omniauth_configs[:google_oauth2].strategy["client_id"]
  end

  # Select the appropriate Boostrap class for Rails"s flash messages
  def bootstrap_class_for(flash_type)
    case flash_type
    when "success"
      "alert-success"   # Green
    when "error"
      "alert-danger"    # Red
    when "alert"
      "alert-warning"   # Yellow
    else
      "alert-info"      # Blue
    end
  end

  # Display model validation errors in form templates
  def display_validation_errors(object)
    return "" if object.errors.empty?

    header = I18n.t("activerecord.errors.template.header",
                    count: object.errors.count)
    msgs = object.errors.full_messages.map { |msg| content_tag(:li, msg) }.join

    html = <<-HTML
      <div class="alert alert-danger alert-dismissable" role="alert">
        <button type="button" class="close" data-dismiss="alert">
          <span aria-hidden="true">&times;</span><span class="sr-only">Close</span>
        </button>
        <h4>
          #{header}
        </h4>
        <ul>#{msgs}</ul>
      </div>
    HTML

    html.html_safe
  end

  # For bootstrap CSS
  def guess_status_color(status_string)
    case status_string
    when "requested"
      "warning"
    when "rejected"
      "danger"
    when "approved"
      "success"
    when "completed"
      "primary"
    when "processing"
      "primary"
    when "failed"
      "danger"
    when "successful"
      "success"
    when "pending"
      "dark"
    when "canceled"
      "danger"
    when "sent"
      "warning"
    when "received"
      "success"
    when "not_received"
      "danger"
    when "unpublished"
      "gray-light"
    when "published"
      "success"
    when ""
      ""
    else
      "dark"
    end
  end

  # For bootstrap CSS for SND style
  def guess_status_color_snd(status_string)
    case status_string
    when "approved"
      "green"
    when "received"
      "green"
    when "completed"
      "green"
    when "pending"
      "dark"
    when "requested"
      "orange"
    when "sent"
      "orange"
    when "rejected"
      "red"
    when "canceled"
      "red"
    when "not_received"
      "red"
    when ""
      ""
    else
      "dark"
    end
  end

  def current_country
    current_tenant = Apartment::Tenant.current

    if current_tenant == "public"
      if Rails.env == "test"
        country_code = "ID"
      else
        return nil
      end
    else
      country_code = current_tenant.upcase
    end

    Country.find_by_iso_name(country_code)
  end

  def nav_accessible(access_list, route_path)
    if user_has_all_access_role
      return true
    end

    route = Rails.application.routes.recognize_path(route_path)
    # will return something like this: {:controller=>"admin/news", :action=>"index"}

    # Mapping "new" -> "create", "edit" -> "update"
    if route[:action] == "new"
      action = "create"
    elsif route[:action] == "edit"
      action = "update"
    else
      action = route[:action]
    end

    # current user access list is stored on access_list array
    access_list.include? route[:controller].classify.pluralize.concat("Controller - #{action}")
  end

  def parent_nav_accessible(access_list, route_paths)
    if user_has_all_access_role
      return true
    end

    route_paths.each do |path|
      route = Rails.application.routes.recognize_path(path)
      # returns true if there is at least one path accessible.
      if ! access_list.include?(route[:controller].classify.pluralize.concat("Controller - #{route[:action]}"))
        next
      else
        return true
      end
    end

    # Only return FALSE if all controller_paths is not accessible
    false
  end

  def user_access_list
    if current_user.admin?
      ActionAccess.where(role_id: current_user.roles.ids).map(&:display_name)
    else
      []
    end
  end

  def user_has_all_access_role
    current_user.roles.where(all_access: true).count > 0
  end

  def fsrv_env_name
    url = ENV["FULFILLMENT_BASE_URL"]
    if url.include? "local"
      :local
    elsif url.include? "production"
      :production
    elsif url.include? "staging"
      :staging
    elsif url.include? "sandbox"
      :sandbox
    else
      ""
    end
  end

  def get_tz
    case Apartment::Tenant.current.downcase
    when "id"
      tz = "Asia/Jakarta"
    when "my"
      tz = "Asia/Kuala_Lumpur"
    when "th"
      tz = "Asia/Bangkok"
    else
      tz = "UTC"
    end
    tz
  end
end
