module AttachmentHelper
  def get_image_mime_type(file_path)
    png = Regexp.new("\x89PNG".force_encoding("binary"))
    jpg = Regexp.new("\xff\xd8\xff\xe0\x00\x10JFIF".force_encoding("binary"))
    exif = Regexp.new("\xff\xd8\xff\xe1(.*){2}Exif".force_encoding("binary"))
    case IO.read(file_path, 10)
    when /^GIF8/
      "image/gif"
    when /^#{png}/
      "image/png"
    when /^#{jpg}/
      "image/jpeg"
    when /^#{exif}/
      "image/jpeg"
    else
      "application/octet-stream" # RFC 2046
    end
  end

  def multiple_source_image(image)
    if image.file.filename.start_with? "http"
      image.file.filename # image from outside source (with full url)
    else
      image # image from carrierwave/cloudinary
    end
  end
end
