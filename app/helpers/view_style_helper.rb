module ViewStyleHelper
  ##
  # @cw_add_classes {Array of additional CSS class name} Example: "merit-strike form-create"
  #
  def content_wrapper_classes
    ["content-wrapper", @cw_add_classes.presence || []].flatten.reject(&:blank?).map(&:strip).join(" ")
  end

  def texteditor_sanitize_options
    { attributes: texteditor_allowed_attributes, tags: texteditor_allowed_tags }
  end

  def texteditor_allowed_attributes
    ActionView::Base.sanitized_allowed_attributes.to_a + %w[style allow allowfullscreen frameborder]
  end

  def texteditor_allowed_tags
    ActionView::Base.sanitized_allowed_tags.to_a + texteditor_allowed_media_tags + texteditor_allowed_text_tags
  end

  def texteditor_allowed_media_tags
    %w[figure iframe embed video audio]
  end

  def texteditor_allowed_text_tags
    %w[mark cite p span strong b em i a ul ol li h1 h2 h3 h4 h5 h6 u s]
  end
end
