module OrderHistoryHelper
  def render_packaging_info(order)
    return "" unless order.original_packagings.present?

    render partial: "snd/order_histories/packaging_details",
           locals: { packaging: order.original_packagings.first }
  end

  def render_order_time_info(order)
    delivery_time_text = order.delivery_time ? local_time(order.delivery_time, "%e %b %Y") : nil
    slot_info = get_slot_info(order)

    return "-" if !delivery_time_text && !slot_info
    info = [delivery_time_text]
    info += [" &middot; ", slot_info] if slot_info

    info.join.html_safe
  end

  def get_slot_info(order)
    slot_start_text = order.slot_start ? local_time(order.slot_start, "%H:%M") : nil
    slot_end_text = order.slot_end ? local_time(order.slot_end, "%H:%M") : nil

    if slot_start_text && slot_end_text
      [
        slot_start_text,
        " &dash; ",
        slot_end_text
      ].reject(&:blank?).join
    else
      nil
    end
  end
end
