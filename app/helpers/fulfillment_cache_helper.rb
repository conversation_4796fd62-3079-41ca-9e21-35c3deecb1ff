module FulfillmentCacheHelper
  def get_stock_locations_by_country!
    cache_key = "stock_locations_#{Apartment::Tenant.current.upcase}"

    stock_locations = Rails.cache.fetch(cache_key, skip_nil: true, expires_in: 15.minute) do
      interactor = ::FulfillmentConfig::GetStockLocationsByCountry.call
      if interactor.success?
        interactor.result
      else
        raise interactor.message # prevent caching on error
      end
    end

    stock_locations
  end

  def get_clusters_by_country!
    cache_key = "clusters_#{Apartment::Tenant.current.upcase}"

    clusters = Rails.cache.fetch(cache_key, skip_nil: true, expires_in: 15.minute) do
      interactor = ::FulfillmentConfig::GetClustersByCountry.call
      if interactor.success?
        interactor.result
      else
        raise interactor.message # prevent caching on error
      end
    end

    clusters
  end

  def get_cached_stock_location_by_id(id)
    return if id.blank?

    cache_key = "fulfillment_stock_location_#{ id }"
    Rails.cache.fetch(cache_key, skip_nil: true, expires_in: 1.minute) do
      sl_interactor = ::FulfillmentConfig::GetStockLocation.call(id: id)
      if sl_interactor.success?
        sl_interactor.result
      else
        raise sl_interactor.message # prevent caching on error
      end
    end
  end
end
