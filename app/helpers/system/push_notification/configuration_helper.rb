module System::PushNotification::ConfigurationHelper
  def use_cases_options_for_select
    ::PushNotification::Configuration.use_cases.map do |key, value|
      [t(key, scope: %i[system push_notification configurations form use_cases]), value]
    end
  end

  def service_types_options_for_select
    ::PushNotification::Configuration.service_types.map do |key, value|
      [t(key, scope: %i[system push_notification configurations form service_types]), value]
    end
  end
end
