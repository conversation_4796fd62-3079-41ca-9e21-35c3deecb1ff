# :nodoc:
module <PERSON><PERSON><PERSON><PERSON>
  def password_placeholder
    ("&bull;" * 8).html_safe
  end

  def last_digits_phone_number(phone_number, length = nil)
    length ||= 4
    country_code = PHONE_COUNTRY_CODE[Apartment::Tenant.current]

    number = country_code ? phone_number.to_s.gsub(/\+?#{country_code}/, "") : phone_number.to_s

    number.length > length ? number.split(//).last(length).join : number
  end

  def mask_phone_number(phone_number, mask_character = "X")
    digits = last_digits_phone_number(phone_number)
    mask_length = phone_number.to_s.length - digits.length

    "#{mask_character * mask_length}#{digits}"
  end
end
