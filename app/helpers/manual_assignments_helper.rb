module ManualAssignmentsHelper
  def compare_time(planned_time, actual_time)
    result = nil
    if actual_time.present?
      p_time = planned_time.split(":")
      p_minutes = p_time[0] * 60 + p_time[1]
      a_time = actual_time.split(":")
      a_minutes = a_time[0] * 60 + a_time[1]

      if a_minutes > p_minutes
        result = 1
      elsif a_minutes < p_minutes
        result = -1
      else
        result = 0
      end
    end

    # nil = ongoing
    # -1 = early (planned > actual)
    # 0 = on time (planned == actual)
    # 1 = late (planned < actual)
    result
  end

  def red_text_when_late(comparation)
    return "" if comparation.nil?
    css_class = ""
    css_class = "text-danger" if comparation == 1 # late
    css_class
  end

  def red_circle_when_late(comparation)
    return "" if comparation.nil?
    css_class = ""
    css_class = "complete" if comparation <= 0 # early or on time
    css_class = "complete-late" if comparation == 1 # late
    css_class
  end

  def switch_to_hf_redirect_url
    fulfillment_config_manual_assignment_url(search_type: params[:search_type], search_string: params[:search_string])
  end

  def reason_options_for(context_name)
    send("#{context_name}_context_reasons")
  rescue NoMethodError
    [other_reason]
  end

  def delivery_batch_context_reasons
    I18n.t("fulfillment_config.manual_assignments.user_assign_form.reasons.delivery_batch").map do |i18n_key, translation|
      [translation, i18n_key]
    end << other_reason
  end

  def shopping_batch_context_reasons
    I18n.t("fulfillment_config.manual_assignments.user_assign_form.reasons.shopping_batch").map do |i18n_key, translation|
      [translation, i18n_key]
    end << other_reason
  end

  def ranger_batch_context_reasons
    I18n.t("fulfillment_config.manual_assignments.user_assign_form.reasons.ranger_batch").map do |i18n_key, translation|
      [translation, i18n_key]
    end << other_reason
  end

  def other_reason
    [other_reason_text, "others"]
  end

  def other_reason_text
    I18n.t("fulfillment_config.manual_assignments.user_assign_form.reasons.others")
  end

  def humanize_reason(assignable_type, reason)
    return other_reason_text if reason.eql?("others")
    I18n.t("fulfillment_config.manual_assignments.user_assign_form.reasons.#{assignable_type}.#{reason}") rescue other_reason_text
  end
end
