module FulfillmentConfig
  class ManualAssignmentLogDatatable < ::ApplicationDatatable
    include ManualAssignmentsHelper

    private

      def columns
        %w[log_id created_at actor_id origin_fleet_id replacement_fleet_id order_number]
      end

      def data
        assignment_logs.map do |log|
          [].tap do |columns|
            columns << log.log_id
            columns << log.created_at.localtime.strftime("%d %b %Y, %H:%M")
            columns << log.actor_email
            columns << log.origin_fleet_email
            columns << log.replacement_fleet_email
            columns << log.order_number
            if log.reason == nil
              columns << ""
            elsif log.reason == "others"
              columns << log.reason_description
            else
              columns << humanize_reason(log.assignable_type, log.reason)
            end
          end
        end
      end

      def assignment_logs
        @assignment_logs ||= fetch_assignment_logs
      end

      def fetch_assignment_logs
        query = assignment_logs_query
        query = build_search_query(query)
        query = build_sorting_query(query)
        query = query.page(page).per(per_page)
        query.load
      end

      def assignment_logs_query
        actor_join = <<-SQL.squish
          LEFT JOIN (SELECT id, email FROM users) actors
          ON actors.id = manual_assignment_logs.actor_id
        SQL

        assigment_query = ManualAssignmentLog.from(@query, "manual_assignment_logs")
        if @options[:email].present?
          assigment_query = assigment_query.with_origin_fleet(@options[:email])
        elsif @options[:order_number].present?
          assigment_query = assigment_query.for_order(@options[:order_number])
        end

        assigment_query.joins(actor_join)
                       .select("manual_assignment_logs.*", "actors.email AS actor_email")
      end

      def build_search_query(base_query)
        search_keyword = params.dig("search", "value")

        if search_keyword.present?
          search_columns = %w{manual_assignment_logs.log_id manual_assignment_logs.order_number actors.email manual_assignment_logs.origin_fleet_email manual_assignment_logs.replacement_fleet_email}
          search_string = search_columns.map { |col| "#{col} ILIKE :search" }.join(" OR ")
          base_query = base_query.where(search_string, search: "%#{search_keyword}%")
        end

        base_query
      end

      def build_sorting_query(base_query)
        order_col_index = params.dig("order", "0", "column") # only support single column sorting

        case columns[order_col_index.to_i]
        when "log_id"
          base_query = base_query.order(log_id: sort_direction)
        when "created_at"
          base_query = base_query.order(created_at: sort_direction)
        when "actor_id"
          base_query = base_query.order("actors.email #{sort_direction}")
        when "origin_fleet_id"
          base_query = base_query.order("manual_assignment_logs.origin_fleet_email #{sort_direction}")
        when "replacement_fleet_id"
          base_query = base_query.order("manual_assignment_logs.replacement_fleet_email #{sort_direction}")
        else
          # default sort
          base_query = base_query.order(created_at: :desc)
        end

        base_query
      end

      def count
        assignment_logs_query.size
      end

      def total_entries
        assignment_logs.total_count
      end
  end
end
