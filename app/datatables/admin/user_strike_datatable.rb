require Rails.root.join("app", "queries", "user_strikes_query.rb").to_s

module Admin
  class UserStrikeDatatable < ApplicationDatatable
    include ActionView::Helpers::TranslationHelper

    delegate :admin_delete_user_strike_path, to: :@view

    private

      def columns
        %w[checkall log_id reference created_at description remark approved_by point deleted_by actions]
      end

      def searchable_columns
        %w{log_id reference remark}.map { |col| "#{UserStrike.table_name}.#{col}" } +
        %w{users.email users.first_name users.last_name}
      end

      def data
        entries.map do |record|
          [].tap do |columns|
            columns << bulk_id_checkbox(record)
            columns << record.log_id
            columns << record.reference.presence || "-"
            columns << local_time(record.created_at, "%d %b %Y, %H:%M")
            columns << record.strike_name
            columns << record.remark
            columns << record.creator.full_name
            columns << record.point_for_log.to_f
            columns << (record.discarded? ? record.destroyer.try(:full_name) : "")
            columns << render_action_column(record)
          end
        end
      end

      def entries
        @entries ||= fetch_entries
      end

      def fetch_entries
        query_builder = @query
        if @options[:start_date] && @options[:end_date]
          query_builder = query_builder.where("user_strikes.created_at::date BETWEEN ? AND ?", @options[:start_date].to_date, @options[:end_date].to_date)
        end

        query_builder = build_sorting_query(query_builder)
        query_builder = build_search_query(query_builder)
        query_builder = query_builder.page(page).per(per_page)

        @query = query_builder

        @query.load
      end

      def count
        @query.count
      end

      def total_entries
        entries.total_count
      end

      def render_action_column(record)
        return tag.span(t("admin.user_strikes.show_log.deleted"), class: %w{badge badge-pill badge-danger}) if record.discarded?

        link_to admin_delete_user_strike_path(record),
                class: "btn btn-default", method: :delete,
                data: { confirm: t("admin.user_strikes.show_log.delete_confirmation", log_id: record.log_id) } do
          tag.span(nil, class: "fas fa-trash")
        end
      end

      def build_sorting_query(scope)
        order_col_index = params.dig("order", "0", "column") # only support single column sorting

        case columns[order_col_index.to_i]
        when "log_id"
          scope = scope.reorder(log_id: sort_direction)
        when "reference"
          scope = scope.reorder(reference: sort_direction)
        when "created_at"
          scope = scope.reorder(created_at: sort_direction)
        when "approved_by"
          scope = scope.joins("LEFT OUTER JOIN users AS creators ON user_strikes.created_by=creators.id").reorder("CONCAT(creators.first_name, ' ', creators.last_name) #{sort_direction}")
        when "deleted_by"
          scope = scope.joins("LEFT OUTER JOIN users AS destroyers ON user_strikes.discarded_by=destroyers.id").reorder("CONCAT(destroyers.first_name, ' ', destroyers.last_name) #{sort_direction}")
        when "point"
          scope = scope.reorder(Arel.sql("CASE WHEN is_appeal = #{true} THEN (point * -1) ELSE point END #{sort_direction}"))
        else
          # default sort
          scope = scope.order(UserStrikesQuery::DEFAULT_ORDER)
        end

        scope
      end

      def build_search_query(scope)
        search_keyword = params.dig("search", "value")

        scope = if search_keyword.present?
          search_string = searchable_columns.map { |col| "#{col} ILIKE :search" }.join(" OR ")
          scope = scope.left_outer_joins(:creator).left_outer_joins(:destroyer)
          scope.where(search_string, search: "%#{search_keyword}%")
        else
          scope
        end

        scope
      end

      def bulk_id_checkbox(record)
        checkbox = tag(:input, type: :checkbox, name: "bulk_ids[]", value: record.id, checked: false, disabled: record.discarded?, class: "checkall-item", id: "user_strikes_checkitem_#{record.id}")
        content_tag(:div, checkbox, class: "form-check pl-1 text-center")
      end
    # end of private block
  end
end
