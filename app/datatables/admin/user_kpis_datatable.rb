class Admin::UserKpisDatatable < ApplicationDatatable
  delegate :admin_user_kpi_path, to: :@view

  private

    def data
      user_kpis.map do |user|
        [].tap do |column|
          column << user.email
          column << user.fms_role.name
          column << user.fms_role.performance_schemes.map { |scheme| "<div>#{scheme.name}</div>" }.join("")

          userkpi_sample = UserKpi.where(email: user.email).order(updated_at: :desc).first
          if userkpi_sample.present?
            column << "#{time_ago_in_words(local_time(userkpi_sample.updated_at))} #{I18n.t('datetime.ago')}"
          else
            column << "-"
          end

          links = []
          links << link_to(admin_user_kpi_path(user), class: "btn btn-secondary btn-sm") do
                    tag.span(class: %w{fas fa-eye}) + " " + I18n.t("admin.user_kpis.index.view_user_kpis")
                  end
          column << links.join("")
        end
      end
    end

    def count
      @query.count
    end

    def total_entries
      user_kpis.total_count
    end

    def user_kpis
      @user_kpis ||= fetch_user_kpis
    end

    def sort_column
      if params[:order].present?
        col = columns[params[:order]["0"][:column].to_i]

        if col == "fms_role"
          col = "fms_roles.name"
        elsif col == "kpi_scheme"
          col = "performance_schemes.name"
        end

        col
      else
        columns[0]
      end
    end

    def fetch_user_kpis
      search_string = []
      # exclude search on updated_at column
      columns.reject { |col| col.include?("updated_at") }.each do |search_term|
        if search_term == "fms_role"
          search_term = "fms_roles.name"
        elsif search_term == "kpi_scheme"
          search_term = "performance_schemes.name"
        end
        search_string << "#{search_term} ILIKE :search"
      end

      users = @query.order("#{sort_column} #{sort_direction}")
      users = users.page(page).per(per_page)
      users.where(search_string.join(" or "), search: "%#{params[:search][:value]}%")
    end

    def columns
      %w{email fms_role kpi_scheme updated_at}
    end
end
