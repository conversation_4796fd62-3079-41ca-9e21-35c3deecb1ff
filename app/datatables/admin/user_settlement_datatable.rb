class Admin::UserSettlementDatatable < ApplicationDatatable
  include ActionView::Helpers::NumberHelper

  delegate :admin_user_settlements_path, to: :@view

  private

    def data
      user_settlements.map do |user|
        balances = UserBalanceCalculator.new(user.email, Date.today)

        [].tap do |column|
          column << user.email
          column << number_with_delimiter(balances.net_balance)
          column << number_with_delimiter(balances.outstanding_cod_balance)
          column << number_with_delimiter(balances.requested_and_approved_cod)
          column << number_with_delimiter(balances.cash_float_balance)
          column << number_with_delimiter(balances.fleet_handover_balance)
          column << number_with_delimiter(balances.tpl_payment_balance)
          column << number_with_delimiter(balances.refund_balance)
          column << number_with_delimiter(balances.third_party_handover_balance)
          column << number_with_delimiter(balances.uncollected_balance)
          column << number_with_delimiter(balances.balance_correction_cod)
        end
      end
    end

    def count
      @query.count
    end

    def total_entries
      user_settlements.total_count
    end

    def user_settlements
      @user_settlements ||= fetch_user_settlements
    end

    def fetch_user_settlements
      user_settlements = @query.order("#{sort_column}": "#{sort_direction}")
      user_settlements = user_settlements.page(page).per(per_page)

      # Handle column search
      search_keyword = params.dig("search", "value") # Safely get params[:search][:value]
      if search_keyword.present?
        search_string = []
        columns.each do |col|
          search_string << "#{col} ILIKE :search"
        end
        user_settlements = user_settlements.where(search_string.join(" or "), search: "%#{search_keyword}%")
      end

      user_settlements
    end

    def columns
      %w{email}
    end
end
