class Admin::SettlementLogsDatatable < ApplicationDatatable
  include ActionView::Helpers::NumberHelper

  delegate :current_country, to: :@view
  delegate :guess_status_color, to: :@view
  delegate :admin_settlement_log_path, to: :@view
  delegate :approve_settlement_admin_settlement_log_path, to: :@view
  delegate :reject_settlement_admin_settlement_log_path, to: :@view

  private

    def data
      fleet_net_balances = generate_fleet_net_balances(settlement_logs)
      current_currency = current_country.currency

      settlement_logs.map do |log|
        [].tap do |column|
          # column Log ID
          column << log.log_id
          # column Amount
          column << number_to_currency(log.amount, current_currency)
          # column Nett Balance
          column << number_to_currency(fleet_net_balances[log.fleet_email].net_balance, current_currency)
          # column Created At
          column << log.created_at.localtime.strftime("%d %b %Y, %H:%M")
          # column Processed By
          column << log.processed_by
          # column Status
          column << tag.span(
            I18n.t("admin.settlement_logs.index.#{log.action}"),
            class: "badge badge-pill badge-#{guess_status_color(log.action)}"
          )
          # column View
          column << (
            tag.button(
              tag.span(class: "fas fa-link"),
              class: "btn btn-secondary btn-xs attachmentButton mr-1",
              type: "button",
              data: {
                "toggle" => "tooltip",
                "placement" => "left",
                "title" => "#{log.log_id} Attachment",
                "original-title" => "View attachment",
                "attachment-url" => log.attachment.url
              }
            ) +
            link_to(
              tag.span(class: "fas fa-eye"),
              admin_settlement_log_path(log.id),
              class: "btn btn-secondary btn-xs",
                "data-toggle" => "tooltip",
                "data-placement" => "left",
                "data-original-title" => "Open detail page",
              )
            )
          # column Actions
          if status_requested? # only show column if status = REQUESTED
            column << (
              tag.button(
                tag.span(class: "fas fa-check"),
                class: "approveButton btn btn-success btn-xs mr-1",
                data: {
                  "toggle" => "tooltip",
                  "placement" => "left",
                  "original-title" => "Approve",
                  "confirm-text" => I18n.t("admin.settlement_logs.show.approve_confirmation"),
                  "url" => approve_settlement_admin_settlement_log_path(log.id),
                  "log-id" => log.log_id,
                }
              ) +
              tag.button(
                tag.span(class: "fas fa-times"),
                class: "rejectButton btn btn-danger btn-xs",
                data: {
                  "toggle" => "tooltip",
                  "placement" => "left",
                  "original-title" => "Reject",
                  "url" => reject_settlement_admin_settlement_log_path(log.id),
                  "log-id" => log.log_id,
                }
              )
            )
          else
            column << "" # dummy data to avoid error in datatable
          end
        end
      end
    end

    def count
      latest_settlement_logs_query.size
    end

    def total_entries
      settlement_logs.total_count
    end

    def settlement_logs
      @settlement_logs ||= fetch_settlement_logs
    end

    def fetch_settlement_logs
      query = latest_settlement_logs_query
      query = build_search_query(query)
      query = build_sorting_query(query)
      query = query.page(page).per(per_page)
      query.load
    end

    def latest_settlement_logs_query
      user_join = <<-SQL.squish
        LEFT JOIN (SELECT id, email FROM users) _u
        ON _u.id = settlement_logs.created_by
      SQL

      log_ids_not_requested =
        SettlementLog.where(action: [2, 3, 4])
          .where("created_at >= ?", @options[:start_date])
          .select(:log_id).distinct

      updated_logs_join = <<-SQL.squish
        LEFT JOIN (
          #{log_ids_not_requested.to_sql}
        ) _lsl ON _lsl.log_id = _logs.log_id
      SQL

      settlement_query =
        if status_requested?
          SettlementLog.from(
            SettlementLog.from(@query, "_logs")
              .joins(updated_logs_join).where("_lsl.log_id IS NULL")
              .select("_logs.*"),
            :settlement_logs
          )
        else
          SettlementLog.from(@query, :settlement_logs)
        end

      settlement_query.joins(user_join).select("settlement_logs.*", "_u.email as processed_by")
    end

    def build_search_query(base_query)
      search_keyword = params.dig("search", "value")

      if search_keyword.present?
        search_columns = %w{settlement_logs.log_id _u.email}
        search_string = search_columns.map { |col| "#{col} ILIKE :search" }.join(" OR ")
        base_query = base_query.where(search_string, search: "%#{search_keyword}%")
      end

      base_query
    end

    def build_sorting_query(base_query)
      order_col_index = params.dig("order", "0", "column") # only support single column sorting

      case columns[order_col_index.to_i]
      when "log_id"
        base_query = base_query.order(log_id: sort_direction)
      when "amount"
        base_query = base_query.order(amount: sort_direction)
      when "processed_by"
        base_query = base_query.order("_u.email #{sort_direction}")
      when "status"
        base_query = base_query.order(action: sort_direction)
      when "created_at"
        base_query = base_query.order(created_at: sort_direction)
      else
        # default sort
        base_query = base_query.order(created_at: :desc)
      end

      base_query
    end

    def columns
      %w{log_id amount nett_balance created_at processed_by status}
    end

    def generate_fleet_net_balances(settlement_logs)
      fleet_emails = settlement_logs.map(&:fleet_email).uniq
      fleet_net_balances = UserBalanceCalculator.aggregate(fleet_emails, Date.today)

      fleet_net_balances
    end

    def status_requested?
      @options[:status].to_s == "1"
    end
end
