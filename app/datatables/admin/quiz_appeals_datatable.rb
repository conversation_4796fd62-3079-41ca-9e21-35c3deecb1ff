class Admin::QuizAppealsDatatable < ApplicationDatatable
  delegate :get_tz, to: :@view
  delegate :guess_status_color, to: :@view
  delegate :admin_approve_quiz_appeal_path, to: :@view
  delegate :admin_reject_quiz_appeal_path, to: :@view

  private

    def data
      quiz_appeals.map do |appeal|
        [].tap do |column|
          column << appeal.log_id
          column << appeal.track_name
          column << appeal.quiz_name
          column << appeal.requester_email
          column << appeal.created_at.in_time_zone(get_tz).strftime("%e %b %Y")
          column << tag.span(
            I18n.t("status.#{appeal.status}"),
            class: "badge badge-pill badge-#{guess_status_color(appeal.status)}"
          )
          if appeal.requested?
            approve_button = link_to(admin_approve_quiz_appeal_path(appeal.id), method: :post, class: "btn btn-success btn-xs mr-1 mb-1", data: tooltip_data("Approve")) do
                tag.span(class: "fas fa-check")
              end

            reject_button = link_to(admin_reject_quiz_appeal_path(appeal.id), method: :post, class: "btn btn-danger btn-xs mb-1", data: tooltip_data("Reject")) do
                tag.span(class: "fas fa-times")
              end

            column << (approve_button + reject_button)
          else
            column << " " # need a character to show action column
          end
        end
      end
    end

    def tooltip_data(title)
      {
        "toggle" => "tooltip",
        "placement" => "left",
        "original-title" => title
      }
    end

    def count
      @query.count
    end

    def total_entries
      quiz_appeals.total_count
    end

    def quiz_appeals
      @cache_var ||= fetch_from_db
    end

    def fetch_from_db
      query = build_base_query
      query = build_search_query(query)
      query = build_sorting_query(query)

      query.page(page).per(per_page)
    end

    def build_base_query
      requested_by_join = <<-SQL.squish
        INNER JOIN users ON users.id = quiz_attempt_appeals.requested_by
      SQL

      training_module_join = <<-SQL.squish
        INNER JOIN training_modules
        ON training_modules.discarded_at IS NULL AND training_modules.id = quizzes.training_module_id
      SQL

      training_type_join = <<-SQL.squish
        INNER JOIN training_types
        ON training_types.discarded_at IS NULL AND training_types.id = training_modules.training_type_id
      SQL

      @query.select(
          "quiz_attempt_appeals.*",
          "users.email as requester_email",
          "quizzes.name as quiz_name",
          "training_types.name as track_name"
        )
        .joins(requested_by_join)
        .joins(:quiz)
        .joins(training_module_join)
        .joins(training_type_join)
    end

    def build_search_query(base_query)
      search_keyword = params.dig("search", "value")

      if search_keyword.present?
        searchable_columns = ["log_id", "users.email", "quizzes.name", "training_types.name"]
        search_string = searchable_columns.map { |col| "#{col} ILIKE :search" }.join(" OR ")
        base_query = base_query.where(search_string, search: "%#{search_keyword}%")
      end

      base_query
    end

    def columns
      %w{log_id track_name quiz_name requested_by created_at status}
    end

    def build_sorting_query(base_query)
      seq = 0

      while (order_col_index = params.dig("order", seq.to_s, "column")).present? do
        column_sort_direction = sort_direction(seq)

        case columns[order_col_index.to_i]
        when "log_id"
          base_query = base_query.order(log_id: column_sort_direction)
        when "track_name"
          base_query = base_query.order("training_types.name #{column_sort_direction}")
        when "quiz_name"
          base_query = base_query.order("quizzes.name #{column_sort_direction}")
        when "requested_by"
          base_query = base_query.order("users.email #{column_sort_direction}")
        when "created_at"
          base_query = base_query.order(created_at: column_sort_direction)
        when "status"
          base_query = base_query.order(status: column_sort_direction)
        else
          # do nothing
        end

        seq += 1
      end

      base_query
    end
end
