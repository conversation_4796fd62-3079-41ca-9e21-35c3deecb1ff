class Admin::UserInventoriesDatatable < ApplicationDatatable
  include ActionView::Helpers::NumberHelper

  delegate :admin_user_inventory_path, to: :@view

  private

    def data
      user_inventories.map do |user|
        [].tap do |column|
          # name
          column << user.full_name
          # inventories
          column << (user[:string_agg] || "-")
          # last update
          column << (user[:last_update].nil? ? "-" : "#{user[:last_update].strftime('%d %b %Y, %H:%M')} GMT+00")
          # actions
          column << link_to(admin_user_inventory_path(user.slug), class: %w{btn btn-sm btn-primary}) do
                    tag.span(class: %w{fas fa-eye}) + " " + I18n.t("actions.view")
                  end
        end
      end
    end

    def count
      @query.size
    end

    def total_entries
      User.from(user_inventories(false)).size
    end

    def user_inventories(paginated = true)
      @cache_var ||= fetch_from_db
      if paginated
        @cache_var.page(page).per(per_page)
      else
        @cache_var
      end
    end

    def fetch_from_db
      base_query = build_base_query(@query)
      search_results_query = build_search_query(base_query)
      sorted_query = build_sorting_query(search_results_query)

      sorted_query
    end

    def build_base_query(query)
      inventories_users = <<-SQL
        LEFT JOIN inventories_users iu ON iu.user_id = users.id
      SQL

      inventories = <<-SQL
        LEFT JOIN inventories i ON iu.inventory_id = i.id
      SQL

      life_cycle_latest_time = <<-SQL
        LEFT JOIN (
          SELECT
            DISTINCT ON (ilc.user_id) ilc.user_id as u_id,
            ilc.life_cycle_phase as phase,
            ilc.updated_at as last_update
          FROM inventory_life_cycles ilc
          ORDER BY
            ilc.user_id,
            ilc.updated_at DESC
        ) life_cycle_latest_time ON life_cycle_latest_time.u_id = users.id
      SQL

      @query.select(
        "users.first_name",
        "users.last_name",
        "string_agg(i.name, ', ')",
        "life_cycle_latest_time.last_update",
        "users.email",
        "users.slug")
        .joins(inventories_users)
        .joins(inventories)
        .joins(life_cycle_latest_time)
        .group("users.email, users.slug, users.first_name, users.last_name, life_cycle_latest_time.last_update")
    end

    def build_search_query(query)
      search_keyword = params.dig("search", "value") # Safely get params[:search][:value]
      if search_keyword.present?
        query = query.having(construct_search_string, search: "%#{search_keyword}%")
      end
      query
    end

    def construct_search_string
      searchable_db_columns = [
        "CONCAT(first_name, ' ', last_name)", # full name search
        "string_agg(i.name, ', ')" # inventories
      ]
      searchable_db_columns.map { |col| "#{col} ILIKE :search" }.join(" OR ")
    end

    def columns
      %w{name inventories last_update actions}
    end

    def build_sorting_query(query)
      order_col_index = params.dig("order", "0", "column") # only support single column sorting

      case columns[order_col_index.to_i]
      when "name"
        query = query.order(first_name: "#{sort_direction}")
      when "inventories"
        query = query.order("string_agg #{sort_direction}")
      when "last_update"
        query = query.order("last_update #{sort_direction}")
      else
        # do nothing
      end

      query
    end
end
