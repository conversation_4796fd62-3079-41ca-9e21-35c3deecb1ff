class Admin::UserMeritStrikesDatatable < ApplicationDatatable
  include ActionView::Helpers::NumberHelper

  delegate :admin_new_user_merit_path, to: :@view
  delegate :admin_user_merit_logs_path, to: :@view
  delegate :admin_new_user_strike_path, to: :@view
  delegate :admin_user_strike_logs_path, to: :@view

  private

    def data
      user_merit_strikes.map do |user|
        [].tap do |column|
          column << user.email
          column << user.merit_point
          column << link_to(I18n.t("admin.user_merit_strikes.index.add"), admin_new_user_merit_path(user), class: %w{btn btn-secondary btn-xs}) # merit_add
          column << link_to(I18n.t("admin.user_merit_strikes.index.log"), admin_user_merit_logs_path(user), class: %w{btn btn-secondary btn-xs}) # merit_log
          column << user.strike_point
          column << link_to(I18n.t("admin.user_merit_strikes.index.add"), admin_new_user_strike_path(user), class: %w{btn btn-secondary btn-xs}) # strike_add
          column << link_to(I18n.t("admin.user_merit_strikes.index.log"), admin_user_strike_logs_path(user), class: %w{btn btn-secondary btn-xs}) # strike_log

          safe_status_str = I18n.t("admin.user_merit_strikes.index.safe")
          dangerous_status_str = I18n.t("admin.user_merit_strikes.index.dangerous")
          safe_status_tag = tag.span(safe_status_str, class: %w{badge badge-pill badge-success})
          dangerous_status_tag = tag.span(dangerous_status_str, class: %w{badge badge-pill badge-danger})
          column << ((user.merit_strike_status == dangerous_status_str) ? dangerous_status_tag : safe_status_tag)
        end
      end
    end

    def count
      @query.count
    end

    def total_entries
      user_merit_strikes.total_count
    end

    def user_merit_strikes
      @cache_var ||= fetch_from_db
    end

    def fetch_from_db
      merit_strikes = build_sorting_query(@query)
      merit_strikes = merit_strikes.page(page).per(per_page)
      # Handle search
      search_keyword = params.dig("search", "value") # Safely get params[:search][:value]
      if search_keyword.present?
        merit_strikes = merit_strikes.where(construct_search_string, search: "%#{search_keyword}%")
      end

      merit_strikes
    end

    def columns
      %w{email merit_point merit_action merit_log 3_strike_point 3_strike_action 3_strike_log status}
    end

    def construct_search_string
      searchable_db_columns = ["email"] # email search only, other colums are not searchable
      searchable_db_columns.map { |col| "#{col} ILIKE :search" }.join(" or ")
    end

    def build_sorting_query(base_query)
      order_col_index = params.dig("order", "0", "column") # only support single column sorting

      case columns[order_col_index.to_i]
      when "email"
        base_query = base_query.order(email: "#{sort_direction}")
      when "merit_point"
        base_query = base_query.order(merit_point: "#{sort_direction}")
      when "3_strike_point"
        base_query = base_query.order(strike_point: "#{sort_direction}")
      when "status"
        minimum_threshold = UserStrike.dangerous_status_minimum
        base_query = base_query.order("strike_point >= #{minimum_threshold} #{sort_direction}") # workaround, there's no status column on users table.
      else
        base_query = base_query.order(first_name: "#{sort_direction}") # default
      end

      base_query
    end
end
