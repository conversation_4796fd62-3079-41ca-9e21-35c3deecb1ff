class Admin::CardsDatatable < ApplicationDatatable
  include ActionView::Helpers::NumberHelper

  delegate :edit_admin_card_path, to: :@view
  delegate :current_country, to: :@view
  delegate :get_tz, to: :@view

  private

    def data
      country_currency = current_country.currency

      paginated_rows.map do |card|
        [].tap do |column|
          column << card.card_number_with_separator
          column << card.name_on_card
          column << card.valid_until
          column << number_to_currency(card.transaction_limit, country_currency)
          column << number_to_currency(card.monthly_limit, country_currency)

          column << (card.llc_assigned_to.nil? ? "-" : card.llc_assigned_to)
          column << (card.llc_assigned_at.nil? ? "-" : card.llc_assigned_at.in_time_zone(get_tz).strftime("%d %b %Y, %H:%M"))

          html_active = tag.span(I18n.t("admin.cards.index.active"), class: "badge badge-pill badge-success")
          html_inactive = tag.span(I18n.t("admin.cards.index.inactive"), class: "badge badge-pill badge-dark")
          column << (card.discarded? ? html_inactive : html_active)
          column << link_to(edit_admin_card_path(card)) { tag.span(class: %w{fas fa-pencil-alt}) }
        end
      end
    end

    # total records before filtering
    def count
      @query.size
    end

    # total records after filtering
    def total_entries
      paginated_rows.total_count
    end

    def paginated_rows
      @cached_paginated_rows ||= fetch_paginated_rows # memoization
    end

    def fetch_paginated_rows
      latest_life_cycle_join = <<-SQL
        LEFT JOIN (
          SELECT DISTINCT ON (inventory_id) inventory_id, user_id, life_cycle_phase AS phase, created_at
          FROM inventory_life_cycles ilc
          ORDER BY inventory_id, created_at DESC
        ) _llc ON (_llc.inventory_id = inventories.id AND _llc.phase = 1)
      SQL

      user_join = <<-SQL
        LEFT JOIN users _u ON (_u.id = _llc.user_id AND _llc.phase = 1)
      SQL

      query = @query.select(
          "_u.email as llc_assigned_to",
          "_llc.created_at as llc_assigned_at",
          "inventories.*")
        .joins(latest_life_cycle_join)
        .joins(user_join)
      query = build_search_query(query)
      query = build_sorting_query(query)
      query.page(page).per(per_page) # paginate using kaminari
    end

    def columns
      %w{number name_on_card valid_until transaction_limit monthly_limit assigned_to assigned_at status}
    end

    def build_search_query(base_query)
      search_keyword = params.dig("search", "value") # Safely get params[:search][:value]
      if search_keyword.present?
        hstore_columns = %w{bin last_digits name_on_card}
        hstore_where_string = hstore_columns.map { |col| "custom_properties -> '#{col}' ILIKE :search" }.join(" or ")

        base_query = base_query.where(hstore_where_string, search: "%#{search_keyword}%")
          .or(base_query.where("_u.email ilike :search", search: "%#{search_keyword}%"))
      end

      base_query
    end

    def build_sorting_query(base_query)
      order_col_index = params.dig("order", "0", "column") # only support single column sorting

      case columns[order_col_index.to_i]
      when "number"
        base_query = base_query.order(Arel.sql("custom_properties -> 'bin' #{sort_direction}, custom_properties -> 'last_digits' #{sort_direction}"))
      when "name_on_card"
        base_query = base_query.order(Arel.sql("custom_properties -> 'name_on_card' #{sort_direction}"))
      when "valid_until"
        base_query = base_query.order(Arel.sql("custom_properties -> 'valid_until_year' #{sort_direction}, custom_properties -> 'valid_until_month' #{sort_direction}"))
      when "transaction_limit"
        base_query = base_query.order(Arel.sql("(custom_properties -> 'transaction_limit')::int #{sort_direction}"))
      when "monthly_limit"
        base_query = base_query.order(Arel.sql("(custom_properties -> 'monthly_limit')::int #{sort_direction}"))
      when "assigned_to"
        base_query = base_query.order(Arel.sql("_u.email #{sort_direction}"))
      when "assigned_at"
        base_query = base_query.order(Arel.sql("_llc.created_at #{sort_direction}"))
      when "status"
        base_query = base_query.order(Arel.sql("discarded_at #{sort_direction}"))
      else
        # default do nothing
      end

      base_query
    end
end
