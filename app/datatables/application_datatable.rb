class ApplicationDatatable
  delegate :params, to: :@view
  delegate :link_to, to: :@view
  delegate :time_ago_in_words, to: :@view
  delegate :local_time, to: :@view
  delegate :tag, to: :@view

  def initialize(view, query, options = {})
    @view = view
    @query = query
    @options = options
  end

  def as_json(option = {})
    {
      recordsTotal: count, # total records before filtering
      recordsFiltered: total_entries, # total records after filtering
      data: data # actual data in Array
    }
  end

  private
    def page
      params[:start].to_i / per_page.to_i + 1
    end

    def per_page
      params[:length].present? ? params[:length].to_i : 10
    end

    def sort_column
      params[:order].present? ? columns[params[:order]["0"][:column].to_i] : columns[0]
    end

    def sort_direction(seq = 0)
      if params.dig(:order, seq.to_s, :dir) == "desc"
        "desc"
      else
        "asc"
      end
    end
end
