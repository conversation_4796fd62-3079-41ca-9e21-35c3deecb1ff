$settlement-tosca: #3BA4B4;
$settlement-black: #3E4148;
$settlement-red: #F04237;
$settlement-green: #8CC540;
$settlement-light-orange: #FEEBD2;
$settlement-light-gray: #F5F5F7;
$settlement-light-black: #CED1D9;
$settlement-orange: #F45A1B;
$settlement-gray: #9DA3B4;
$settlement-dark-gray: #6D727D;
$settlement-white: #FFFFFF;

#cash-settlement-home {

  color: $settlement-black;

  .card {

    border: none;

    .card-header {

      font-size: 16px;

      &.balance-header {
        background-color: $settlement-tosca;
        font-weight: 600;
      }

      &.balance-information {
        background-color: $settlement-light-orange;
        font-size: 10px;
        border-color: $settlement-light-orange;
      }

      &.title { font-weight: 600; }
    }

    .card-body {
      .current-balance {
        font-size: 28px;
        font-weight: 700;
      }

      .balance-separator, .cod-separator {
        border: 1px solid $settlement-light-gray;
        margin: 0;
      }

      .cod-sum-up { font-size: 14px; }

      .cod-details { font-size: 11px; }

      .last-update { font-size: 10px; }

      .cod-collected-number {
        color: $settlement-red;
        font-weight: 700;
      }

      .cod-settled-number {
        color: $settlement-green;
        font-weight: 700;
      }

      .cod-balance-number { font-weight: 700; }
    }
  }
}

#create-cash-settlement {

  color: $settlement-black;

  .card, .card-shadow { border-radius: 0; }

  .control-label { font-size: 14px; }

  .form-control { font-size: 12px; }

  .form-text { font-size: 10px; }

  input::placeholder, textarea::placeholder { color: $settlement-gray; }

  textarea { height: 76px; }

  .bottom-space {
    background-color: $settlement-white;
    height: 140px;
  }
}

#my-settlement-details {

  color: $settlement-black;

  .log_id { font-size: 16px; }

  tr {
    height: 25px;
  }

  .status {

    &-label {
      font-size: 11px;
      font-weight: bold;
      width: 36%;
    }

    &-value {
      font-size: 11px;
      width: 64%;
    }
  }

  hr .header-separator {
    border: 1px solid $settlement-light-gray;
    margin: 0;
    border-top: none;
  }

  .bottom-space {
    background-color: $settlement-white;
    height: 40px;
  }
}

#my-settlements {
  .card {
    margin-bottom: 0.75rem;
  }

  .customized-select .fas {
    right: 15px;
  }
}

#my-order-index {
  .remark {
    font-weight: 500;
    font-size: 10px;
    color: $settlement-dark-gray;
  }
}

#my-order-details {
  color: $settlement-black;

  hr .header-separator {
    border: 1px solid $settlement-light-gray;
    margin: 0;
    border-top: none;
  }

  .order-number { font-size: 16px; }

  .pending-total { font-size: 28px; }

  .card { box-shadow: none; }
}
