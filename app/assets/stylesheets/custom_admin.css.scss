// shared styles to make FMS style consistent
$admin-light-grey: #6D727D;
$admin-grey: #3E4148;
$admin-red: #F04237;
$admin-white: #ffffff;
$admin-green: #8CC540;
$admin-orange: #F45A1B;

.custom-admin {

  h3, h4 { color: $admin-grey }

  .form-detail {
    margin-bottom: 5px;
    margin-top: 5px;

    .value {
      color: $admin-grey;
      display: inline-block;
    }

    .label {
      color: $admin-light-grey;
      font-weight: 700;
      width: 150px;
      display: inline-block;
    }
  }

  .table {
    tbody {
      tr {
        &.settlement_adjustment_logs {
          td {
            &.status {
              p {
                max-width: 180px;
                font-size: 12px;
                font-style: italic;
              }
              &.failed {
                p {
                  color: $admin-red !important;
                }
                .badge {
                  background-color: $admin-red;
                }
              }
              &.successful {
                .badge {
                  background-color: $admin-green;
                  pointer-events: none;
                }
              }
              .badge {
                background-color: $admin-light-grey;
                color: $admin-white;
              }
            }
            &.actions {
              .error-message-popup {
                display: none;
              }
              &.action-failed {
                .error-message-popup {
                  display: inherit;
                }
              }
            }
          }
        }
      }
    }
  }
}

.btn {
  /* button acts as unclickable object without sacrificing pointer-events */
  &.hover-only {
    &:hover, &:focus {
      box-shadow: none;
    }
  }
  &.btn-transparent {
    border: solid 1px transparent;
    background-color: transparent;
  }
  &.btn-primary-orange {
    background-color: $admin-orange;
    color: $admin-white;
  }
  &.btn-outline-orange {
    background-color: transparent;
    border: solid 1px $admin-orange;
    color: $admin-orange;
  }
}

.reason {
  &.others {
    .note {
      font-size: small;
    }
  }
}

form {
  .form-group {
    .field-hint-row {
      small {
        &::before {
          content: "* ";
        }
      }
    }
  }
}
