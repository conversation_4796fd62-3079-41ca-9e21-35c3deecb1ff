.horizontal-timeline {
  margin: 0;
  padding: 0;
  list-style-type: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ht-status {
  margin-bottom: 20px;
  padding: 0px 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.ht-timestamp {
  text-align: center;
  padding: 15px 0px;
  border-top: 2px solid #D6DCE0;
  position: relative;
  font-weight: 200;
}

.ht-timestamp:before {
  content: "";
  width: 23px;
  height: 23px;
  background-color: white;
  border-radius: 25px;
  border: 1px solid #ddd;
  position: absolute;
  top: -15px;
  left: 42%;
}

.ht-li.complete .ht-timestamp {
  border-top: 2px solid #D6DCE0;
}
.ht-li.complete .ht-timestamp:before {
  background-color: #6e6e6e;
  border: none;
}
.ht-li.complete-late .ht-timestamp:before {
  background-color: #b75353;
  border: none;
}
.ht-li.complete .ht-timestamp h4 {
  color: #66DC71;
}

.ht-label {
  padding-top:32px; 
  padding-right: 0; 
  padding-left: 0;
}

.pgb-label {
  padding-right: 0; 
  padding-left: 0;
}

.pgb-container {
  height: 25px;
}

@media (min-device-width: 320px) and (max-device-width: 700px) {
  .horizontal-timeline {
    list-style-type: none;
    display: block;
  }

  .ht-li {
    display: flex;
    width: inherit;
  }

  .ht-status {
    width: 100px;
  }

  .ht-timestamp:before {
    left: -8%;
    top: 30%;
  }
}
