$green: #9acb3c;
$red: #ea0000;
$lightblue: #00a7b6;
$orange: #f15b29;
$tosca: #3BA4B4;
$training-module-disabled: #CED1D9;
$training-gray: #CED1D9;
$training-search-btn-hover: #F5F5F7;
$training-search-box-field-border: #3E4148;
$training-search-list-item-black: #3E4148;
$training-search-list-item-gray: #9DA3B4;

a.text-link {
  color: $lightblue;
  font-weight: bold;
  text-decoration: underline;
}

// training type page
.training-type-image {
  width: 100%;
  max-width: 100px;
  max-height: 100px;
  height: auto;
  border-radius: 0.25rem;
}

// training module page
ul.timeline {
  list-style-type: none;
  position: relative;
}

ul.timeline:before {
  content: " ";
  background: #d4d9df;
  display: inline-block;
  position: absolute;
  left: 18px;
  width: 1px;
  height: 90%;
  z-index: 100;
}

ul.timeline li {
  margin: 20px 0;
  padding-left: 20px;
  border-bottom: dashed 1px #d4d9df;
}

ul.timeline li:before {
  content: " ";
  background: white;
  display: inline-block;
  position: absolute;
  border-radius: 50%;
  border: 1px solid #d4d9df;
  left: 8px;
  width: 20px;
  height: 20px;
  z-index: 100;
}

ul.timeline p:after {
  content: ">";
  position: absolute;
  right: 1em;
}

ul.timeline li.timeline-success::before {
  background: #27c24c;
  border: 1px solid #27c24c;
}

ul.timeline li .timeline-badge.primary {
  position: absolute;
  left: 12px;
  margin-top: 2px;
  font-size: 0.8em;
  text-align: center;
  z-index: 200;
  color: #fff;
}

ul.timeline li.timeline-end {
  float: none;
  clear: both;
  border: none;
}

// training submodule content page
.card.content-body-container {
  margin: 10px -15px;
}

p.caption {
  font-size: 0.8rem;
}

.btn.btn-success.btn-filled-next {
  background-color: $green;
  border-color: $green;
  margin-left: 4px;
}

.btn-outline-prev {
  color: $green;
  border-color: $green;
  margin-right: 4px;
}

// quiz question page
.btn-filled-submit {
  background-color: #f15b29;
  border-color: #f15b29;
  margin: 20px 0;
  width: 50%;
  color: #fff;
  font-size: 18px;
  font-weight: bold;
}

.quiz-question {
  padding: 20px 0;
}

.radiocontainer {
  border: 1px solid #ddd;
  border-radius: 4px;
  display: block;
  position: relative;
  padding: 10px 20px;
  margin-bottom: 10px;
  color: #909fa7;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  word-wrap: break-word;

  &:hover:not(.answered) {
    border: 1px solid $green;
    background-color: #f3f9eb;
    color: #656565;
  }

  &.answered {
    cursor: default;
  }
}

.radiocontainer input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.checked-label {
  border: 1px solid $green;
  background-color: #f3f9eb;
  color: #656565;

  &.wrong {
    border: 1px solid $red;
    background-color: #fcf1f1;
  }
}

.quiz-check {
  .fa-check {
    color: $green;
  }

  .fa-times {
    color: $red;
  }
}

img.training-profile-image {
  width: 50px;
  height: 50px;
}

p.profile-name {
  margin-bottom: 0;
  font-weight: bold;
}

p.profile-text {
  margin-bottom: 0;
}

.achievement-container {
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.12);
}

ul.achievements-list {
  list-style-type: none;
  position: relative;
  padding: 0;
}

ul.achievements-list > li {
  border-bottom: dashed 1px #d4d9df;
}

ul.achievements-list > li.achievements-list-end {
  border: none;
  margin-bottom: -20px;
}

.quiz-result {
  text-align: center;

  img {
    margin-bottom: 1em;
  }
}

.quiz-time-limit {
  background-color: $tosca;
  color: #fff;
  font-weight: bold;
  padding: 0.25em 0.6em;
  border-radius: 2.67px;
  font-size: 1.1em;
}

.quiz-time-duration {
  color: $tosca;
  font-weight: bold;
  font-size: 1.5em;
}

.quiz-attempt {
  color: #333333;
}

.quiz-attempt-zero {
  color: #F04237;
}

.quiz-information {
  .module-name {
    font-weight: bold;
    font-size: 1.1em;
  }
}

#quiz-time-up-modal {
  .modal-dialog {
    top: 50%;
    transform: translateY(-50%);
  }

  .center {
    text-align: center;
  }
}

#show-quiz {
  .btn-warning.disabled {
    cursor: initial;
    color: $training-gray;
    background-color: #F5F5F7;
    border-color: #F5F5F7;
  }
}

.box .chart {
  position: relative;
  margin: 0 auto;
  text-align: center;
  width: 56px;
  height: 56px;
  line-height: 56px;
  font-size: 16px;
  color: #7CB136;
  font-weight: 800;
}

.box canvas {
  position: absolute;
  top: 0;
  left: 0;
}

.card-disable {
  pointer-events: none;
  opacity: 0.4;
}

.progressing-module {
  &.locked {
    position: relative;
    background-color: $training-module-disabled;
    opacity: 0.4;
  }
}
.ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.next-module {
  font-weight:400;
  margin-bottom:0.1rem;
  font-size: 0.75rem;
}

.module-title {
  font-size: 0.875rem;
  font-weight: 800;
}

.nav-title {
  font-weight:700;
}

.module-active {
  color: #F15B2A;
}

.module-non-active {
  color: $training-gray;
}

.toast-module-locked {
  max-width: 500px;
  border-radius: 0.396rem;
  background-color: #3E4148;
  color: white;
  position: absolute;
  top: 0.2rem;
  right: 0.1rem;
}

.close-toast {
  color: #7CB136;
  font-weight: 700;
  text-decoration: none;
}

.close-toast:hover {
  color: #ffff;
  text-decoration:none;
}

.training-view {
  .card {
    &.progressing-module {
      margin-bottom: 0 !important;
    }
  }

  .card-line {
    height: 32px;
    border-left: solid 1px $training-gray;
    margin-left: 3rem;
  }
}

.btn-nav {
  border: none;
  background-color: none;
}

.link-wrapper {
  &.btn-circle {
    background-color: $training-gray;
    &:hover {
      background-color: $training-search-btn-hover;
    }
  }
}

.search-box {
  .search-input-wrapper {
    background-color: white;
    border: 1px solid #ced4da;
    border-radius: .25rem;
    padding: 0;
    height: auto;
    border-color: $training-search-box-field-border;
    .form-control {
      margin: 0;
      padding: 0;
      border-width: 0px;
    }
  }
}

.search-result {
  .list-item {
    .list-item-icon {
      margin-right: 16px;
      .lock-icon {
        display: none;
      }
      .search-icon {
        display: inherit;
      }
    }
    .list-item-content {
      line-height: 16px;
      font-size: 12px;
      font-weight: 500;
      color: $training-search-list-item-gray;
      small, strong {
        font-family: "Montserrat", "Open Sans", Helvetica, Arial, sans-serif;
      }
      strong {
        line-height: 20px;
        font-size: 14px;
        font-weight: 600;
        color: $training-search-list-item-black;
        margin-bottom: 4px;
      }
      small {
        &.source-type {
          margin-right: 4px;
        }
        &.module-submodule-name {
          margin-left: 4px;
        }
      }
    }
    .list-item-nav {
      .next-icon {
        &.unlocked {
          display: inherit;
        }
        &.locked {
          display: none;
        }
      }
    }
    &.locked {
      .list-item-icon {
        .lock-icon {
          display: inherit;
        }
        .search-icon {
          display: none;
        }
      }
      .list-item-content {
        strong { color: $training-search-list-item-gray; }
      }
      .list-item-nav {
        .next-icon {
          &.locked {
            display: inherit;
          }
          &.unlocked {
            display: none;
          }
        }
      }
    }
  }
  .search-result-hint {
    .card-body {
      border-radius: 0.25rem !important;
      padding-right: 2px;
    }
    .text-hint {
      border-top-right-radius: 0.25rem;
      border-bottom-right-radius: 0.25rem;
      border-color: transparent;
      font-family: "Montserrat", "Open Sans", Helvetica, Arial, sans-serif;
      font-weight: 500;
      font-size: 12px;
      line-height: 16px;
    }
  }
}
