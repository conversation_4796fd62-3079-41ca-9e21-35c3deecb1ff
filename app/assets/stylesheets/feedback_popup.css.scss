$feedback-orange: #F45A1B;
$feedback-light-orange: #FDEEE9;
$feedback-white: #FFFFFF;
$feedback-black: #3E4148;
$feedback-dark-grey: #6D727D;
$feedback-grey: #9DA3B4;
$feedback-light-grey: #CED1D9;

.feedback-form {
  font-family: '<PERSON><PERSON><PERSON>', sans-serif;

  .close {
    position: absolute;
    color: $feedback-light-grey;
    float: left;
    opacity: 1;
    top: 90px;
    left: 470px;
    background-color: $feedback-white;
    border-radius: 50%;
    z-index: -1;
    width: 25px;
    height: 25px;

    .cross-icon {
      color: $feedback-dark-grey;
    }
  }

  .modal-dialog {
    top: 15%;
  }

  .outliner {
    padding: 8px;
    background-color: $feedback-white;
    border-radius: 12px;
  }

  .modal-content {
    background-color: $feedback-light-orange;
    border: none;
  }

  .header-container {
    position: relative;
    display: flex;
    justify-content: center;

    .header-img {
      position: absolute;
      top: -114px;
      width: 280px;
      height: 180px;
      background-position: top;
    }
  }

  .question {
    font-size: 20px;
    font-weight: 700;
    text-align: center;
    margin-top: 4rem;
    color: $feedback-black;
  }

  .thumbs-text {
    font-size: 16px;
    font-weight: 700;
    text-align: center;
    margin-top: 0.5rem;
    color: $feedback-grey;
  }

  .feedback-textarea { display: none; }

  textarea { resize: none; }

  .modal-footer {
    display: none;
    text-align: center;
    padding-top: 5px;
    border-top: none;

    .btn-primary {
      background-color: $feedback-orange;
      border: none;
      width: 201px;
      font-weight: bold;
    }
  }
}