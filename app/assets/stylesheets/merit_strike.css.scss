@import "angle/app/common/variables";

$merit-strike-tosca: #3BA4B4;
$merit-strike-orange: #F89A1F;
$merit-strike-dark-orange: #F45A1B;
$merit-strike-green: #79C913;
$merit-strike-red: #F04237;
$merit-strike-gray: #3E4148;
$merit-strike-white: #FFFFFF;
$merit-strike-black: #000000;

$ms-badge-bg-green: #E8F3D9;
$ms-badge-bg-red: #FDECEB;
$ms-badge-bg-gray: #CED1D9;
$ms-badge-bg-orange: #FEEBD2;

$ms-badge-font-green: #8CC540;
$ms-badge-font-orange: #FF8F2C;

.merit-strike {
  &.status-strike-snd {
    margin-top: -7px;
    margin-right: -5px;
    margin-bottom: 6px;
    display: flex;
    justify-content: flex-end;

    .badge {
      font-size: 0.7em;
    }
  }

  .badge {
    text-transform: uppercase;
    padding: 0.2em 1.25em;
    border-radius: 2px;
    letter-spacing: 0.25px;

    &.badge-success {
      background-color: $ms-badge-bg-green;
      color: $merit-strike-green;
    }

    &.badge-danger {
      background-color: $ms-badge-bg-red;
      color: $merit-strike-red;
    }

    &.badge-dark {
      background-color: $ms-badge-bg-gray;
      color: $merit-strike-gray;
    }
  }
}

#merit-strike-status {
  font-family: 'Montserrat', sans-serif;

  .card {
    color: #ffffff;

    &.merit {
      background-color: $merit-strike-tosca;
    }

    &.strike {
      background-color: $merit-strike-orange;
    }

    .icon {
      width: 120px;
    }

    a {
      color: #ffffff;
    }

    .card-title {
      margin-bottom: 0.1em;
      font-weight: 600;
    }

    .points {
      font-weight: 700;
    }

    .see-details {
      flex-grow: 1;
      font-size: 0.9em;
    }
  }
}

#strike-and-appeal-history {
  h5 {
    color: $merit-strike-gray;
  }

  tr {
    td:nth-child(2) {
      padding-left: 0;
    }
  }

  .btn {
    padding: 0.25em 1.5em;
  }

  .btn-secondary {
    border-color: #CED1D9;
    border-radius: 4px;
    border-width: 1px;
    font-weight: 500;
    font-size: 0.9em;
  }

  .badge {
    font-size: 0.65rem;
  }

  .card-default {
    border: none;
  }

  .title {
    font-size: 1.125rem;
    margin-bottom: 0.25em;
  }

  .penalty-points-info {
    background-color: $merit-strike-orange;
    color: white;
    border-radius: 4px;

    .merit-strike {
      margin-top: -3px;
    }
  }

  .strike-filter {
    select {
      width: 100%;
      padding: 0.5em;
      border: 1px solid #dde6e9;
    }
  }

  .datatable-strike {
    .strike-name {
      color: $merit-strike-red;
      font-weight: bold;
    }

    .point {
      font-weight: bold;
      color: $merit-strike-gray;

      &.approved {
        text-decoration: line-through;
      }
    }

    .badge {
      padding: 0.25em 0.5em;
    }

    .date-status {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      color: $merit-strike-gray
    }

    .given-by {
      word-break: break-word;
      color: $merit-strike-gray;
    }
  }
}

.display-merit-strike-snd {
  $orange: #F45A1B;
  $dark-green: #3BA4B4;
  $white: #FFFFFF;
  $black: #3E4148;
  $red: #F04237;

  font-family: "Montserrat", sans-serif;

  .btn-orange {
    background-color: $orange;
    border: none;
    color: $white;
    font-weight: 600;
    padding: 0.25em 1.5em;
  }

  .bg-dark-green {
    background-color: $dark-green;
  }

  .card-default {
    border-color: transparent;

    .dataTables_filter { display: none; }

    .page-item.active .page-link {
      background-color: $orange;
      border-color: $orange;
    }

    .merit-header {
      color: $white;
      font-size: 1.125rem;
      font-weight: bold;
    }

    .merit-subheader { color: $white; }

    .merit-name { color: $dark-green; }

    .merit-description {
      font-size: 0.9em;
    }

    .merit-label { font-size: 10pt; }

    .merit-redemption-name {
      color: $black;
      font-size: 12pt;
    }

    .merit-redemption-point {
      color: $red;
      text-align: center;
      margin-bottom: 0.25rem;
    }

    .flex-wrapper {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      align-items: flex-end;
      color: $black;
    }

    .flex-block {
      flex: 1;
      width: 48%;
    }

    .view-button { margin-top: 0.6rem }

    .col_2 { vertical-align: top; }

    .status-badge {
      border-radius: 2px;
      font-size: 9pt;
    }
  }
}

// Customization
.merit-redemptions-new {
  &.card {
    .card-body {
      padding: 12px 0;
    }
  }
  label {
    &.control-label {
      font-weight: 700;
      font-size: 12px;
    }
  }
  .button-wrapper {
    display: flex;
    flex-direction: row;
    padding-left: 15px;
    padding-right: 15px;
    @media only screen and (max-width: 575px) {
      &.form-button {
        justify-content: flex-end;
      }
    }
    @media only screen and (min-width: 576px) {
      &.form-button {
        justify-content: flex-start;
      }
    }
  }
  .form-group--inline {
    display: flex;
    flex-direction: row;
    .form-group {
      margin-bottom: 0;
    }
  }
  .form-group {
    small {
      &.form-text {
        font-weight: 500;
        color: $merit-strike-gray;
      }
      @media only screen and (max-width: 575px) {
        &.form-text {
          font-size: 7px;
        }
      }
    }
  }
}

.strike-appeals-form,
.merit-redemption-detail,
.strike-appeals-detail {
  .card-wrapper {
    display: flex;
    flex-direction: column;
    flex: 1 1 auto;
    background-color: $merit-strike-white;
    padding: 12px 12px 0 12px;

    .card-title {
      margin-bottom: 5px;
      font-weight: 700;
      font-size: 14px;
      line-height: 17px;
      color: $merit-strike-gray;
    }

    .card {
      margin-bottom: 16px;
    }
  }

  .card {
    border: solid 1px $card-default-border-color; // require angle/app/common/variables.scss
  }
}

.strike-appeals-form {
  .form-group {
    .control-label {
      font-weight: 700;
      margin-bottom: 4px;
    }
  }
}

.strike-detail-group {
  display: flex;
  flex-direction: row;
  flex: 1 1 auto;
}

.strike-detail-item {
  display: flex;
  flex-direction: column;
  flex: 1 1 auto;
  margin: 5px 0;

  .item-label, .item-value {
    font-weight: 500;
    font-size: 10px;
    line-height: 12px;
    color: $merit-strike-gray;
  }

  @media only screen and (min-width: 575px) {
    .item-label, .item-value {
      font-size: 14px !important;
    }
  }

  .item-label {
    font-weight: 700;
    margin-bottom: 4px;

    &.strike-detail-info {
      font-size: 12px;
      line-height: 14px;
    }

    &#strike_detail_point {
      color: $merit-strike-red;
    }

    @media only screen and (max-width: 575px) {
      &#strike_detail_point {
        text-align: right;
      }
    }
  }

  .item-value {
    font-weight: 500;
    font-size: 10px;
    line-height: 12px;
  }

  @media only screen and (min-width: 321px) and (max-width: 414px) {
    .item-value {
      max-width: 256px;
    }
  }

  @media only screen and (max-width: 320px) {
    .item-value {
      max-width: 201px;
    }
  }
}

.merit-redemption-detail {
  .redemption-details {
    strong {
      font-size: 0.75rem;
    }
    span, a {
      font-size: 0.7rem;
    }
    .card-body {
      .row {
        margin-bottom: 10px;
      }
    }
  }
}

.reward-details,
.appeal-details {
  .card-body {
    strong, span, a {
      font-size: 0.75rem;
    }
    .reward-info {
      font-size: 0.85rem;
    }
    span {
      &.item-value {
        img {
          max-height: 150px;
        }
      }
    }
  }
}

.strike-appeals-detail {
  .card-wrapper {
    padding-left: 0;
    padding-right: 0;
  }
}

.merit-redemption-detail {
  .reward-details {
    .card-body {
      strong, span {
        font-size: 10px;
        color: $merit-strike-gray;
      }
      .reward-info {
        font-size: 12px;
      }
    }
  }
  .redemption-details {
    strong, span, a {
      font-size: 10px;
      color: $merit-strike-gray;
    }
    .card-body {
      .row {
        margin-bottom: 10px;
      }
    }
  }
}
