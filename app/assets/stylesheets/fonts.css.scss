/**
 * Redefine font-face with font-url() , this function is provided by sass-rails.
 */

/* Font Awesome 5 */

@font-face {
  font-family: "Font Awesome 5 Free";
  font-style: normal;
  font-weight: 900;
  src: font-url("@fortawesome/fontawesome-free/webfonts/fa-solid-900.eot");
  src: font-url(
        "@fortawesome/fontawesome-free/webfonts/fa-solid-900.eot?#iefix"
      )
      format("embedded-opentype"),
    font-url("@fortawesome/fontawesome-free/webfonts/fa-solid-900.woff2")
      format("woff2"),
    font-url("@fortawesome/fontawesome-free/webfonts/fa-solid-900.woff")
      format("woff"),
    font-url("@fortawesome/fontawesome-free/webfonts/fa-solid-900.ttf")
      format("truetype"),
    font-url(
        "@fortawesome/fontawesome-free/webfonts/fa-solid-900.svg#fontawesome"
      )
      format("svg");
}

@font-face {
  font-family: "Font Awesome 5 Free";
  font-style: normal;
  font-weight: 400;
  src: font-url("@fortawesome/fontawesome-free/webfonts/fa-regular-400.eot");
  src: font-url(
        "@fortawesome/fontawesome-free/webfonts/fa-regular-400.eot?#iefix"
      )
      format("embedded-opentype"),
    font-url("@fortawesome/fontawesome-free/webfonts/fa-regular-400.woff2")
      format("woff2"),
    font-url("@fortawesome/fontawesome-free/webfonts/fa-regular-400.woff")
      format("woff"),
    font-url("@fortawesome/fontawesome-free/webfonts/fa-regular-400.ttf")
      format("truetype"),
    font-url(
        "@fortawesome/fontawesome-free/webfonts/fa-regular-400.svg#fontawesome"
      )
      format("svg");
}

@font-face {
  font-family: "Font Awesome 5 Brands";
  font-style: normal;
  font-weight: normal;
  src: font-url("@fortawesome/fontawesome-free/webfonts/fa-brands-400.eot");
  src: font-url(
        "@fortawesome/fontawesome-free/webfonts/fa-brands-400.eot?#iefix"
      )
      format("embedded-opentype"),
    font-url("@fortawesome/fontawesome-free/webfonts/fa-brands-400.woff2")
      format("woff2"),
    font-url("@fortawesome/fontawesome-free/webfonts/fa-brands-400.woff")
      format("woff"),
    font-url("@fortawesome/fontawesome-free/webfonts/fa-brands-400.ttf")
      format("truetype"),
    font-url(
        "@fortawesome/fontawesome-free/webfonts/fa-brands-400.svg#fontawesome"
      )
      format("svg");
}

/* Simple-Line-Icons */

@font-face {
  font-family: "Simple-Line-Icons";
  src: font-url("simple-line-icons/fonts/Simple-Line-Icons.eot");
  src: font-url("simple-line-icons/fonts/Simple-Line-Icons.eot?#iefix")
      format("embedded-opentype"),
    font-url("simple-line-icons/fonts/Simple-Line-Icons.woff") format("woff"),
    font-url("simple-line-icons/fonts/Simple-Line-Icons.ttf") format("truetype"),
    font-url("simple-line-icons/fonts/Simple-Line-Icons.svg#Simple-Line-Icons")
      format("svg");
  font-weight: normal;
  font-style: normal;
}

/* Weather Icons */

@font-face {
  font-family: "weathericons";
  src: font-url("weather-icons/font/weathericons-regular-webfont.eot");
  src: font-url("weather-icons/font/weathericons-regular-webfont.eot?#iefix")
      format("embedded-opentype"),
    font-url("weather-icons/font/weathericons-regular-webfont.woff")
      format("woff"),
    font-url("weather-icons/font/weathericons-regular-webfont.ttf")
      format("truetype"),
    font-url(
        "weather-icons/font/weathericons-regular-webfont.svg#weathericons-regular-webfontRg"
      )
      format("svg");
  font-weight: normal;
  font-style: normal;
}

/* Montserrat font */
/*
 * How add to css:
 * font-family: 'Montserrat', sans-serif;
 */
 @import url("https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");
