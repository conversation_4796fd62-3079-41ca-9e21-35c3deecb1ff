// shared styles to make FMS style consistent with SnD app
$snd-orange: #F45A1B;
$snd-dark-orange: #F15A2B;
$snd-ultra-light-orange: #FEF4E8;
$snd-light-orange: #F89A1F;
$snd-green: #8CC540;
$snd-pastel-green: #3BA4B4;
$snd-gray: #CED1D9;
$snd-white: #FFFFFF;
$snd-black: #3E4148;
$snd-light-red: #FDECEB;
$snd-red: #F04237;
// merit strike origin
$snd-title-gray: #929292;
$snd-pale-green: #E8F3D9;
$snd-pale-orange: #FEEBD2;
$snd-semidark-orange: #FF8F2C;
$snd-border-gray: #CFDBE2;

.custom-snd {
  font-family: 'Montserrat', sans-serif;

  .btn-primary {
    background-color: $snd-orange;
    border: none;
    font-weight: 600;
  }

  .btn-green {
    background-color: $snd-green;
    border: none;
    font-weight: 600;
  }

  .btn-outline-primary {
    font-weight: 600;
    border-color: $snd-gray;
    color: $snd-orange;

    &:hover {
      background: initial;
    }
  }

  .btn-gosend-bike, .btn-gosend-car {
    float: left;
    margin: 16px;
  }

  .custom-overlay {
    text-align: center;
    position: relative;
    background-color: black;

    img {
      opacity: 0.5;
    }

    .caption {
      font-size: 12px;
      color: $snd-white;
      position: absolute;
      top: 45%;
      left: 0;
      width: 100%;
    }
  }

  input[type=file]::file-selector-button {
    border: 2px solid $snd-gray;
    padding:5px 10px;
    border-radius: .4em;
    margin-right: 10px;
    background-color: $snd-white;
    color: $snd-dark-orange;
    font-weight: 700;
    font-size: 12px;
  }

  .form-control-file { font-size: 12px; }

  .card {
    &-shadow {
      box-shadow: 0 2px 8px #dfdfdf;
      border: none;
      border-radius: 0.5rem;
    }
  }

  .custom-badge {
    font-weight: bold;
    display: inline-block;
    font-size: 0.75em;
    padding: 0.1em 0.5em;
    margin-bottom: 0.2em;
    border-radius: 4px;

    &.badge-orange {
      background-color: $snd-pale-orange;
      color: $snd-semidark-orange;
    }

    &.badge-green {
      background-color: $snd-pale-green;
      color: $snd-green;
    }

    &.badge-red {
      background-color: $snd-light-red;
      color: $snd-red;
    }

    &.badge-dark {
      background-color: $snd-gray;
      color: $snd-black;
    }
  }

  .custom-list {
    font-weight: 500;
    color: $snd-black;

    .left-column, .right-colomn {
      padding: 0;
    }

    .right-colomn {
      text-align: right;
    }

    .first-row {
      font-size: 0.9em;
    }

    .second-row {
      font-weight: bold;
      margin-bottom: 0.10rem;
    }

    .third-row {
      font-size: 0.8em;
    }
  }

  .customized-select {

    position: relative;

    select {
      -webkit-appearance: none;
      -moz-appearance: none;
      -o-appearance: none;
      appearance: none;
    }

    .fas {
      position: absolute;
      pointer-events: none;
      top: 12px;
      right: 30px;
      color: $snd-pastel-green;
    }
  }

  /* non-bootstrap styles */

  .custom-datatable-filter {
    input[type="text"],
    input[type="date"] {
      font-weight: 500;
    }

    .search-field {
      .field-icon {
        position: absolute;
        right: 1%;
        top: 50%;
        transform: translateY(-50%);
        z-index: 999;
        padding: 0.2em 0.5em;
        background-color: white;
      }
    }
  }

  .date-selector {
    display: flex;
    justify-content: space-between;

    &-col {
      width: 48%;
      position: relative;
    }

    .btn-cal-wrp {
      width: 100%;
      position: relative;
      z-index: 10;
    }

    .cal-click {
      background-color: transparent;
      cursor: pointer;
      -webkit-appearance: none;

      &::-webkit-calendar-picker-indicator {
        -webkit-appearance: none;
        opacity: 0;
      }

      &::-webkit-inner-spin-button {
        -webkit-appearance: none;
      }

      &::-webkit-clear-button {
        margin-right: 75px;
      }
    }

    .btn-cal {
      background: transparent;
      border: none;
      position: absolute;
      right: 0.5em;
      top: 50%;
      height: 50%;
    }
  }

  .dataTables_filter {
    display: none;
  }

  .page-item:not(.active) .page-link {
    color: $snd-orange;
  }

  .page-item.active .page-link {
    background-color: $snd-orange;
    border-color: $snd-orange;
  }

  // details page
  .form-detail {
    margin-bottom: 1rem;
    margin-top: 1rem;
  }

  .label {
    color: #6D727D;
    font-size: 10px;
    margin-bottom: 0.15rem
   }

  .value {
    font-size: 14px;
    font-weight: 600;
  }

  .page-title {
    margin-left: 19px;
    font-size: 16px;
    font-weight: 700;
    line-height: 19px;
    color: $snd-title-gray;
  }
}

// .content-wrapper customization
.content-wrapper {
  @media only screen and (max-width: 575px) {
    &.strike-appeals-form,
    &.merit-redemption-detail,
    &.strike-appeals-detail,
    &.training-view {
      padding-left: 0;
      padding-right: 0;
    }
  }
}

#datatable-configurable-columndefs-mobile-view {
  .tbody {
    td {
      &.row-column,
      &.strike-information {
        border-bottom: solid 1px $snd-border-gray !important;
      }
      &.strike-information {
        padding: 12px;
      }
    }
  }
}

@media only screen and (max-width: 575px) {
  #datatable-configurable-columndefs-mobile-view {
    tbody {
      .row-column {
        padding-top: 12px;
        padding-bottom: 12px;
      }
      .row-column-left {
        padding-left: 12px;
      }
      .row-column-right {
        margin-right: 4px;
      }
    }
  }
}
