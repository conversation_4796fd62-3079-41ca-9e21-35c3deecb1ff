// Place all the styles related to the Forms controller here.
// They will automatically be included in application.css.
// You can use Sass (SCSS) here: http://sass-lang.com/

// --- Select2
//= require select2/dist/css/select2.css
//= require @ttskch/select2-bootstrap4-theme/dist/select2-bootstrap4.css

// Select 2
.select2-container {
  max-width: 100%;
}
.select2-container--bootstrap4 .select2-selection--single {
  height: 35px !important;
  .select2-selection__rendered,
  .select2-selection__placeholder {
    line-height: 35px !important;
  }
}
.select2-container--bootstrap4 .select2-selection,
.select2-container--bootstrap4.select2-container--focus .select2-selection {
  border: 1px solid #dde6e9;
  box-shadow: 0 0 0 #000 !important;
}
.select2-container--bootstrap4
  .select2-selection--multiple
  .select2-selection__rendered {
  padding: 4px 5px;
}
.select2-dropdown {
  max-height: 300px;
  overflow-y: scroll;
}
.select2-container--bootstrap4 .select2-selection__clear {
  margin-top: 0.65em;
}
