$order-history-light-gray: #F5F5F7;
$order-history-light-gold: #FEF4E8;
$order-history-light-purple: #EDEBF4;
$order-history-light-red: #FDECEB;
$order-history-light-blue: #EBF5F7;
$order-history-gold: #F89A1F;
$order-history-purple: #4C3994;
$order-history-red: #F04237;
$order-history-blue: #3BA4B4;
$order-history-text-color: #3E4148;
$order-history-muted-text: #9DA3B4;
$order-history-label-color: #6D727D;

#order-history {
  .card {
    margin-bottom: 0.75rem;
  }

  .gold-member {
    display: inline-block;
    font-size: 0.75em;
    background-color: $order-history-light-gold;
    color: $order-history-gold;
    padding: 0.1em 0.5em;
    margin-bottom: 0.2em;
    border-radius: 4px;
  }
}

#order-history-detail {

  color: $order-history-text-color;

  .card {
    margin-bottom: 0.5rem;
  }

  .order-information {
    font-size: 12px;

    .customer {
      font-weight: bold;
      font-size: 14px;

      .gold-member-first-order {
        display: inline-block;
        font-size: 0.75em;
        background-color: $order-history-light-gold;
        color: $order-history-gold;
        padding: 0.1em 0.5em;
        margin-bottom: 0.2em;
        border-radius: 4px;
      }
    }

    .order-number, .date {
      font-weight: 500;
    }
  }

  .order-details {

    .title {
      font-weight: bold;
      margin-bottom: 0.3rem;
    }

    .label {
      font-size: 10px;
      font-weight: 500;
      width: 35%;
      color: $order-history-label-color;
    }

    .payment-label {
      width: 60%;
    }

    .value {
      font-size: 12px;
      font-weight: 550;
      width: 65%;
      text-align: right;
    }

    .payment-value {
      width: 40%;
    }

    .custom-overlay {
      text-align: center;
      position: relative;
      background-color: black;

      .thumbnail-image {
        opacity: 0.75;
      }

      .caption {
        font-size: 14px;
        color: white;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        left: 0;
        width: 100%;
      }
    }

    .thumbnail-image {
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
      height: 60px;
      width: 60px;
      border-radius: 5px;
    }
  }

  .item-details {
    font-size: 0.9em;

    .title {
      font-weight: bold;
      font-size: 14px;
    }

    .subtitle {
      font-size: 0.8em;
    }

    .item-title {
      font-weight: bold;
    }

    .item-card {
      margin: 10px 0;
      box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.1);
      -webkit-box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.1);
      -moz-box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.1);
    }

    .original-price {
      font-weight: 400;
      font-size: 11px;
      color: $order-history-muted-text;
    }

    .packaging-label {
      font-weight: bold;
      text-align: center;
      display: inline-block;
      font-size: 0.75em;
      background-color: $order-history-light-purple;
      color: $order-history-purple;
      padding: 0.1em 0.5em;
      margin-bottom: 0.2em;
      border-radius: 4px;
    }

    .item-amount { font-weight: bold; }
    .item-quantity { font-size: 0.8em; }
    .sku { color: $order-history-muted-text}

    .unit {
      font-size: 0.8em;
      color: $order-history-muted-text;
    }

    .replacement-item-separator {
      border-top: 1px dashed #CED1D9;
      box-sizing: border-box;
      border-radius: 4px;
    }

    .replacement-label {
      font-weight: bold;
      text-align: center;
      display: inline-block;
      font-size: 0.8em;
      padding: 0.1em 0.5em;
      border-radius: 4px;

      &.by-shopper {
        background-color: $order-history-light-gold;
        color: $order-history-gold;
      }

      &.by-sku {
        background-color: $order-history-light-blue;
        color: $order-history-blue;
      }

      &.preference-dont-replace {
        background-color: $order-history-light-red;
        color: $order-history-red;
      }
    }

    .replacement_notes { font-size: 0.9em; }

    .overlay-oos-item {
      height:100%;
      width: 100%;
      position:relative;
      background-color:#FFFFFF;
      opacity:.4;
    }

    .item-image {
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
      height: 64px;
      width: 64px;
    }
  }
}

.modal-photos {

  .modal {
    background-color: $order-history-text-color;
  }

  .close {
    color: white;
    float: left;
    opacity: 1;
  }

  .displayed-picture {
    height: 80vh;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
  }
}
