.custom-breadcrumb {
  background-color: initial;
  padding: initial;

  .active {
    font-weight: bold;
  }

  .breadcrumb-item + .breadcrumb-item::before {
    content: '>'
  }
}

.custom-modal {
  .modal-header {
    border-bottom: none;
  }

  .modal-footer {
    border-top: none;
  }

  label, .btn {
    font-weight: bold;
  }
}

.duplicate-modal {
  .modal-dialog {
    padding-top: 13%;
    padding-bottom: 13%;
  }
}

.modal-training {
  @extend .custom-modal;

  .modal-footer {
    align-items: initial;
    justify-content: initial;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
  }

  .img-preview {
    width: 100%;
  }

  .red-text {
    color: #F15B2A;
  }
}

.training-submodule {
  &.breadcrumb {
    @extend .custom-breadcrumb;
  }
}

.form-quiz-question-answer {
  .quiz-answer-list {
    .correct-choice {
      width: 40%;

      label, input {
        cursor: pointer;
      }
    }

    label {
      margin-bottom: 0;
    }
  }
}

#training-submodules-cms {
  .btn-primary.disabled {
    background-color: #F5F5F7;
    border-color: #E0E0E0;
    color: #9CA3B6;

    &:hover {
      cursor: default;
    }
  }

  .divider {
    flex-grow: 1;
  }
}

#quiz-cms {
  .breadcrumb {
    @extend .custom-breadcrumb;
  }

  .quiz-attributes {
    span:not(:last-child) {
      &::after {
        margin: 0 0.75em;
        content: "|";
      }
    }
  }

  .quiz-question-list {
    margin: 2em 0;

    &:first-child {
      margin-top: initial;
    }

    &:last-child {
      margin-bottom: initial;
    }

    .actions {
      $width: 15%;
      flex: 0 0 $width;
      max-width: $width;
    }
  }

  .quiz-question-answer {
    flex-grow: 1;

    .answers {
      & > .answer-item:first-child.correct {
        margin-top: 0.5em;
      }

      & > .answer-item:last-child:not(.correct) {
        padding-bottom: 0;
      }
    }

    .answer-item {
      padding: 0.5em 1em;

      .answer-body {
        flex-grow: 1;
      }

      &.correct {
        $green: #8CC540;

        background-color: #F3F9EB;
        border: 1px solid $green;

        .fas {
          color: $green;
        }
      }
    }
  }
}

#quiz-form {
  label:not(.form-check-label) {
    font-weight: bold;
  }

  .form-check-label {
    cursor: pointer;
  }
}