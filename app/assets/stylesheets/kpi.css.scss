$performance-tosca: #3ba4b4;
$performance-green: #8cc540;

.performance {
  font-family: "Montserrat", sans-serif;
}

.period-indicator {
  font-size: 1.1rem;
  margin-bottom: 1em;

  .period-navigation span {
    color: $performance-green;
  }

  .period-dates {
    text-align: center;
    font-weight: bold;
  }
}

.performance-summary {
  box-shadow: 0 2px 8px #b8b8b8;

  &.card {
    border: none;
  }

  .card-header {
    background-color: $performance-tosca;
    color: #ffffff;
    font-weight: bold;
  }

  .kpi-assigned {
    font-weight: 600;
  }

  .last-update {
    font-size: 0.75rem;
  }

  .card-footer {
    background-color: #FEEBD2;
    border: none;
    font-size: 0.75rem;

    .fas {
      font-size: 1.25rem;
      color: #6D727D;
    }
  }
}

.earning-accordion {
  border-top: 1px solid #F5F5F7;

  .accordion-toggler {
    cursor: pointer;

    .fas {
      color: $performance-tosca;
      font-size: 1.2em;
    }
  }

  .annotation {
    font-weight: bold;
  }

  .earning-item {
    font-size: 0.85em;

    &-name {
      line-height: 1.2em;
    }

    &-detail {
      min-width: fit-content;
    }
  }
}

.kpi-card {
  $red: #f04237;
  $orange: #f89a1f;
  $light-gray: #eaebef;
  $gray: #979797;

  box-shadow: 0 2px 8px #b8b8b8;
  border: none;
  padding-left: 0;
  padding-right: 0;

  .progress {
    position: relative;
    background-color: $light-gray;
    border: none;

    .target-line {
      position: absolute;
      z-index: 10;
      top: 0;
      height: 100%;
      border-left: solid 2px $gray;

      &.reached {
        border-left-color: #ffffff;
      }
    }

    .bg-green {
      background-color: $performance-green;
    }

    .bg-danger {
      background-color: $red;
    }
  }

  .kpi-name {
    color: $performance-tosca;
    font-weight: 600;
    margin-bottom: 0.5em;
    margin-top: 0.4em;
  }

  .positive {
    .star-rating {
      color: $orange;
      font-size: 1.5em;
    }
  }

  .user-result {
    margin-bottom: 0;
    font-size: 30px;
    font-weight: 700;
  }

  .unit {
    margin-bottom: 0.25em;
  }

  .negative {
    .kpi-name {
      color: $red;
    }
  }
}
