module FulfillmentConfig
  module StockLocationTimeHelper
    extend ActiveSupport::Concern

    private

      def convert_utc_time_fields_to_localtime(stock_location)
        stock_location_time_zone = stock_location["state"]["time_zone"]

        Service::Fulfillment::StockLocation.time_fields.each do |time_field|
          next if stock_location[time_field].nil?

          split_time_field = stock_location[time_field].split(":")
          hour = split_time_field[0]
          minute = split_time_field[1]
          utc_time = DateTime.now.utc.change(hour: hour, min: minute)

          stock_location[time_field] = utc_time.in_time_zone(stock_location_time_zone).strftime("%H:%M")
        end

        stock_location

      rescue ArgumentError => e
        context.fail!(message: e.message, status_code: :internal_server_error)
      end
  end
end
