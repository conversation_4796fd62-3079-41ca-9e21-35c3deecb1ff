module DuplicateTrainingModuleHelper
  include ActiveSupport::Concern

  def get_last_seq_modul_target(track_target)
    last_modul_target = track_target.training_modules.order(:seq).last
    if last_modul_target.nil?
      0
    else
      last_modul_target.seq
    end
  end

  def get_summary_origin(modul_origin)
    modul_origin.training_submodules.summary
  end

  def get_submodules_origin(modul_origin)
    modul_origin.training_submodules.contents
  end
end
