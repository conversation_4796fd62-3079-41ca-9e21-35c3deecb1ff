module QuizHelper
  include ActiveSupport::Concern

  def validate_quiz_answers(answers_hash)
    if answers_all_blank?(answers_hash)
      I18n.t("admin.quiz_questions.e.quiz_answers_nil")
    elsif only_one_answer?(answers_hash)
      I18n.t("admin.quiz_questions.e.only_one_answer")
    elsif has_duplicate_answer?(answers_hash)
      I18n.t("admin.quiz_questions.e.duplicate_answer")
    elsif has_skipped_answer?(answers_hash)
      I18n.t("admin.quiz_questions.e.has_skipped_answer")
    else
      nil
    end
  end

  def answers_without_blank(answers_hash)
    answers_hash.values.select(&:present?)
  end

  def correct_answer_in_range?(answers_hash, correct_answer_index)
    largest_answer_index = answers_without_blank(answers_hash).size - 1
    (0..largest_answer_index).include? correct_answer_index.to_i
  end

  def validate_quiz_attempt_appeal(quiz, user)
    requested_appeals = QuizAttemptAppeal.where(quiz: quiz, requester: user, status: :requested)
    latest_quiz_attempt = QuizAttempt.where(user: user, quiz: quiz).order(created_at: :desc).first
    if requested_appeals.present?
      I18n.t("snd.trainings.e.appeal_already_exists")
    elsif latest_quiz_attempt.present? && latest_quiz_attempt.attempt_taken < quiz.attempt_limit
      I18n.t("snd.trainings.e.remaining_attempts_available")
    else
      nil
    end
  end

  private
    def answers_all_blank?(answers_hash)
      answers_without_blank(answers_hash).empty?
    end

    def only_one_answer?(answers_hash)
      answers_without_blank(answers_hash).size == 1
    end

    def has_duplicate_answer?(answers_hash)
      answer_occurences = answers_without_blank(answers_hash).group_by(&:itself).transform_values(&:count).values
      answer_occurences.any? { |answer_count| answer_count > 1 }
    end

    def has_skipped_answer?(answers_hash)
      last_answer = answers_without_blank(answers_hash).last
      last_answer_index = answers_hash.key(last_answer).to_i
      answers_without_blank(answers_hash).size != (last_answer_index + 1)
    end
end
