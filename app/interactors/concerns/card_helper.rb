require "bcrypt"

module Card<PERSON>elper
  extend ActiveSupport::Concern

  private

    # Set discarded_at column based on "is_active" params
    def set_discarded_at(is_active)
      if is_active == true || is_active == "1"
        context.model.discarded_at = nil
      else
        context.model.discarded_at = Time.current
      end
    end

    def create_card_number_hash(card_number)
      BCrypt::Password.create(card_number.to_s)
    end

    def validate_card_number_uniqueness
      card_num = context.params[:card_number]
      bin = get_bin(card_num)
      last_digits = get_last_digits(card_num)
      similar_cards = Inventory.cards
        .where("custom_properties @> hstore(:key, :value)", key: :bin, value: bin)
        .where("custom_properties @> hstore(:key, :value)", key: :last_digits, value: last_digits)

      if context.model.new_record?
        # create
        similar_cards.each do |card| # use traditional loop to break when duplicate is found
          if card.card_number_hash.present?
            if BCrypt::Password.new(card.card_number_hash) == card_num
              context.fail!(message: I18n.t("admin.cards.e.card_duplicate"), status_code: :unprocessable_entity)
            end
          end
        end
      else
        # update
        similar_cards = similar_cards.where.not(id: context.model.id)
        similar_cards.select do |card|
          if card.card_number_hash.present?
            if BCrypt::Password.new(card.card_number_hash) == card_num
              context.fail!(message: I18n.t("admin.cards.e.card_duplicate"), status_code: :unprocessable_entity)
            end
          end
        end
      end
    end

    def get_bin(card_number)
      card_number[0, 6] # bin (first 6 digits)
    end

    def get_last_digits(card_number)
      card_number[card_number.length - 4, card_number.length] # last_digits (4 digits)
    end
end
