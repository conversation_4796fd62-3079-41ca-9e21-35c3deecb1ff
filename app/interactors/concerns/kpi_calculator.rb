module KpiCalculator
  include ActiveSupport::Concern

  def calculate_kpi(user_kpis, kpi_type)
    calc_method = "calculate_#{kpi_type}"
    self.public_send(calc_method, user_kpis)
  end

  def calculate_on_time_performance(user_kpis)
    get_rate(user_kpis, :on_time_orders, :orders_delivered)
  end

  def calculate_almost_on_time(user_kpis)
    get_rate(user_kpis, :almost_on_time_orders, :orders_delivered)
  end

  def calculate_early_on_time(user_kpis)
    get_rate(user_kpis, :early_on_time_orders, :orders_delivered)
  end

  def calculate_on_time_almost_late(user_kpis)
    get_rate(user_kpis, :on_time_almost_late_orders, :orders_delivered)
  end

  def calculate_very_late(user_kpis)
    get_rate(user_kpis, :very_late_orders, :orders_delivered)
  end

  def calculate_stock_check_accuracy(user_kpis)
    get_rate(user_kpis, :correct_stocks_checked, :stocks_checked)
  end

  def calculate_replacement_rate(user_kpis)
    get_rate(user_kpis, :unique_replaced_items, :unique_oos_items_before_replacement)
  end

  def calculate_oos_rate_after_replacement(user_kpis)
    get_rate(user_kpis, :oos_items_after_replacement, :items_ordered)
  end

  def calculate_oos_rate_per_total_after_replacement(user_kpis)
    get_rate(user_kpis, :unique_oos_items_after_replacement, :unique_items_ordered)
  end

  def calculate_big_orders(user_kpis)
    user_kpis.sum(:big_orders)
  end

  def calculate_chat_initiative(user_kpis)
    get_rate(user_kpis, :chats_on_chat_contact_method, :orders_with_oos_with_chat_contact_method)
  end

  def calculate_days_worked(user_kpis)
    user_kpis.sum(:days_worked)
  end

  def calculate_days_worked_with_order(user_kpis)
    user_kpis.sum(:days_worked_with_order)
  end

  def calculate_star_rating(user_kpis)
    get_rate(user_kpis, :star_rating_total, :orders_delivered_with_rating)
  end

  def calculate_orders_picked(user_kpis)
    user_kpis.sum(:orders_picked)
  end

  def calculate_orders_delivered(user_kpis)
    user_kpis.sum(:orders_delivered)
  end

  def calculate_orders_delivered_with_failed(user_kpis)
    user_kpis.sum(:orders_delivered_with_failed)
  end

  def calculate_rejection_rate(user_kpis)
    get_rate(user_kpis, :rejected_items, :items_ordered)
  end

  def calculate_on_time_shopping(user_kpis)
    get_rate(user_kpis, :on_time_shoppings, :orders_picked)
  end

  def calculate_number_of_items_delivered(user_kpis)
    user_kpis.sum(:number_of_items_delivered)
  end

  def calculate_let_shopper_pick(user_kpis)
    get_rate(user_kpis, :let_shopper_pick, :unique_sku_oos_items_before_replacement_with_let_shopper_pick)
  end

  def calculate_call_customer(user_kpis)
    get_rate(user_kpis, :call_customer, :unique_sku_oos_items_before_replacement_with_chat)
  end

  def calculate_chat_customer(user_kpis)
    get_rate(user_kpis, :chat_customer, :unique_sku_oos_items_before_replacement_with_call)
  end

  def calculate_delivered_orders_sequence_1(user_kpis)
    user_kpis.sum(:delivered_orders_sequence_1)
  end

  def calculate_delivered_orders_sequence_2(user_kpis)
    user_kpis.sum(:delivered_orders_sequence_2)
  end

  def calculate_delivered_orders_sequence_3(user_kpis)
    user_kpis.sum(:delivered_orders_sequence_3)
  end

  def calculate_delivered_orders_sequence_4(user_kpis)
    user_kpis.sum(:delivered_orders_sequence_4)
  end

  def calculate_delivered_orders_sequence_5(user_kpis)
    user_kpis.sum(:delivered_orders_sequence_5)
  end

  def calculate_delivered_orders_sequence_more_than_5(user_kpis)
    user_kpis.sum(:delivered_orders_sequence_more_than_5)
  end

  def calculate_ship_distance(user_kpis)
    user_kpis.sum(:ship_distance)
  end

  private

    def get_rate(user_kpis, dividend, divisor)
      # can also use user_kpis.pluck("SUM(#{dividend})", "SUM(#{divisor})").first
      # if querying twice causes performance issues
      dividend = user_kpis.sum(dividend)
      divisor = user_kpis.sum(divisor)

      if divisor == 0
        0
      else
        # must be converted to float first,
        # because ruby always returns integer on integer division operation
        (dividend.to_f / divisor.to_f).floor(2)
      end
    end

    def result_for_display(kpi, result)
      result_float = result.to_f
      if kpi.unit == "%"
        "#{(result_float * 100).to_i}%"
      elsif result_float % 1 == 0 # whole number
        result_float.to_i
      else
        result_float
      end
    end
end
