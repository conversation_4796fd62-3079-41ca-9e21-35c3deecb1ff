require "open-uri"
require "csv"

module SettlementAdjustmentHelper
  extend ActiveSupport::Concern

  # @params {String} url Cloudinary or other public cloud URL.
  # @returns {Tempfile}
  #          {StandardError} can be from open-uri exceptions or IOError.
  def download_file_from_url(url, &block)
    filename = File.basename(url)
    tempfile = Tempfile.new(filename, Rails.root.join("tmp").to_s)

    File.open(tempfile.path, "wb") do |tmp|
      open(url, "rb") do |downloaded_file|
        tmp.write(downloaded_file.read)
      end
    end

    yield tempfile if block_given?
  end

  # @params {File} Tempfile or existing local file.
  # @returns {Hash} Hashes of rows from CSV
  #          {RuntimeError} CSV::MalformedCSVError etc
  def read_csv_rows(csv_file)
    csv_rows = []

    CSV.foreach(csv_file.path, headers: true) do |row|
      row_hash = row.to_h.symbolize_keys
      csv_rows << {
        fleet_email: row_hash[:fleet_email],
        amount: row_hash[:amount],
        date: row_hash[:date],
        description: row_hash[:description]
      }
    end

    csv_rows
  rescue RuntimeError => error
    # convert exception thrown by CSV into StandardError
    raise StandardError.new("#{error.class} #{error.message}")
  end
end
