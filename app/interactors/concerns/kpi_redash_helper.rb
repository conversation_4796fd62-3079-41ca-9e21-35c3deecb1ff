module KpiRedashHelper
  include ActiveSupport::Concern

  def validate_period_start_and_type(kpis, period_start, period_type)
    kpis.each do |kpi|
      if kpi.redash? && !kpi.redash_schedule.nil? && kpi.redash_schedule.period_type != period_type && kpi.redash_schedule.period_start != period_start
        context.fail!(message: I18n.t("admin.performance_schemes.e.invalid_kpis"))
      end
    end
  end

  def calculate_kpi_redash(email, kpi, start_date, end_date)
    value = 0
    redash_batch = RedashBatch
      .where(schedule_id: kpi.redash_schedule_id, start_date: start_date, end_date: end_date)
      .order(created_at: :desc).first

    if redash_batch.present?
      redash_facts = redash_batch.facts.where(email: email, column: kpi.redash_column).order(created_at: :desc).first
      if redash_facts.present?
        value = redash_facts.value.nil? ? 0 : redash_facts.value
      end
    end

    value
  end
end
