module SettlementBalancesHelper
  extend ActiveSupport::Concern

  private
    def cod_balance_validator(fleet_email)
      context.fail!(message: I18n.t("snd.settlement_balances.e.fleet_email_nil"), status_code: :unprocessable_entity) if fleet_email.blank?

      orders_by_email = SettlementBalance.where(fleet_email: fleet_email)
        .select("fleet_email", "settlement_total", "pending_total", "approved", "settled", "uncollected", "order_number")
      context.fail!(message: I18n.t("snd.settlement_balances.e.orders_by_email_nil"), status_code: :not_found) if orders_by_email.blank?

      invalid_order_numbers = []
      orders_by_email.each do |o|
        if o.settlement_total != (o.pending_total + o.approved + o.settled + o.uncollected)
          invalid_order_numbers << o.order_number
        end
      end
      context.fail!(message: I18n.t("snd.settlement_balances.e.invalid_settlement_balance", orders: invalid_order_numbers.join(", ")), status_code: :internal_server_error) if invalid_order_numbers.present?
    end

    def cod_balance_bulk_validator(fleet_emails)
      settlement_balances = SettlementBalance.where(fleet_email: fleet_emails)
        .select("fleet_email", "settlement_total", "pending_total", "approved", "settled", "uncollected", "order_number")

      # find fleet without settlement_balances
      fleet_emails_with_sb = settlement_balances.map { |sb| sb.fleet_email }
      fleet_emails_without_sb = fleet_emails - fleet_emails_with_sb
      context.fail!(message: I18n.t("snd.settlement_balances.e.orders_by_email_nil"), status_code: :not_found) if fleet_emails_without_sb.present?

      invalid_order_numbers = []
      fleet_emails.each do |email|
        fleet_orders = settlement_balances.select { |sb| sb.fleet_email == email }
        fleet_orders.each do |o|
          if o.settlement_total != (o.pending_total + o.approved + o.settled + o.uncollected)
            invalid_order_numbers << o.order_number
          end
        end
      end

      if invalid_order_numbers.present?
        err_message = I18n.t("snd.settlement_balances.e.invalid_settlement_balance", orders: invalid_order_numbers.join(", "))
        context.fail!(message: err_message, status_code: :internal_server_error)
      end
    end

    def distribute_cod_to_pending_total(settlement_log)
      fleet_email = settlement_log.fleet_email
      amount = settlement_log.amount
      orders = SettlementBalance.cod.where(fleet_email: fleet_email).order(delivery_time: :asc)
        .where("pending_total > 0").to_a
      settled_orders = []
      order_distributions = []
      settled_amount = amount
      while settled_amount > 0
        settled_order = orders.shift
        if settled_order.nil?
          return settled_orders
        end
        if settled_amount > settled_order.pending_total
          settled_order.settled += settled_order.pending_total
          settled_amount -= settled_order.pending_total
          distribution_amount = settled_order.pending_total
          settled_order.pending_total = 0
        else
          settled_order.settled += settled_amount
          settled_order.pending_total -= settled_amount
          distribution_amount = settled_amount
          settled_amount = 0
        end
        settled_orders << settled_order
        order_distributions << record_order_distribution(settlement_log, settled_order, "pending_to_settled", distribution_amount)
      end

      SettlementOrderDistribution.import! order_distributions, on_duplicate_key_update: [:id]
      settled_orders
    end

    def bulk_distribute_cod_to_pending_total(settlement_logs)
      fleets_settlement_balances = SettlementBalance.cod
        .where(fleet_email: settlement_logs.map(&:fleet_email))
        .where("pending_total > 0").to_a

      updated_settlement_balances = []
      order_distributions = []

      settlement_logs.each do |sl|
        settled_amount = sl.amount
        fleet_settlement_balances = fleets_settlement_balances.select { |o| o.fleet_email == sl.fleet_email }
          .sort_by(&:delivery_time)

        while settled_amount > 0
          sb = fleet_settlement_balances.shift

          return if sb.nil?
          if settled_amount > sb.pending_total
            sb.settled += sb.pending_total
            settled_amount -= sb.pending_total
            distribution_amount = sb.pending_total
            sb.pending_total = 0
          else
            sb.settled += settled_amount
            sb.pending_total -= settled_amount
            distribution_amount = settled_amount
            settled_amount = 0
          end

          # prepare data for bulk update (Settlement Balance)
          updated_settlement_balances << sb
          order_distributions << record_order_distribution(sl, sb, "pending_to_settled", distribution_amount)
        end
      end

      # bulk update and create
      SettlementBalance.import! updated_settlement_balances, on_duplicate_key_update: { conflict_target: [:id], columns: [:settled, :pending_total] }
      SettlementOrderDistribution.import! order_distributions, on_duplicate_key_update: [:id]
    end

    def undistribute_cod_from_pending_total(settlement_log)
      fleet_email = settlement_log.fleet_email
      amount = settlement_log.amount
      orders = SettlementBalance.cod.where(fleet_email: fleet_email).order(delivery_time: :asc)
        .where("settled > 0").to_a
      settled_orders = []
      order_distributions = []
      settled_amount = amount
      while settled_amount > 0
        settled_order = orders.pop
        if settled_order.nil?
          return settled_orders
        end
        if settled_amount > settled_order.settled
          settled_order.pending_total += settled_order.settled
          settled_amount -= settled_order.settled
          distribution_amount = settled_order.settled
          settled_order.settled = 0
        else
          settled_order.pending_total += settled_amount
          settled_order.settled -= settled_amount
          distribution_amount = settled_amount
          settled_amount = 0
        end
        settled_orders << settled_order
        order_distributions << record_order_distribution(settlement_log, settled_order, "settled_to_pending", distribution_amount)
      end

      SettlementOrderDistribution.import! order_distributions, on_duplicate_key_update: [:id]
      settled_orders
    end

    def distribute_settled_to_approved(settlement_log)
      fleet_email = settlement_log.fleet_email
      amount = settlement_log.amount
      orders = SettlementBalance.cod.where(fleet_email: fleet_email).order(delivery_time: :asc)
        .where("settled > 0").to_a
      approved_orders = []
      order_distributions = []
      approved_amount = amount
      while approved_amount > 0
        approved_order = orders.shift
        if approved_order.nil?
          return approved_orders
        end
        if approved_amount > approved_order.settled
          approved_order.approved += approved_order.settled
          approved_amount -= approved_order.settled
          distribution_amount = approved_order.settled
          approved_order.settled = 0
        else
          approved_order.approved += approved_amount
          approved_order.settled -= approved_amount
          distribution_amount = approved_amount
          approved_amount = 0
        end
        approved_orders << approved_order
        order_distributions << record_order_distribution(settlement_log, approved_order, "settled_to_approved", distribution_amount)
      end

      SettlementOrderDistribution.import! order_distributions, on_duplicate_key_update: [:id]
      approved_orders
    end

    def record_order_distribution(settlement_log, settlement_balance, distribution_type, distribution_amount)
      order_distribution = SettlementOrderDistribution.new(
        settlement_log_id: settlement_log.id,
        settlement_balance_id: settlement_balance.id,
        distribution_type: SettlementOrderDistribution.distribution_types[distribution_type],
        settlement_log_action: SettlementOrderDistribution.settlement_log_actions[settlement_log.action],
        amount: distribution_amount
      )

      order_distribution
    end
end
