module TimezoneHelper
  include ActiveSupport::Concern

  def utc_to_local(time_value)
    current_zone = time_value.try(:zone).nil? ? "UTC" : time_value.zone
    if (current_zone == "UTC") || (current_zone == "+00:00")
      tenant = Apartment::Tenant.current
      case tenant.downcase
      when "id"
        tz = "Asia/Jakarta"
      when "my"
        tz = "Asia/Kuala_Lumpur"
      when "th"
        tz = "Asia/Bangkok"
      else
        tz = "UTC"
      end
      converted_time = time_value.in_time_zone(tz)
    else
      converted_time = time_value
    end
    converted_time
  end

  def time_to_local(time)
    tenant = Apartment::Tenant.current
    case tenant.downcase
    when "id"
      tz = "Asia/Jakarta"
    when "my"
      tz = "Asia/Kuala_Lumpur"
    when "th"
      tz = "Asia/Bangkok"
    else
      tz = "UTC"
    end

    Time.find_zone(tz).parse(time)
  end
end
