module PayrollAdjustmentHelper
  extend ActiveSupport::Concern

  private
    def adjustment_condition_validator(number, target_amount, is_unconditional, condition_data_source, kpi_target_id)
      context.fail!(message: I18n.t("admin.payroll_adjustments.e.number_nil")) if number.blank?

      if target_amount <= 0 && !(is_kpi_related(condition_data_source) || is_unconditional)
        context.fail!(message: I18n.t("admin.payroll_adjustments.e.target_amount_zero"))
      end

      if !is_unconditional && is_kpi_related(condition_data_source) && kpi_target_id.blank?
        context.fail!(message: I18n.t("admin.payroll_adjustments.e.kpi_target_id_nil"))
      end

      if !is_unconditional && is_kpi_related(condition_data_source) && !KpiTarget.exists?(kpi_target_id)
        context.fail!(message: I18n.t("admin.payroll_adjustments.e.kpi_target_id_not_found"))
      end
    end

    def adjustment_item_validator(unit_amount, max_cap_amount, item_data_source, kpi_type)
      if unit_amount > 0 && max_cap_amount != 0 && max_cap_amount <= unit_amount
        context.fail!(message: I18n.t("admin.payroll_adjustments.e.max_cap_less_than_unit"))
      end

      if is_kpi_related(item_data_source) && kpi_type.blank?
        context.fail!(message: I18n.t("admin.payroll_adjustments.e.kpi_type_nil"))
      end
    end

    def is_kpi_related(data_source)
      [0, :user_kpis, 1, :redash_kpis].include? data_source
    end

    def set_condition_data_source(kpi_target_id, fact_id)
      # TODO: Accommodate redash_kpis in adjustment item kpi selection
      if kpi_target_id.present?
        kpi = KpiTarget.find(kpi_target_id).kpi
        condition_data_source = kpi.user_kpis? ? :user_kpis :
                                kpi.redash_kpis? ? :redash_kpis : nil
      elsif fact_id.present?
        condition_data_source = :payroll_fact
      else
        condition_data_source = nil
      end
      condition_data_source
    end

    def set_item_data_source(multiplier_fact_id, kpi_type)
      # TODO: Accommodate redash_kpis in adjustment item kpi selection
      multiplier_fact_id.present? ? :payroll_fact : kpi_type.present? ? :user_kpis : nil
    end
end
