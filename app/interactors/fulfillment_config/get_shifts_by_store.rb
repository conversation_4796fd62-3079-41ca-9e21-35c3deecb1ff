module FulfillmentConfig
  class GetShiftsByStore < ::FulfillmentConfig::BaseFulfillmentInteractor
    include Interactor

    def call
      external_id = context.store_external_id
      date = context.date
      response = Service::Fulfillment::Shift.find_by_store_and_date(external_id, date)

      # Handle request error
      handle_request_error(response)

      shifts = response.body["response"]["shifts"]

      # Get unique clusters and stores
      clusters = shifts.map { |sl| sl["stock_location"]["cluster"] }.uniq { |c| c["id"] }.each { |c| c["stores"] = [] }
      stores = shifts.map { |sl| sl["stock_location"] }.uniq { |s| s["id"] }.each { |s| s["shifts"] = [] }

      # Put store opening hours in stores
      stores.each do |store|
        interactor = ::FulfillmentConfig::GetStockLocation.call(id: store["id"])
        context.fail!(message: interactor.message, status_code: interactor.status_code) unless interactor.success?
        stock_location = interactor.result.with_indifferent_access
        if stock_location.present?
          store["opening_hours"] = "#{stock_location["open_at"]}-#{stock_location["close_at"]}"
          store["active"] = stock_location["active"]
        end
      end

      # Put shifts in stores
      shifts.each do |shift|
        stores.each do |store|
          store["shifts"] << shift.except("stock_location") if store["id"] == shift["stock_location"]["id"]
        end
      end

      # Put stores in clusters
      stores.each do |store|
        clusters.each do |cluster|
          if context.show_inactive == "true"
            cluster["stores"] << store.except("cluster") if cluster["id"] == store["cluster"]["id"]
          else
            cluster["stores"] << store.except("cluster") if cluster["id"] == store["cluster"]["id"] && store["active"]
          end
        end
      end

      context.result = clusters
    rescue Faraday::ConnectionFailed => e
      context.fail!(message: "#{ I18n.t("fulfillment_config.e.request_error") } (#{ e.message })", status_code: :bad_gateway)
    end
  end
end
