module FulfillmentConfig
  class UpdateStockLocation < BaseFulfillmentStockLocationInteractor
    include Interactor
    include FulfillmentConfig::FulfillmentAuditable

    def call
      context.fail!(message: I18n.t("fulfillment_config.long_slots.e.updater_nil"), status_code: :unprocessable_entity) unless context.updater.present?

      get_response = Service::Fulfillment::StockLocation.find_by_id(context.id)
      handle_request_error(get_response)
      if wrong_country?(get_response)
        context.fail!(message: I18n.t("fulfillment_config.stock_locations.e.wrong_country"), status_code: :unprocessable_entity)
      end

      prepared_form = convert_time_fields_to_utc(context.stock_location, get_response.body["stock_location"])
      prepared_form = convert_store_type(prepared_form)
      payload = merge_current_to_payload(prepared_form, get_response.body["stock_location"])

      response = Service::Fulfillment::StockLocation.update(context.id, payload)
      handle_request_error(response)

      context.result = response.body["stock_location"]

      create_audit_log user: context.updater, auditable_type: "stock_location", auditable_id: context.id, request_url: request_url(context.id)
    rescue Faraday::ClientError => e
      context.fail!(message: "#{ I18n.t("fulfillment_config.e.request_error") } (#{ e.message })", status_code: :bad_gateway)
    rescue ActiveModel::ValidationError => e
      context.errors = e.model.errors.to_h
      context.fail!(message: I18n.t("fulfillment_config.stock_locations.e.validation_error"), status_code: :unprocessable_entity)
    end


    private

      def merge_current_to_payload(stock_location_form, current_data)
        form_from_current_data = create_form(current_data)
        form_from_current_data.deep_merge(stock_location_form)
      end

      def create_form(current_data)
        form = {}
        form["type"] = current_data["type"]
        form["active"] = current_data["active"].to_s
        form["address1"] = current_data["address1"]
        form["address2"] = current_data["address2"]
        form["ask_receipt_number"] = current_data["ask_receipt_number"].to_s
        form["close_at"] = current_data["close_at"]
        form["cluster_id"] = current_data["cluster"]["id"]
        form["code"] = current_data["code"]
        form["crossday_prepicking_enabled"] = current_data["crossday_prepicking_enabled"].to_s
        form["ddf_type"] = current_data["ddf_type"]
        form["delivery_batch_notified_offset"] = current_data["delivery_batch_notified_offset"]
        form["enable_chat"] = current_data["enable_chat"].to_s
        form["enable_cut_off"] = current_data["enable_cut_off"].to_s
        form["enable_grab_express"] = current_data["enable_grab_express"].to_s
        form["enable_grab_express_cod"] = current_data["enable_grab_express_cod"].to_s
        form["enable_multi_batch_shopping"] = current_data["enable_multi_batch_shopping"].to_s
        form["lat"] = current_data["lat"]
        form["lon"] = current_data["lon"]
        form["max_delivery_handover"] = current_data["max_delivery_handover"].to_i
        form["max_delivery_volume"] = current_data["max_delivery_volume"]
        form["max_shopping_volume"] = current_data["max_shopping_volume"]
        form["name"] = current_data["name"]
        form["open_at"] = current_data["open_at"]
        form["preferences"] = {
          "grab_express_delay_time" => current_data["grab_express_delay_time"],
          "grab_express_offset_time" => current_data["grab_express_offset_time"],
          "meeting_point_lat" => current_data["meeting_point_lat"],
          "meeting_point_lon" => current_data["meeting_point_lon"],
          "meeting_point_name" => current_data["meeting_point_name"],
          "meeting_point_remark" => current_data["meeting_point_remark"],
          "enable_lalamove_delivery_fee" => current_data["enable_lalamove_delivery_fee"].to_s,
          "lalamove_flat_service_fee" => current_data["lalamove_flat_service_fee"],
          "enable_delyva_delivery_fee" => current_data["enable_delyva_delivery_fee"].to_s,
          "delyva_flat_service_fee" => current_data["delyva_flat_service_fee"],
          "ongoing_slot_cut_off" => current_data["ongoing_slot_cut_off"],
          "cut_off_offset_day" => current_data["cut_off_offset_day"],
          "cut_off_time" => current_data["cut_off_time"],
          "enable_pending_job_notification" => current_data["enable_pending_job_notification"].to_s,
          "express_set_slot_prioritize_on_demand" => current_data["express_set_slot_prioritize_on_demand"].to_s,
          "maximum_traveled_distance" => current_data["maximum_traveled_distance"]
        }
        form["prepicking_offset"] = current_data["prepicking_offset"]
        form["ship_distance_threshold"] = current_data["ship_distance_threshold"]
        form["shopper_average_picking_time_per_uniq_item"] = current_data["shopper_average_picking_time_per_uniq_item"]
        form["current_shopper_average_picking_time"] = current_data["shopper_average_picking_time_per_uniq_item"]
        form["shopper_handover_to_driver_time"] = current_data["shopper_handover_to_driver_time"].to_i
        form["shopper_queue_replacement_time"] = current_data["shopper_queue_replacement_time"].to_i
        form["shopping_batch_notified_offset"] = current_data["shopping_batch_notified_offset"]
        form["state_id"] = current_data["state"]["id"]
        form["supplier_id"] = current_data["supplier"]["id"]
        form["tpl_enabled"] = current_data["tpl_enabled"].to_s
        form["enable_lalamove"] = current_data["enable_lalamove"].to_s
        form["enable_delyva"] = current_data["enable_delyva"].to_s
        form
      end

      def convert_time_fields_to_utc(stock_location_form, current_data)
        stock_location_time_zone = current_data["state"]["time_zone"]

        Service::Fulfillment::StockLocation.time_fields.each do |time_field|
          next if stock_location_form[time_field].nil? && (stock_location_form["preferences"].blank? || stock_location_form["preferences"][time_field].blank?)

          is_updating_preference = true if stock_location_form["preferences"].present? && stock_location_form["preferences"][time_field].present?

          if is_updating_preference
            stock_location_form["preferences"][time_field] = local_time_string_to_utc_time_string(stock_location_form["preferences"][time_field], stock_location_time_zone)
          else
            stock_location_form[time_field] = local_time_string_to_utc_time_string(stock_location_form[time_field], stock_location_time_zone)
          end
        end

        stock_location_form

      rescue ArgumentError => e
        context.fail!(message: e.message, status_code: :internal_server_error)
      end

      def local_time_string_to_utc_time_string(local_time, stock_location_time_zone)
        hour = local_time.split(":")[0]
        minute = local_time.split(":")[1]
        localtime = DateTime.now.in_time_zone(stock_location_time_zone).change(hour: hour, min: minute)
        localtime.utc.strftime("%H:%M")
      end

      def request_url(stock_location_id)
        "#{ENV["FULFILLMENT_BASE_URL"]}/api/admin/stock_locations/#{ stock_location_id }"
      end

      def convert_store_type(stock_location_form)
        return stock_location_form if stock_location_form["type"].nil?
        stock_location_form["special"] = stock_location_form["type"].downcase == "special"
        stock_location_form
      end
  end
end
