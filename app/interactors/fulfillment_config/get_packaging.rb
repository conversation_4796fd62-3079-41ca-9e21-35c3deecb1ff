module FulfillmentConfig
  class GetPackaging < BaseFulfillmentInteractor
    include Interactor

    def call
      response = Service::Fulfillment::Packaging.find_by_id_and_stock_location_id(context.id, context.stock_location_id)
      handle_request_error(response)

      # Convert result (Hash) to object
      context.result = Service::Fulfillment::Packaging.new(
        response.body["packaging"],
        context.stock_location_id,
        is_new: false
      )
    rescue Faraday::ClientError => e
      context.fail!(message: "#{ I18n.t("fulfillment_config.e.request_error") } (#{ e.message })")
    end
  end
end
