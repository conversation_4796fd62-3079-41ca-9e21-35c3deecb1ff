module FulfillmentConfig
  module Ddf
    module Constants
      TIME_BASED_FEE_SHEET_NAME             = "BaseFee"
      TRAVELLING_ADDITIVE_SHEET_NAME        = "TravellingDistanceAdditive"
      SLOT_MULTIPLIER_AND_OTHER_SHEET_NAME  = "RemainingSlotMultiplierAndOther"
      TIME_BASED_FEE_MATRIX                 = "TimeBasedFeeMatrix"
      TRAVELLING_DISTANCE_ADDITION_MATRIX   = "TravellingDistanceAdditionMatrix"
      EACH_ADDITIONAL_KM_FIELD              = "EachAdditionalKm"
      FREE_DELIVERY_THRESHOLD               = "FreeDeliveryThreshold"
      KEY_TO_MATRIX_HASH                    = {
        "EachAdditionalKm"            => { row_id: 1, column_id: 0, type: "TravellingDistanceAdditionMatrix" },
        "ExemptedCapacity"            => { row_id: 0, column_id: 0, type: "ExemptedCapacityMatrix" },
        "OneOnOne"                    => { row_id: 0, column_id: 0, type: "SlotUtilizationMultiplierMatrix" },
        "OneOnTwo"                    => { row_id: 0, column_id: 1, type: "SlotUtilizationMultiplierMatrix" },
        "TwoOnTwo"                    => { row_id: 0, column_id: 2, type: "SlotUtilizationMultiplierMatrix" },
        "Low"                         => { row_id: 0, column_id: 3, type: "SlotUtilizationMultiplierMatrix" },
        "Med"                         => { row_id: 0, column_id: 4, type: "SlotUtilizationMultiplierMatrix" },
        "High"                        => { row_id: 0, column_id: 5, type: "SlotUtilizationMultiplierMatrix" },
        "LowerBoundary"               => { row_id: 0, column_id: 6, type: "SlotUtilizationMultiplierMatrix" },
        "UpperBoundary"               => { row_id: 0, column_id: 7, type: "SlotUtilizationMultiplierMatrix" },
        "DiscountMultiplier"          => { row_id: 0, column_id: 0, type: "RemainingTimeMultiplierMatrix" },
        "PremiumMultiplier"           => { row_id: 1, column_id: 0, type: "RemainingTimeMultiplierMatrix" },
        "TimeRemainingLowerBoundary"  => { row_id: 2, column_id: 0, type: "RemainingTimeMultiplierMatrix" },
        "TimeRemainingUpperBoundary"  => { row_id: 3, column_id: 0, type: "RemainingTimeMultiplierMatrix" },
        "SlotWindowSize"              => { row_id: 4, column_id: 0, type: "RemainingTimeMultiplierMatrix" },
        "FreeDeliveryThreshold"       => { row_id: 0, column_id: 0, type: "FreeDeliveryThresholdMatrix" },
        "NextHourFactor"              => { row_id: 0, column_id: 0, type: "NextHourMultiplierMatrix" },
        "NextDayDecrement"            => { row_id: 0, column_id: 0, type: "SaverFeeMatrix" },
        "SameDayIncrement"            => { row_id: 1, column_id: 1, type: "SaverFeeMatrix" },
        "Quota"                       => { row_id: 2, column_id: 2, type: "SaverFeeMatrix" },
        "ConstantX"                   => { row_id: 0, column_id: 0, type: "FourWheelMatrix" },
        "ConstantY"                   => { row_id: 0, column_id: 1, type: "FourWheelMatrix" },
      }
    end
  end
end
