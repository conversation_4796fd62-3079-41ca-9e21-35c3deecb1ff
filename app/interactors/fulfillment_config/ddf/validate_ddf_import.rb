module FulfillmentConfig
  module Ddf
    class ValidateDdfImport
      include Interactor

      def call
        context.fail!(message: I18n.t("fulfillment_config.ddfs.e.missing_parsed_ddf")) unless context.parsed_ddf.present?
        parsed_ddf = context.parsed_ddf

        begin
          context.ddf_import = Service::Fulfillment::DdfImport.from_hash(parsed_ddf)
        rescue ActiveModel::ValidationError => e
          context.errors = e.model.errors.to_h
          context.fail!(message: I18n.t("fulfillment_config.ddfs.e.import_validation_error"))
        end
      end
    end
  end
end
