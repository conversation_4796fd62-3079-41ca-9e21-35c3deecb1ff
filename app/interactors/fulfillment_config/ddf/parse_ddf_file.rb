module FulfillmentConfig
  module Ddf
    class ParseDdfFile
      include Interactor
      include ::FulfillmentConfig::Ddf::Constants

      def call
        context.fail!(message: I18n.t("fulfillment_config.ddfs.e.file_nil")) unless context.params[:file].present?
        file = context.params[:file]

        begin
          spreadsheet = Roo::Spreadsheet.open(file)
          time_based_fee = parse_time_based_fee(spreadsheet)
          travelling_distance_additive = parse_travelling_distance_additive(spreadsheet)
          slot_multiplier_and_other = parse_slot_multiplier_and_other(spreadsheet)

          context.parsed_ddf = {
            time_based_fee: time_based_fee,
            travelling_distance_additive: travelling_distance_additive,
            slot_multiplier_and_other: slot_multiplier_and_other
          }
        rescue Zip::Error, ArgumentError, IOError
          context.fail!(message: I18n.t("fulfillment_config.ddfs.e.file_io_error"))
        rescue Roo::Error, RangeError
          context.fail!(message: I18n.t("fulfillment_config.ddfs.e.parse_error_template_missing_parts"))
        end
      end


      private

        def parse_time_based_fee(spreadsheet)
          spreadsheet.sheet(TIME_BASED_FEE_SHEET_NAME).parse() do |row|
            row[1..-1]
          end
          .flatten
        end

        def parse_travelling_distance_additive(spreadsheet)
          spreadsheet.sheet(TRAVELLING_ADDITIVE_SHEET_NAME).parse(name: "name", value: "value") do |row|
            row
          end
        end

        def parse_slot_multiplier_and_other(spreadsheet)
          spreadsheet.sheet(SLOT_MULTIPLIER_AND_OTHER_SHEET_NAME).parse(category: "category", name: "name", value: "value") do |row|
            row
          end
        end
    end
  end
end
