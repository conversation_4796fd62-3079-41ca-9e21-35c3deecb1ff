module FulfillmentConfig
  class UpdateShipmentStatus < BaseFulfillmentInteractor
    include Interactor

    def call
      context.fail!(message: I18n.t("fulfillment_config.manual_arrivals.e.shipment_number_nil")) unless context.shipment_number.present?
      context.fail!(message: I18n.t("fulfillment_config.manual_arrivals.e.status_nil")) unless context.current_value.present?
      context.fail!(message: I18n.t("fulfillment_config.manual_arrivals.e.reason_empty")) unless context.reason.present?
      context.fail!(message: I18n.t("fulfillment_config.manual_arrivals.e.reason_empty")) if context.reason.eql?("others") && context.note.to_s.strip.blank?

      response = Service::Fulfillment::Shipment.update_status(context.shipment_number,
                                                              context.current_value,
                                                              { "reason" => context.reason, "note" => context.note.to_s.strip })

      # handle if service endpoint is unavailable
      if response.status.to_i == 404
        context.fail!(message: fallback_error_message, status_code: :not_found)
      else
        handle_request_error(response)
      end
    # rescue any StandardError (Faraday::Error parent class) may occured from Faraday
    rescue Faraday::Error => error
      context.fail!(message: (error.message.presence || fallback_error_message), status_code: :internal_server_error)
    end

    private

      def fallback_error_message
        @fallback_error_message ||= I18n.t(:request_error, scope: "fulfillment_config.e")
      end
    # end private block
  end
end
