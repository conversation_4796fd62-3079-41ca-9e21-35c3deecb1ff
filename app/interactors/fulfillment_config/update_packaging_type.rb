module FulfillmentConfig
  class UpdatePackagingType < BaseFulfillmentInteractor
    include Interactor

    def call
      response = Service::Fulfillment::PackagingType.update(context.country_id, context.id, context.packaging_type)
      handle_request_error(response)

      context.result = response.body["packaging_type"]
    rescue Faraday::ClientError => e
      context.fail!(message: "#{ I18n.t("fulfillment_config.e.request_error") } (#{ e.message })")
    rescue ActiveModel::ValidationError => e
      context.errors = e.model.errors.to_h
      context.fail!(message: I18n.t("fulfillment_config.packaging_types.e.validation_error"))
    end
  end
end
