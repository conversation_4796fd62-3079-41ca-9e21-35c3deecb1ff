module FulfillmentConfig
  class ImportLongSlots < BaseFulfillmentInteractor
    include Interactor

    def call
      context.fail!(message: I18n.t("fulfillment_config.slots.e.file_nil"), status_code: :unprocessable_entity) unless context.params[:file].present?

      begin
        response = Service::Fulfillment::Slot.upload_long_slot(context.params[:file])
        handle_request_error(response)
      rescue Faraday::ClientError => e
        context.fail!(message: "#{ I18n.t("fulfillment_config.e.request_error") } (#{ e.message })", status_code: :bad_gateway)
      end
    end
  end
end
