module FulfillmentConfig
  class ValidateFleetAssignmentReason < ::FulfillmentConfig::BaseFulfillmentInteractor
    include Interactor

    MIN_REASON_DESC_LEN = 3

    def call
      validate_reason_presence!
    end

    private

      def validate_reason_presence!
        context.fail!(message: I18n.t("fulfillment_config.manual_assignments.e.reason_empty")) unless context.reason.present?
        context.fail!(message: I18n.t("fulfillment_config.manual_assignments.e.reason_too_short", min: MIN_REASON_DESC_LEN)) if other_reason? && description_too_short?
      end

      def other_reason?
        context.reason.to_s.eql?("others")
      end

      def description_too_short?
        context.reason_description.to_s.strip.length < MIN_REASON_DESC_LEN
      end
    # end of private block
  end
end
