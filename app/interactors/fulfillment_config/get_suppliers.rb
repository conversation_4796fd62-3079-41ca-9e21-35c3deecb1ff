module FulfillmentConfig
  class GetSuppliers < ::FulfillmentConfig::BaseFulfillmentInteractor
    include Interactor

    def call
      response = Service::Fulfillment::Supplier.find_all
      handle_request_error(response)
      context.result = response.body["response"]["suppliers"]
    rescue Faraday::ConnectionFailed => e
      context.fail!(message: "#{ I18n.t("fulfillment_config.e.request_error") } (#{ e.message })", status_code: :bad_gateway)
    end
  end
end
