module FulfillmentConfig
  class SwitchFleetToTpl < BaseFulfillmentInteractor
    include Interactor

    def call
      response = Service::Fulfillment::Batch.switch_fleet_to_tpl(context.order_number.to_s, context.tpl_vendor.to_s, context.tpl_vehicle_type.to_s)
      # handle if service endpoint is unavailable
      if response.status.to_i == 404
        context.fail!(message: fallback_error_message)
      else
        handle_request_error(response)
      end
      context.result = response.body

      # required to pass it to create manual assignment log to distinguish flash message
      # since create log is lower priority than assignment result itself
      context.assignment_success = true

    # rescue any StandardError (Faraday::Error parent class) may occured from Faraday
    rescue Faraday::Error => error
      context.fail!(message: error.message.presence || fallback_error_message)
    end

    private

      def fallback_error_message
        @fallback_error_message ||= I18n.t(:request_error, scope: "fulfillment_config.e")
      end
    # end private block
  end
end
