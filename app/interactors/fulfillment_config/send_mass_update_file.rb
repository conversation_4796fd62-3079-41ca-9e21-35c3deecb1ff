module FulfillmentConfig
  class SendMassUpdateFile < BaseFulfillmentInteractor
    include Interactor

    def call
      context.fail!(message: I18n.t("fulfillment_config.mass_update.e.file_nil")) unless context.params[:file].present?

      begin
        response = Service::Fulfillment::StockLocation.upload_mass_configurations(context.params[:file])
        context.has_failing_validation = response.status == 207
        if [200, 207].include? response.status
          context.file = response.body
        else
          handle_request_error(response)
        end
      rescue Faraday::ClientError => e
        context.fail!(message: "#{ I18n.t("fulfillment_config.e.request_error") } (#{ e.message })")
      end
    end
  end
end
