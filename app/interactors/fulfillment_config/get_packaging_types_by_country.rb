module FulfillmentConfig
  class GetPackagingTypesByCountry < BaseFulfillmentInteractor
    include Interactor

    def call
      response = Service::Fulfillment::PackagingType.find_all_by_country_id(context.country_id)
      handle_request_error(response)

      packaging_types = response.body["response"]["packaging_types"]
      if context.active_only
        context.result = packaging_types.select { |pt| pt["active"] }
      else
        context.result = packaging_types
      end

    rescue Faraday::ClientError => e
      context.fail!(message: "#{ I18n.t("fulfillment_config.e.request_error") } (#{ e.message })")
    end
  end
end
