module FulfillmentConfig
  class GetPackagings < ::FulfillmentConfig::BaseFulfillmentInteractor
    include Interactor

    def call
      context.fail!(message: I18n.t("fulfillment_config.e.invalid_url_params")) if context.stock_location_id.nil?

      response = Service::Fulfillment::Packaging.find_all_by_stock_location_id(context.stock_location_id)
      handle_request_error(response)
      context.result = response.body["response"]["packaging"]
    rescue Faraday::ConnectionFailed => e
      context.fail!(message: "#{ I18n.t("fulfillment_config.e.request_error") } (#{ e.message })")
    end
  end
end
