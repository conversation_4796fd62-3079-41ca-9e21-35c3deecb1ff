module FulfillmentConfig
  class GetStockLocation < BaseFulfillmentStockLocationInteractor
    include Interactor
    include StockLocationTimeHelper

    def call
      response = Service::Fulfillment::StockLocation.find_by_id(context.id)
      handle_request_error(response)
      if wrong_country?(response)
        context.fail!(message: I18n.t("fulfillment_config.stock_locations.e.wrong_country"), status_code: :unprocessable_entity)
      end

      context.result = convert_utc_time_fields_to_localtime(response.body["stock_location"])

    rescue Faraday::ClientError => e
      context.fail!(message: "#{ I18n.t("fulfillment_config.e.request_error") } (#{ e.message })", status_code: :bad_gateway)
    end
  end
end
