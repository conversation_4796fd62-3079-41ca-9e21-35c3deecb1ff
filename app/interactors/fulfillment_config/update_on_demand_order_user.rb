module FulfillmentConfig
  class UpdateOnDemandOrderUser < BaseFulfillmentInteractor
    include Interactor

    ON_DEMAND_DELIVERY_JOB_TYPES = ["on_demand_ranger", "on_demand_driver"]

    def call
      context.fail!(message: I18n.t("fulfillment_config.on_demand_orders.e.order_number_nil")) unless context.order_number.present?
      context.fail!(message: I18n.t("fulfillment_config.on_demand_orders.e.user_id_nil")) unless context.user_id.present?

      order_number = context.order_number
      response = Service::Fulfillment::OnDemandOrder.update_user(order_number, context.user_id)

      context.response_message = response_message(response, order_number)
      handle_request_error(response)

      context.result = map_response_to_result(response.body)
    rescue Faraday::ClientError => e
      context.fail!(message: "#{ I18n.t("fulfillment_config.e.request_error") } (#{ e.message })")
    end


    private

      def map_response_to_result(body)
        body_order = body["on_demand_order"]
        {
          "number" => body_order["number"],
          "available_users" => body_order["available_users"],
          "delivery_job" => body_order["jobs"].select { |job| ON_DEMAND_DELIVERY_JOB_TYPES.include? job["job_type"] }.first
        }
      end

      def response_message(response, order_number)
        body = response.body
        if response.status != 200
          error_message = body["errors"].present? ?
            "- " + body["errors"].map { |e| e["message"] }.join(". ") :
            ""
          cant_reassign = I18n.t("fulfillment_config.on_demand_orders.info.e.cant_reassign")

          "#{cant_reassign} #{order_number} #{error_message}"
        else
          reassigned = I18n.t("fulfillment_config.on_demand_orders.info.success_reassign")
          "Order #{order_number} #{reassigned}"
        end
      end
  end
end
