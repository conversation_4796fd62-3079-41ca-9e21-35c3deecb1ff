module FulfillmentConfig
  class GetExpressConfigurationByStore < BaseFulfillmentInteractor
    include Interactor

    def call
      response = Service::Fulfillment::ExpressConfiguration.find_by_stock_location_id(context.stock_location_id)
      handle_request_error(response)
      context.result = response.body["express_configurations"]
    rescue Faraday::ClientError => e
      context.fail!(message: "#{ I18n.t("fulfillment_config.e.request_error") } (#{ e.message })", status_code: :bad_gateway)
    end
  end
end
