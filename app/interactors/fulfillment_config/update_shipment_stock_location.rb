module FulfillmentConfig
  class UpdateShipmentStockLocation < BaseFulfillmentInteractor
    include Interactor

    def call
      context.fail!(message: I18n.t("fulfillment_config.manual_order_mutations.e.shipment_number_nil")) unless context.shipment_number.present?
      context.fail!(message: I18n.t("fulfillment_config.manual_order_mutations.e.stock_location_id_nil")) unless context.stock_location_id.present?
      context.fail!(message: I18n.t("fulfillment_config.manual_order_mutations.e.reason_empty")) unless context.reason.present?
      context.fail!(message: I18n.t("fulfillment_config.manual_order_mutations.e.reason_empty")) if context.reason.eql?("others") && context.reason_description.to_s.strip.blank?

      response = Service::Fulfillment::Shipment.change_stock_location(context.shipment_number, context.stock_location_id, context.remove_distance_restriction)

      # handle if service endpoint is unavailable
      if response.status.to_i == 404
        context.fail!(message: fallback_error_message, status_code: :not_found)
      else
        handle_request_error(response)
      end
    # rescue any StandardError (Faraday::Error parent class) may occured from Faraday
    rescue Faraday::Error => error
      context.fail!(message: (error.message.presence || fallback_error_message), status_code: :internal_server_error)
    end

    private

      def fallback_error_message
        @fallback_error_message ||= I18n.t(:request_error, scope: "fulfillment_config.e")
      end
    # end private block
  end
end
