module FulfillmentConfig
  class UpdateOnDemandCluster < BaseFulfillmentInteractor
    include Interactor

    def call
      response = Service::Fulfillment::OnDemandCluster.update(context.params, context.on_demand_cluster_id)
      handle_request_error(response)
      context.result = response.body["on_demand_cluster"]
    rescue Faraday::ClientError => e
      context.fail!(message: "#{ I18n.t("fulfillment_config.e.request_error") } (#{ e.message })", status_code: :bad_gateway)
    rescue ActiveModel::ValidationError => e
      context.errors = e.model.errors.to_h
      context.fail!(message: I18n.t("fulfillment_config.packagings.e.validation_error"), status_code: :unprocessable_entity)
    end
  end
end
