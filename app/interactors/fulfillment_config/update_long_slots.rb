module FulfillmentConfig
  class UpdateLongSlots < BaseFulfillmentStockLocationInteractor
    include Interactor
    include FulfillmentConfig::FulfillmentAuditable

    @@date_time_format = "%Y-%m-%d %H:%M:%S"
    @@time_format = "%H:%M:%S"

    def call
      context.fail!(message: I18n.t("fulfillment_config.long_slots.e.updater_nil"), status_code: :unprocessable_entity) unless context.updater.present?

      validate_params(context.params) do |err|
        context.fail!(message: err.flatten, status_code: :unprocessable_entity) unless err.empty?
      end

      date = context.params[:date]

      context.slots = context.params[:slots].map do |slot|
        create_slot_form_row(slot, date)
      end

      slots_payload = { "slots" => context.slots }

      response = Service::Fulfillment::Slot.update_long_slots(slots_payload)
      handle_request_error(response)

      context.params[:slots].each do |slot|
        create_audit_log user: context.updater, auditable_type: "slot", auditable_id: slot["id"], request_url: request_url
      end
    rescue Faraday::ClientError => e
      context.fail!(message: "#{ I18n.t("fulfillment_config.e.request_error") } (#{ e.message })", status_code: :bad_gateway)
    rescue ArgumentError => e
      context.fail!(message: "#{ I18n.t("fulfillment_config.e.argument_error") }: #{ e.message }", status_code: :internal_server_error)
    end


    private

      def validate_params(params)
        errors = []
        errors.push(I18n.t("fulfillment_config.long_slots.e.date_nil")) unless params[:date].present?
        errors.push(I18n.t("fulfillment_config.long_slots.e.time_zone_nil")) unless params[:time_zone].present?
        if params[:slots].present?
          slots_errors = validate_slots(params[:slots])
          errors.concat(slots_errors)
        else
          errors.push(I18n.t("fulfillment_config.long_slots.e.slots_empty"))
        end

        yield(errors)

        errors.empty?
      end

      def validate_slots(slots)
        errors = []
        # validate uniqueness of start and end
        errors.push(I18n.t("fulfillment_config.long_slots.e.duplicate_start")) unless elements_unique?(slots.map { |s| s["start_time"] })
        errors.push(I18n.t("fulfillment_config.long_slots.e.duplicate_end")) unless elements_unique?(slots.map { |s| s["end_time"] })
        slots.each do |s|
          slot_errors = validate_slot(s)
          errors = errors.concat(slot_errors)
        end
        errors = errors.uniq
      end

      def validate_slot(slot)
        errors = []
        # validate_time_format for start and end
        r = /^([0-1]?[0-9]|2[0-3]):([0-5][0-9])(:[0-5][0-9])?$/
        if !r.match(slot["start_time"]) || !r.match(slot["end_time"])
          errors.push(I18n.t("fulfillment_config.long_slots.e.wrong_time_format"))
        else
          # validate start end chrono order
          start_time = Time.strptime(slot["start_time"], @@time_format)
          end_time = Time.strptime(slot["end_time"], @@time_format)
          errors.push(I18n.t("fulfillment_config.long_slots.e.start_after_end")) if start_time > end_time
        end
        errors
      end

      def elements_unique?(elements)
        elements.count == elements.uniq.count
      end

      def create_slot_form_row(slot, date)
        {
          "id" => slot["id"],
          "start_time" => create_formatted_time(date, slot["start_time"]),
          "end_time" => create_formatted_time(date, slot["end_time"]),
          "open" => slot["open"]
        }
      end

      def create_formatted_time(date, time)
        DateTime.strptime("#{date} #{time}", @@date_time_format)
          .strftime(@@date_time_format)
      end

      def request_url
        "#{ENV["FULFILLMENT_BASE_URL"]}/#{Service::Fulfillment::BaseFulfillmentService.paths[:long_slots][:update]}"
      end
  end
end
