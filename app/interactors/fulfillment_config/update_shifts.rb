module FulfillmentConfig
  class UpdateShifts < BaseFulfillmentStockLocationInteractor
    include Interactor
    include FulfillmentConfig::FulfillmentAuditable

    @@time_format = "%H:%M:%S"
    @@date_time_format = "%Y-%m-%d #{@@time_format}"

    def call
      context.fail!(message: I18n.t("fulfillment_config.shifts.e.updater_nil"), status_code: :unprocessable_entity) unless context.updater.present?

      validate_params(context.params) do |err|
        context.fail!(message: err.flatten, status_code: :unprocessable_entity) unless err.empty?
      end

      time_zone = Time.find_zone(context.params[:time_zone])
      date = context.params[:date]

      shifts = context.params[:shifts].map do |s|
        create_shift_form_row(s, date, time_zone)
      end

      response = Service::Fulfillment::Shift.update({ "shifts" => shifts })
      handle_request_error(response)

      context.params[:shifts].each do |shift|
        create_audit_log user: context.updater, auditable_type: "shift", auditable_id: shift["id"], request_url: request_url
      end
    rescue Faraday::ClientError => e
      context.fail!(message: "#{ I18n.t("fulfillment_config.e.request_error") } (#{ e.message })", status_code: :bad_gateway)
    end


    private

      def validate_params(params)
        errors = []
        errors.push(I18n.t("fulfillment_config.shifts.e.date_nil")) unless params[:date].present?
        errors.push(I18n.t("fulfillment_config.shifts.e.shifts_empty")) unless params[:shifts].present?
        errors.push(I18n.t("fulfillment_config.shifts.e.time_zone_nil")) unless params[:time_zone].present?
        errors.concat(validate_shifts(params[:shifts])) if params[:shifts].present?

        yield(errors)

        errors.empty?
      end

      def validate_shifts(shifts)
        errors = []
        # validate uniqueness of start and end
        errors.push(I18n.t("fulfillment_config.shifts.e.duplicate_start")) unless elements_unique?(shifts.map { |s| s["start_time"] })
        errors.push(I18n.t("fulfillment_config.shifts.e.duplicate_end")) unless elements_unique?(shifts.map { |s| s["end_time"] })
        shifts.each do |s|
          shift_errors = validate_shift(s)
          errors = errors.concat(shift_errors)
        end
        errors = errors.uniq
      end

      def elements_unique?(elements)
        elements.count == elements.uniq.count
      end

      def validate_shift(shift)
        errors = []
        # validate_time_format for start and end
        r = /^([0-1]?[0-9]|2[0-3]):([0-5][0-9])(:[0-5][0-9])?$/
        if !r.match(shift["start_time"]) || !r.match(shift["end_time"])
          errors.push(I18n.t("fulfillment_config.shifts.e.wrong_time_format"))
        else
          # validate start end chrono order
          start_time = Time.strptime(shift["start_time"], @@time_format)
          end_time = Time.strptime(shift["end_time"], @@time_format)
          errors.push(I18n.t("fulfillment_config.shifts.e.start_after_end")) if start_time > end_time
        end
        # validate count > 0
        errors.push(I18n.t("fulfillment_config.shifts.e.not_positive_count")) if shift["count"].to_i <= 0
        errors
      end

      def create_shift_form_row(shift, date, time_zone)
        {
          "id" => shift["id"],
          "start_time" => create_formatted_time(date, shift["start_time"]),
          "end_time" => create_formatted_time(date, shift["end_time"]),
          "count" => shift["count"]
        }
      end

      def create_formatted_time(date, time)
        DateTime.strptime("#{date} #{time}", @@date_time_format)
          .strftime(@@date_time_format)
      end

      def request_url
        "#{ENV["FULFILLMENT_BASE_URL"]}/#{Service::Fulfillment::BaseFulfillmentService.paths[:shifts][:update]}"
      end
  end
end
