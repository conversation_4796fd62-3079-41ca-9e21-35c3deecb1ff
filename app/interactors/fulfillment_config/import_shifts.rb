module FulfillmentConfig
  class ImportShifts < BaseFulfillmentInteractor
    include Interactor

    def call
      context.fail!(message: I18n.t("fulfillment_config.shifts.e.file_nil"), status_code: :unprocessable_entity) unless context.params[:file].present?

      begin
        response = Service::Fulfillment::Shift.upload_shift(context.params[:file])
        handle_request_error(response)
      rescue Faraday::ClientError => e
        context.fail!(message: "#{ I18n.t("fulfillment_config.e.request_error") } (#{ e.message })", status_code: :bad_gateway)
      end
    end
  end
end
