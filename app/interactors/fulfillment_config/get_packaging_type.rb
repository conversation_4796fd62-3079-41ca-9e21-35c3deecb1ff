module FulfillmentConfig
  class GetPackagingType < BaseFulfillmentInteractor
    include Interactor

    def call
      response = Service::Fulfillment::PackagingType.find_by_id(context.country_id, context.id)
      handle_request_error(response)
      context.result = response.body["packaging_type"]
    rescue Faraday::ClientError => e
      context.fail!(message: "#{ I18n.t("fulfillment_config.e.request_error") } (#{ e.message })")
    end
  end
end
