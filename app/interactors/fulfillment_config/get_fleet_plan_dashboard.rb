module FulfillmentConfig
  class GetFleetPlanDashboard
    include <PERSON><PERSON>

    def call
      stock_locations_interactor = ::FulfillmentConfig::GetStockLocationsByCountry.call
      context.fail!(message: stock_locations_interactor.message, status_code: stock_locations_interactor.status_code) if stock_locations_interactor.failure?

      sample_stock_location = stock_locations_interactor.result.find do |s|
        (s["cluster"]["id"] == context.cluster_id.to_i) && s["active"]
      end
      context.fail!(message: I18n.t("fulfillment_config.fleet_plan_dashboard.e.no_stores"), status_code: :internal_server_error) unless sample_stock_location.present?

      sample_external_id = sample_stock_location["external_id"]

      slots_interactor = ::FulfillmentConfig::GetSlotsByStoreV2.call(date: context.date, store_external_id: sample_external_id, show_inactive: false)
      context.fail!(message: slots_interactor.message, status_code: slots_interactor.status_code) if slots_interactor.failure?

      shifts_interactor = ::FulfillmentConfig::GetShiftsByStore.call(date: context.date, store_external_id: sample_external_id, show_inactive: false)
      context.fail!(message: shifts_interactor.message, status_code: shifts_interactor.status_code) if shifts_interactor.failure?

      batches_interactor = ::FulfillmentConfig::GetBatchesByCluster.call(date: context.date, cluster_id: context.cluster_id)
      context.fail!(message: batches_interactor.message, status_code: batches_interactor.status_code) if batches_interactor.failure?

      batches = batches_interactor.result
      store_slots = slots_interactor.result.try(:first).try(:[], "stores") || []
      store_shifts = shifts_interactor.result.try(:first).try(:[], "stores") || []

      context.result = { batches: batches, store_slots: store_slots, store_shifts: store_shifts }
    end
  end
end
