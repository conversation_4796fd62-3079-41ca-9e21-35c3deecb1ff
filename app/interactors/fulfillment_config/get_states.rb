module FulfillmentConfig
  class GetStates < ::FulfillmentConfig::BaseFulfillmentInteractor
    include Interactor

    def call
      response = Service::Fulfillment::State.find_all
      handle_request_error(response)
      context.result = response.body["response"]["states"].select do |state|
        state["country"]["iso_name"].upcase == Apartment::Tenant.current.upcase
      end
    rescue Faraday::ConnectionFailed => e
      context.fail!(message: "#{ I18n.t("fulfillment_config.e.request_error") } (#{ e.message })", status_code: :bad_gateway)
    end
  end
end
