module FulfillmentConfig
  class GetStockLocationsByCountry < ::FulfillmentConfig::BaseFulfillmentInteractor
    include Interactor

    def call
      response = Service::Fulfillment::StockLocation.find_by_country(Apartment::Tenant.current.upcase)
      handle_request_error(response)
      context.result = response.body["response"]["stock_locations"]
    rescue Faraday::ConnectionFailed => e
      context.fail!(message: "#{ I18n.t("fulfillment_config.e.request_error") } (#{ e.message })", status_code: :bad_gateway)
    end
  end
end
