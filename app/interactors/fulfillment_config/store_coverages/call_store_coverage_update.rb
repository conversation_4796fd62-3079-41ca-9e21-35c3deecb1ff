module FulfillmentConfig
  class CallStoreCoverageUpdate
    include Interactor

    E_PREFIX = ".fulfillment_config.store_coverages.e"

    def call
      context.fail!(message: I18n.t("#{E_PREFIX}.coverages_payload_nil")) unless context.payload.present?

      results = []
      errors = []
      context.payload.each do |store_payload|
        result = Service::Fulfillment::StoreCoverage.create_or_update_stock_location_coverage(store_payload)
        results.push result
      rescue GRPC::BadStatus => e
        errors.push e
      end

      context.messages = errors.map { |e| { message: e.message.split(",")[0] } }
      context.fail! if errors.count == context.payload.count

      context.results = results
      context.errors = errors
    end
  end
end
