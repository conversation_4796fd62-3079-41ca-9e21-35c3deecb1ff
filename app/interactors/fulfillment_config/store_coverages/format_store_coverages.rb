module FulfillmentConfig
  class FormatStoreCoverages
    include Interactor

    E_PREFIX = ".fulfillment_config.store_coverages.e"

    def call
      context.fail!(message: I18n.t("#{E_PREFIX}.coverages_form_nil")) if context.coverages_form.blank?

      context.store_coverages = format_for_validation(context.coverages_form)
      context.payload = format_for_payload(context.coverages_form)
    rescue TypeError, ArgumentError => e
      context.fail!(message: e.message)
    end


    private

      def format_for_validation(form)
        form.map do |entry|
          { id: Integer(entry["id"]),
            center: geopoint_as_array(entry["center"]),
            coverage_polygon: entry["polygon"].map { |point| geopoint_as_array(point) } }
        end
      end

      def format_for_payload(form)
        form.map do |entry|
          { id: Integer(entry["id"]),
            name: entry["name"],
            coverage_polygon: { points: entry["polygon"].map { |point| geopoint_as_object(point) } } }
        end
      end

      def geopoint_as_array(point)
        [Float(point["lat"]), Float(point["lon"])]
      end

      def geopoint_as_object(point)
        { lat: Float(point["lat"]), lon: Float(point["lon"]) }
      end
  end
end
