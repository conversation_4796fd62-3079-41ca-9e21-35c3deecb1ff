
module FulfillmentConfig
  class GetStockLocationCoverage < ::FulfillmentConfig::BaseFulfillmentInteractor
    include Interactor

    def call
      context.fail!(message: I18n.t(".fulfillment_config.store_coverages.e.external_id_nil")) unless context.external_id.present?

      id = context.external_id
      coverage = Service::Fulfillment::StoreCoverage.get_stock_location_coverage(id)

      context.result = format_geopoints(coverage.to_h)
    rescue GRPC::BadStatus => e
      context.fail!(message: "#{ I18n.t("fulfillment_config.e.scs_request_error") } (#{ e.message.split(". ")[0] })")
    end


    private

      def format_geopoints(coverage)
        coverage[:coverage_polygon][:points] = coverage[:coverage_polygon][:points].map { |p| [p[:lat], p[:lon]] }
        coverage
      end
  end
end
