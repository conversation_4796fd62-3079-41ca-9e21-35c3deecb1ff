module FulfillmentConfig
  class ReorderPackagingSequence < BaseFulfillmentInteractor
    include Interactor

    def call
      initial_sequence = context.initial_sequence.split(",")
      final_sequence = context.final_sequence.split(",")

      if initial_sequence.size != final_sequence.size
        context.fail!(message: I18n.t("fulfillment_config.e.invalid_sequence"))
      end

      final_sequence.each_with_index do |packaging_id, index|
        next if final_sequence[index] == initial_sequence[index]

        packaging_ctx = GetPackaging.call(
          id: packaging_id,
          stock_location_id: context.stock_location_id
        )
        context.fail!(message: I18n.t("fulfillment_config.e.invalid_id")) if packaging_ctx.fail?
        packaging = packaging_ctx.result

        update_ctx = UpdatePackaging.call(
          stock_location_id: context.stock_location_id,
          id: packaging_id,
          params: {
            "packaging_type_id" => packaging.packaging_type_id,
            "price" => packaging.price,
            "max_quantity" => packaging.max_quantity,
            "display_sequence" => (index + 1)
          }
        )
        if update_ctx.failure?
          context.fail!(message: "#{ I18n.t("fulfillment_config.e.request_error") } (#{ update_ctx.message })")
        end
      end
    end
  end
end
