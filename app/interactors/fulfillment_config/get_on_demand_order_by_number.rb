
module FulfillmentConfig
  class GetOnDemandOrderByNumber < BaseFulfillmentInteractor
    include Interactor

    ON_DEMAND_DELIVERY_JOB_TYPES = ["on_demand_ranger", "on_demand_driver"]

    def call
      response = Service::Fulfillment::OnDemandOrder.find_by_number(context.order_number)

      context.error_messages = error_messages(response.body)
      handle_request_error(response)

      context.result = map_response_to_result(response.body)
    rescue Faraday::ClientError => e
      context.fail!(message: "#{ I18n.t("fulfillment_config.e.request_error") } (#{ e.message })")
    end


    private

      def map_response_to_result(body)
        body_order = body["on_demand_order"]
        {
          "number" => body_order["number"],
          "available_users" => body_order["available_users"],
          "delivery_job" => body_order["jobs"].select { |job| ON_DEMAND_DELIVERY_JOB_TYPES.include? job["job_type"] }.first
        }
      end

      def error_messages(body)
        if body["errors"].present?
          body["errors"].map { |e| e["message"] }
        end
      end
  end
end
