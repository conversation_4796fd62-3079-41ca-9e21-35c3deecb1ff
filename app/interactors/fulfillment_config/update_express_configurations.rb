module FulfillmentConfig
  class UpdateExpressConfigurations < BaseFulfillmentInteractor
    include Interactor

    def call
      response = Service::Fulfillment::ExpressConfiguration.update(
        context.stock_location_id,
        context.params
      )
      handle_request_error(response)
      context.result = response.body["express_configurations"]

    rescue Faraday::ClientError => e
      context.fail!(message: "#{ I18n.t("fulfillment_config.e.request_error") } (#{ e.message })", status_code: :bad_gateway)
    rescue ActiveModel::ValidationError => e
      context.errors = e.model.errors.to_h
      context.fail!(message: I18n.t("fulfillment_config.express_confgurations.e.validation_error"), status_code: :unprocessable_entity)
    end
  end
end
