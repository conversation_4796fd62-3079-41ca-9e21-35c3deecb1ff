module FulfillmentConfig
  class UpdateCountry < BaseFulfillmentInteractor
    include Interactor

    def call
      response = Service::Fulfillment::Country.update(context.iso_name, context.country)
      handle_request_error(response)
      context.result = response.body["country"]

    rescue Faraday::ClientError => e
      context.fail!(message: "#{ I18n.t("fulfillment_config.e.request_error") } (#{ e.message })")
    rescue ActiveModel::ValidationError => e
      context.errors = e.model.errors.to_h
      context.fail!(message: I18n.t("fulfillment_config.countries.e.validation_error"))
    end
  end
end
