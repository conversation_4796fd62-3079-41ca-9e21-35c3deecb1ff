module FulfillmentConfig
  class UpdatePackaging < BaseFulfillmentInteractor
    include Interactor

    def call
      response = Service::Fulfillment::Packaging.update(
        context.params,
        context.stock_location_id,
        context.id
      )
      handle_request_error(response)
      context.result = response.body["packaging"]

    rescue Faraday::ClientError => e
      context.fail!(message: "#{ I18n.t("fulfillment_config.e.request_error") } (#{ e.message })")
    rescue ActiveModel::ValidationError => e
      context.errors = e.model.errors.to_h
      context.fail!(message: I18n.t("fulfillment_config.packagings.e.validation_error"))
    end
  end
end
