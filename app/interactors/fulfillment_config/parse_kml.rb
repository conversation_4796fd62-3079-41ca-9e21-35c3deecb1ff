
module FulfillmentConfig
  class ParseKml < ::FulfillmentConfig::BaseFulfillmentInteractor
    include Interactor

    def call
      context.fail!(message: I18n.t(".fulfillment_config.store_coverages.e.kml_file_nil")) if context.kml_file.blank?

      doc = Nokogiri::XML(context.kml_file)

      store_coverages = {}

      # stock_location_id stored in "Placemark > description"
      ids = doc.css("Placemark description").map { |d| d.text.strip }.uniq
      ids.each do |id|
        store_coverages[id] = {
          id: id,
          name: nil,
          center: nil,
          coverage_polygon: nil
        }
      end

      doc.css("Placemark").each do |placemark|
        id = placemark.at_css("description")&.content.strip

        store_coverages[id][:name] = placemark.at_css("name")&.content

        center_str = placemark.at_css("Point coordinates")&.content
        if center_str.present?
          (lon, lat, alt) = center_str.split(",")
          store_coverages[id][:center] = [lat.to_f, lon.to_f]
        end

        polygon_str = placemark.at_css("Polygon coordinates")&.content
        if polygon_str.present?
          polygon = polygon_str.split(" ").map do |point_str|
            (lon, lat, alt) = point_str.split(",")
            [lat.to_f, lon.to_f]
          end
          store_coverages[id][:coverage_polygon] = polygon
        end
      end

      context.store_coverages = store_coverages.values.map(&:stringify_keys)
      # result structure:
      # [
      #   {
      #     "id" => 115,
      #     "name" => "Farmers Market One Belpark Fatmawati",
      #     "center" => [-6.3044485, 106.7928141],  # [lat, lon]
      #     "coverage_polygon" => [
      #       [-6.3044485, 106.84704095762459],   # [lat, lon]
      #       ...,
      #       [-6.3044485, 106.84704095762459]
      #     ]
      #   },
      #   ...
      # ]
    rescue => e
      context.fail!(message: e.message)
    end
  end
end
