module FulfillmentConfig
  class ImportSlots < BaseFulfillmentInteractor
    include <PERSON><PERSON>

    def call
      context.fail!(message: I18n.t("fulfillment_config.slots.e.file_nil")) unless context.params[:file].present?
      context.fail!(message: I18n.t("fulfillment_config.slots.e.updater_nil")) unless context.updater.present?

      ActiveRecord::Base.transaction do
        # Get store IDs
        store_external_ids = []
        CSV.foreach(context.params[:file].path, headers: true).with_index do |row, i|
          row_hash = row.to_h.transform_keys { |k| k.to_s.downcase }
          row_hash.each do |col, value|
            if col == "store id" && value.to_s.strip.present?
              store_external_ids << value
            end
          end
        end

        response = Service::Fulfillment::Slot.upload_slot(context.params[:file])
        handle_request_error(response)

        context.model = SlotLog.create(
          created_by: context.updater.id,
          file: context.params[:file],
          original_filename: context.params[:file].original_filename,
          store_external_ids: store_external_ids.join(",")
        )
      rescue Faraday::ClientError => e
        context.fail!(message: "#{ I18n.t("fulfillment_config.e.request_error") } (#{ e.message })")
      rescue CSV::MalformedCSVError
        context.fail!(message: I18n.t("fulfillment_config.slots.e.invalid_file"))
      rescue ArgumentError
        context.fail!(message: I18n.t("fulfillment_config.slots.e.invalid_file"))
      end
    end
  end
end
