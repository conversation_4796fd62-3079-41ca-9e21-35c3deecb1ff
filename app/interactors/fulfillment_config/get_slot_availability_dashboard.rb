module FulfillmentConfig
  class GetSlotAvailabilityDashboard < ::FulfillmentConfig::BaseFulfillmentInteractor
    include Interactor

    def call
      result_map = {}

      all_stores = get_stock_locations()
      clusters = get_clusters()

      result_map["clusters"] = clusters

      clusters.each_with_index do |cluster, index|
        cluster_stores = all_stores.select do |store|
          (cluster["id"] == store["cluster"]["id"]) && store["active"]
        end

        # Slots statistics
        if cluster["slot_type"] == "LONGER_DELIVERY"
          cluster_slots = get_cluster_slots(cluster_stores)
          cluster_shifts = get_cluster_shifts(cluster_stores)
          cluster_slots = calculate_shift_counts(cluster_slots, cluster_shifts)

          cluster_batches = get_cluster_batches(cluster["id"])
          slot_batch_durations = calculate_slot_batch_durations(cluster_batches)

          cluster_slots = cluster_slots.map do |slot|
            slot.merge(slot_batch_durations[slot["id"]] || { "shopper_duration" => 0, "driver_duration" => 0 })
          end
        else
          cluster_slots = get_cluster_slots(cluster_stores)
          ## Driver by cluster slots non-dds
          driver_cluster_slots = get_driver_cluster_slots(cluster_slots)
        end

        ## Slot availability
        stores = cluster_stores.map do |store|
          slots_availability = get_slot_availability(store)
            .map { |s| s.slice("id", "available") }
            .reduce({})  { |acc, s| acc[s["id"]] = s; acc }

          slots = cluster_slots
            .select { |slot| slot["store_id"] == store["id"] }
            .map { |slot| slot.merge(slots_availability[slot["id"]] || {}) }
            .map { |slot| slot.merge(set_availability_state(slot, store)) }
          store
            .merge({ "time_zone" => store["state"]["time_zone"] })
            .slice("id", "name", "type", "code", "external_id")
            .merge({ "slots" => slots })
        end

        ## Final data shape per cluster
        clusters[index] = clusters[index].merge({ "stores" => stores, "driver_cluster_slots" => driver_cluster_slots })
      end

      context.result = result_map
    end


    private

      HOUR_MULTIPLIER = 24
      MINUTE_MULTIPLIER = 24 * 60

      def get_stock_locations
        stock_locations_interactor = ::FulfillmentConfig::GetStockLocationsByCountry.call
        context.fail!(message: stock_locations_interactor.message) if stock_locations_interactor.failure?

        stock_locations_interactor.result
      end

      def get_clusters
        clusters_interactor = ::FulfillmentConfig::GetClustersByCountry.call
        context.fail!(message: stock_locations_interactor.message) if clusters_interactor.failure?

        clusters = clusters_interactor.result.select { |cluster| (context.cluster_ids.include? cluster["id"]) }
        clusters.map { |c| c.slice("id", "name", "slot_type") }
      end

      def get_slot_availability(store)
        shipment = Service::Fulfillment::DummyShipment.create(
          store,
          context.distance_km,
          context.line_item_count,
          context.total_volume)

        response = Service::Fulfillment::Slot.available(shipment, context.date)
        handle_request_error(response)
        response.body["slots"]
      rescue Faraday::ConnectionFailed => e
        context.fail!(message: "#{ I18n.t("fulfillment_config.e.request_error") + ". Store ID: #{store["id"]}." } (#{ e.message })")
      end

      def get_cluster_slots(stores)
        return [] if !(stores.present?)
        sample_store_id = stores.first["external_id"]
        response = Service::Fulfillment::Slot.find_by_store_and_date_v2(sample_store_id, context.date)
        handle_request_error(response)

        response.body["response"]["slots"].map do |slot|
          slot
            .slice("id", "slot_type", "start_time", "end_time", "shopper_count", "driver_count", "shared_driver_in_cluster", "shipment_count")
            .merge({
              "store_id" => slot["stock_location"]["id"],
              "duration_hours" => get_duration(slot["start_time"], slot["end_time"], HOUR_MULTIPLIER) })
        end
      rescue Faraday::ConnectionFailed => e
        context.fail!(message: "#{ I18n.t("fulfillment_config.e.request_error") + ". Store ID: #{sample_store_id}." } (#{ e.message })")
      end

      def get_cluster_shifts(stores)
        return [] if !(stores.present?)
        sample_store_id = stores.first["external_id"]
        response = Service::Fulfillment::Shift.find_by_store_and_date(sample_store_id, context.date)
        handle_request_error(response)

        response.body["response"]["shifts"].map do |shift|
          shift
            .slice("id", "type", "start_time", "end_time", "count")
            .merge({ "store_id" => shift["stock_location"]["id"] })
        end
      rescue Faraday::ConnectionFailed => e
        context.fail!(message: "#{ I18n.t("fulfillment_config.e.request_error") + ". Store ID: #{sample_store_id}." } (#{ e.message })")
      end

      def get_cluster_batches(cluster_id)
        response = Service::Fulfillment::Batch.find_by_cluster_and_date(cluster_id, context.date)
        handle_request_error(response)

        result = response.body["batches"].map do |batch|
          { "type" => batch["type"],
            "slot_id" => batch["slot"]["id"],
            "start" => batch["start"],
            "end" => batch["end"],
            "duration_minutes" => get_duration(batch["start"], batch["end"], MINUTE_MULTIPLIER),
            "store_id" => batch["stock_location"]["id"] }
        end
        result
      rescue Faraday::ConnectionFailed => e
        context.fail!(message: "#{ I18n.t("fulfillment_config.e.request_error") } (#{ e.message })")
      end

      def get_driver_cluster_slots(cluster_slots)
        result = {}
        cluster_slots.each do |slot|
          key = "#{slot["start_time"]}-#{slot["end_time"]}"
          if !result[key]
            result[key] = {
              "start_time" => slot["start_time"],
              "end_time" => slot["end_time"],
              "total_count" => 0,
              "total_shipment" => 0,
              "duration_hours" => slot["duration_hours"]
            }
          end
          result[key]["total_count"] += [0, slot["driver_count"]].max
          result[key]["total_shipment"] += slot["shipment_count"]
        end
        result = result.values
      end

      def to_datetime(datetime_string)
        DateTime.parse(datetime_string)
      end

      def calculate_shift_counts(slots, shifts)
        slots.each do |slot|
          shifts.each do |shift|
            if within_range(slot, shift)
              add_shopper_driver_shift_counts(slot, shift)
            end
          end
        end

        slots
      end

      def within_range(slot, shift)
        slot_start = to_datetime(slot["start_time"])
        shift_start = to_datetime(shift["start_time"])
        shift_end = to_datetime(shift["end_time"])

        slot_start >= shift_start && slot_start < shift_end
      end

      def get_duration(start_time, end_time, multiplier)
        ((DateTime.parse(end_time) - DateTime.parse(start_time)) * multiplier).to_f
      end

      def add_shopper_driver_shift_counts(slot, shift)
        if !slot["shopper_shift_count"].present?
          slot["shopper_shift_count"] = 0
        end
        if !slot["driver_shift_count"].present?
          slot["driver_shift_count"] = 0
        end

        case shift["type"]
        when "driver"
          slot["driver_shift_count"] += shift["count"]
        when "shopper"
          slot["shopper_shift_count"] += shift["count"] if slot["store_id"] == shift["store_id"]
        end
      end

      def calculate_slot_batch_durations(batches)
        batches.group_by { |batch| batch["slot_id"] }
          .map do |slot_id, slot_batches|
            [ slot_id,
              slot_batches.reduce({ "shopper_duration" => 0, "driver_duration" => 0 }) do |acc, batch|
                case batch["type"]
                when "SHOPPING"
                  acc["shopper_duration"] += batch["duration_minutes"]
                when "DELIVERY"
                  acc["driver_duration"] += batch["duration_minutes"]
                when "RANGER"
                  acc["driver_duration"] += batch["duration_minutes"]
                end
                acc
              end
            ]
          end
          .to_h
      end

      def set_availability_state(slot, store)
        available = slot["available"]
        slot_availability_state = {}
        if available.nil?
          time_zone = store["state"]["time_zone"] || "UTC"
          now_at_zone = Time.use_zone(time_zone) { Time.current }
          start_time = Time.use_zone(time_zone) { Time.zone.parse(slot["start_time"]) }
          availability_state = now_at_zone > start_time ? "past" : "no data"
          slot_availability_state = { "availability_state" => availability_state }
        else
          if (store["type"] == "original" && (slot["shopper_count"] < 0 || slot["driver_count"] == 0)) ||
             (store["type"] == "special" && slot["driver_count"] < 0)
            slot_availability_state = { "availability_state" => "slot closed" }
          else
            slot_availability_state = { "availability_state" => slot["available"] ? "available" : "full" }
          end
        end
        slot_availability_state
      end
  end
end
