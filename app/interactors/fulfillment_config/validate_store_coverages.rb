
module FulfillmentConfig
  class ValidateStoreCoverages < ::FulfillmentConfig::BaseFulfillmentInteractor
    include Interactor
    include FulfillmentCacheHelper

    E_PREFIX = ".fulfillment_config.store_coverages.e"

    def call
      context.fail!(message: I18n.t("#{E_PREFIX}.store_coverages_nil")) if context.store_coverages.blank?
      context.store_coverages = context.store_coverages.map(&:symbolize_keys)

      context.store_coverages.each do |sc|
        validate_center(sc)
        validate_polygon(sc)
      end
      validate_store_ids
    end

    private

      def validate_center(store_coverage)
        center = store_coverage[:center]
        context.fail!(message: I18n.t("#{E_PREFIX}.center_nil", store: store_coverage[:name])) if center.blank?

        lat = center[0]
        lon = center[1]
        unless (lat >= -90.0000000000000 && lat <= 90.0000000000000) && (lon >= -180.0000000000000 && lon <= 180.0000000000000)
          context.fail!(message: I18n.t("#{E_PREFIX}.invalid_center", store: store_coverage[:name]))
        end
      end

      def validate_polygon(store_coverage)
        polygon = store_coverage[:coverage_polygon]
        context.fail!(message: I18n.t("#{E_PREFIX}.polygon_nil", store: store_coverage[:name])) if polygon.blank?

        polygon.each do |point|
          lat = point[0]
          lon = point[1]
          unless (lat >= -90.0000000000000 && lat <= 90.0000000000000) && (lon >= -180.0000000000000 && lon <= 180.0000000000000)
            context.fail!(message: I18n.t("#{E_PREFIX}.invalid_polygon", store: store_coverage[:name]))
          end
        end
      end

      def validate_store_ids
        ids = context.store_coverages.map { |sc| sc[:id].to_s }
        existing_ids = get_stock_locations.map { |sl| sl[:external_id].to_s }
        unknown_ids = ids - existing_ids

        if unknown_ids.present?
          unknown_names = context.store_coverages.select { |sc| unknown_ids.include? sc[:id] }
            .map { |sc| "#{sc[:name]} - #{sc[:id]}" }
            .join(", ")
          context.store_coverages = context.store_coverages.reject { |sc| unknown_ids.include? sc[:id] }

          context.fail!(message: I18n.t("#{E_PREFIX}.invalid_stores", names: unknown_names))
        end
      end

      def get_stock_locations
        stock_locations = get_stock_locations_by_country!
        stock_locations.map(&:symbolize_keys)
      rescue => e
        context.fail!(message: e.message)
      end
  end
end
