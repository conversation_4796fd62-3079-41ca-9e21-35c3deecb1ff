module FulfillmentConfig
  class GetOnDemandClustersByCountry < ::FulfillmentConfig::BaseFulfillmentInteractor
    include Interactor

    def call
      response = Service::Fulfillment::OnDemandCluster.find_by_country(Apartment::Tenant.current.upcase)
      handle_request_error(response)
      context.result = response.body["response"]["on_demand_clusters"]
    rescue Faraday::ConnectionFailed => e
      context.fail!(message: "#{ I18n.t("fulfillment_config.e.request_error") } (#{ e.message })", status_code: :bad_gateway)
    end
  end
end
