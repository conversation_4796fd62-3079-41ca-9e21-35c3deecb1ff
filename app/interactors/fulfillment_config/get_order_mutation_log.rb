module FulfillmentConfig
  class GetOrderMutationLog
    include Interactor

    def call
      query = ManualOrderMutationLog.recent

      if context.order_number.to_s.strip.present?
        query = query.where(order_number: context.order_number)
      end

      start_date = context.start_date.presence || DateTime.current.beginning_of_day - 6.months
      end_date = context.end_date.presence || DateTime.current.end_of_day

      context.logs = query.between_date(start_date, end_date)
    end
  end
end
