image: public.ecr.aws/j9e7a9t4/coramil/pipeline-builder:ruby263-0.0.2

variables:
  DOCKER_HOST: tcp://docker:2375
  DOCKER_TLS_CERTDIR: ""
  DOCKER_DRIVER: overlay2

stages:
  - deploy
  - deploy-k8s

cache:
  paths:
    - /usr/local/bundle
    - packages.zip

1-build_staging:
  stage: deploy
  environment:
    name: staging
  tags:
    - docker_runner
  services:
    - docker:20.10.16-dind
  before_script:
    - apt update --allow-releaseinfo-change-suite && apt install docker.io apt-transport-https ca-certificates -y
    - update-ca-certificates
  extends:
    - .ex_staging_rule
  script:
    - export VERSION=$(cat ./VERSION)
    - source /builder/scripts/ecs-deploy-lib.sh
    - login_aws ${AWS_ACCESS_KEY_ID} ${AWS_SECRET_ACCESS_KEY}
    - login_docker_to_aws_ecr ${ECS_REGION} ${CONTAINER_REPOSITORY_URL}
    - CACHE_IMAGE="${CONTAINER_REPOSITORY_URL}/${DOCKER_IMAGE_URL}:latest"
    - FULL_IMAGE_REPO_NAME="${CONTAINER_REPOSITORY_URL}/${DOCKER_IMAGE_URL}:build-${CI_COMMIT_SHORT_SHA}"
    - docker build -t ${DOCKER_IMAGE_URL} . --build-arg GIT_SSL_NO_VERIFY="true" --build-arg CI_JOB_TOKEN="${CI_JOB_TOKEN}" --build-arg VERSION="${VERSION}" --build-arg CACHE_IMAGE=${CACHE_IMAGE}
    - docker_tag_and_push ${DOCKER_IMAGE_URL} "${CONTAINER_REPOSITORY_URL}/${DOCKER_IMAGE_URL}:latest"
    - docker_tag_and_push ${DOCKER_IMAGE_URL} ${FULL_IMAGE_REPO_NAME}
  artifacts:
    paths:
      - trivy-*

1-1-k8s-deploy-staging:
  stage: deploy-k8s
  image: public.ecr.aws/docker/library/alpine:latest
  environment:
    name: staging
  tags:
    - docker_runner
  extends: 
    - .ex_staging_rule
    - .k8s_setup_context
  needs:
    - 1-build_staging
  script:
    - DEPLOYMENT_NAME=$(echo ${AWS_SERVICE_NAME} | tr '[:upper:]' '[:lower:]')
    - CONTAINER_NAME="fulfillmentservice-fms"
    - NAMESPACE="default"
    - IMAGE_TAG="${CONTAINER_REPOSITORY_URL}/${DOCKER_IMAGE_URL}:build-${CI_COMMIT_SHORT_SHA}"
    - kubectl set image deployment/$DEPLOYMENT_NAME $CONTAINER_NAME=$IMAGE_TAG -n $NAMESPACE
    - echo "Deployment $DEPLOYMENT_NAME image updated to $IMAGE_TAG in namespace $NAMESPACE"

2-build_production:
  stage: deploy
  environment:
    name: production
  tags:
    - docker_runner
  services:
    - docker:20.10.16-dind
  before_script:
    - apt update --allow-releaseinfo-change-suite && apt install docker.io apt-transport-https ca-certificates -y
    - update-ca-certificates
  rules:
    - when: manual
  script:
    - export VERSION=$(cat ./VERSION)
    - source /builder/scripts/ecs-deploy-lib.sh
    - login_aws ${AWS_ACCESS_KEY_ID} ${AWS_SECRET_ACCESS_KEY}
    - login_docker_to_aws_ecr ${ECS_REGION} ${CONTAINER_REPOSITORY_URL}
    - FULL_IMAGE_REPO_NAME="${CONTAINER_REPOSITORY_URL}/${DOCKER_IMAGE_URL}:build-${CI_COMMIT_SHORT_SHA}"
    - docker build -t ${DOCKER_IMAGE_URL} . --build-arg GIT_SSL_NO_VERIFY="true" --build-arg CI_JOB_TOKEN="${CI_JOB_TOKEN}" --build-arg VERSION="${VERSION}"
    - docker_tag_and_push ${DOCKER_IMAGE_URL} "${CONTAINER_REPOSITORY_URL}/${DOCKER_IMAGE_URL}:latest"
    - docker_tag_and_push ${DOCKER_IMAGE_URL} ${FULL_IMAGE_REPO_NAME}

2-1-k8s-deploy-production:
  stage: deploy-k8s
  image: public.ecr.aws/docker/library/alpine:latest
  environment:
    name: production
  tags:
    - docker_runner
  rules:
    - when: manual
  extends:
    - .k8s_setup_context
  needs:
    - 2-build_production
  script:
    - DEPLOYMENT_NAME=$(echo ${AWS_SERVICE_NAME} | tr '[:upper:]' '[:lower:]')
    - CONTAINER_NAME="fulfillmentservice-fms"
    - NAMESPACE="production"
    - IMAGE_TAG="${CONTAINER_REPOSITORY_URL}/${DOCKER_IMAGE_URL}:build-${CI_COMMIT_SHORT_SHA}"
    - kubectl set image deployment/$DEPLOYMENT_NAME $CONTAINER_NAME=$IMAGE_TAG -n $NAMESPACE
    - echo "Deployment $DEPLOYMENT_NAME image updated to $IMAGE_TAG in namespace $NAMESPACE"

.ex_staging_rule:
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      when: never
    - if: $CI_COMMIT_BRANCH == "stage" || $CI_COMMIT_BRANCH =~ /^stage-.*/ || $CI_COMMIT_BRANCH == "staging" || $CI_COMMIT_BRANCH =~ /^staging-.*/ # run for staging branches
    - if: $CI_COMMIT_BRANCH == "master"
      when: manual
    - when: manual

.k8s_setup_context:
  before_script:
    # Check if kubectl installed else then download
    - if ! command -v kubectl &> /dev/null; then
        echo "kubectl not found, installing...";
        wget "https://dl.k8s.io/release/$(wget -qO- https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl";
        chmod +x ./kubectl;
        mv ./kubectl /usr/local/bin/kubectl;
      fi
    # Set environment-specific variable names
    - |
      if [ "$CI_ENVIRONMENT_NAME" = "production" ]; then
        K8S_CA_CRT_BASE64="$PRODUCTION_K8S_CA_CRT_BASE64"
        K8S_CLUSTER_NAME="$PRODUCTION_K8S_CLUSTER_NAME"
        K8S_SERVER="$PRODUCTION_K8S_SERVER"
        K8S_TOKEN="$PRODUCTION_K8S_TOKEN"
      else
        # Use staging variables for all other environments (staging, dev, etc.)
        K8S_CA_CRT_BASE64="$STAGING_K8S_CA_CRT_BASE64"
        K8S_CLUSTER_NAME="$STAGING_K8S_CLUSTER_NAME"
        K8S_SERVER="$STAGING_K8S_SERVER"
        K8S_TOKEN="$STAGING_K8S_TOKEN"
      fi
    - echo "$K8S_CA_CRT_BASE64" | base64 -d > ca.crt
    - kubectl config set-cluster $K8S_CLUSTER_NAME --server=$K8S_SERVER --certificate-authority=ca.crt
    - kubectl config set-credentials $K8S_CLUSTER_NAME --token=$K8S_TOKEN
    - kubectl config set-context $K8S_CLUSTER_NAME --cluster=$K8S_CLUSTER_NAME --user=$K8S_CLUSTER_NAME
    - kubectl config use-context $K8S_CLUSTER_NAME
